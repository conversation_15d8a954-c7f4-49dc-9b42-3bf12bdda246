package main

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type OldResourceContextInsertDoc struct {
	UserAgent []string `json:"userAgent"`
}

func UpdateUsrAgentObj() {

	resourcesQuery := `{"query":{"bool":{"must":[{"wildcard":{"userAgent.keyword":"*"}}]}},"size":10,"from":0}`
	var (
		searchAfter any
	)

	for {

		resourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, resourcesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(resourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		logger.Print(logger.INFO, "Resources fetched", len(resourcesDocs))

		var bulkUpdate strings.Builder

		for resourcesDocID, rctxDoc := range resourcesDocs {

			jsonBytes, err := json.Marshal(rctxDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling resource context doc", resourcesDocID, err)
				continue
			}

			var oldDoc OldResourceContextInsertDoc
			if err := json.Unmarshal(jsonBytes, &oldDoc); err != nil {
				logger.Print(logger.INFO, "Skipping doc (already migrated or invalid)", resourcesDocID)
				continue
			}

			if len(oldDoc.UserAgent) == 0 {
				continue
			}

			ua := make([]common.UserAgent, 0, len(oldDoc.UserAgent))
			for _, name := range oldDoc.UserAgent {
				name = strings.TrimSpace(name)
				if len(name) == 0 {
					continue
				}
				ua = append(ua, common.UserAgent{
					Name: name,
					Type: contextutils.IAC_USERAGENT_TYPE,
				})
			}

			if len(ua) == 0 {
				continue
			}

			bulkUpdate.WriteString(fmt.Sprintf(`{"update":{"_id":"%s"}}`, resourcesDocID))
			bulkUpdate.WriteString("\n")

			updateDoc := map[string]any{"doc": map[string]any{
				"userAgents": ua,
			}}

			if docBytes, er := json.Marshal(updateDoc); er == nil {
				bulkUpdate.Write(docBytes)
				bulkUpdate.WriteString("\n")
			} else {
				logger.Print(logger.ERROR, "Failed marshalling update doc for", resourcesDocID, er)
			}
		}

		if bulkUpdate.Len() > 0 {
			if err := elastic.BulkDocumentsAPI("", elastic.RESOURCE_CONTEXT_INDEX, bulkUpdate.String()); err != nil {
				logger.Print(logger.ERROR, "Bulk update failed (final flush)", err)
				return
			}
			logger.Print(logger.INFO, "Bulk updated final batch", "done")
		}
	}

	logger.Print(logger.INFO, "Migration complete")
}
