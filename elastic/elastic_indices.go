package elastic

const (
	IAC_GIT_COMMITS_INDEX          = "iac_git_commits"
	CFSTACK_TEMPLATES_INDEX        = "cfstack_templates"
	ARM_TEMPLATES_INDEX            = "arm_templates"
	TERRAFORM_RESOURCES_INDEX      = "terraform_resources"
	TFCLOUD_TFSTATEVERSIONS_INDEX  = "tfcloud_tfstateversions"
	GITLAB_TFSTATEVERSIONS_INDEX   = "gitlab_tfstateversions"
	RESOURCE_USER_EVENTS_INDEX     = "resource_user_events"
	CLOUD_RESOURCES_INDEX          = "cloud_resources"
	CLOUD_RESOURCE_STORE_INDEX     = "cloud_resource_store"
	RESOURCE_CONTEXT_INDEX         = "resource_context"
	IDENTITIES_INDEX               = "identities"
	TF_COMMITS_INDEX               = "tf_commits"
	USER_EMAIL_DETAILS_INDEX       = "user_email_details"
	TEXT_LOOKUP_INDEX              = "text_lookup"
	TF_VARIABLES_INDEX             = "tf_variables"
	COMPANY_METADATA_INDEX         = "company_metadata"
	JIRA_ISSUES_INDEX              = "jira_issues"
	IDP_EVENTS_INDEX               = "idp_events"
	IDP_USERS_INDEX                = "idp_users"
	IDP_APPS_INDEX                 = "idp_apps"
	IDP_GROUPS_INDEX               = "idp_groups"
	CLOUD_INCIDENTS_INDEX          = "cloud_incidents"
	GENERIC_MAPPING_INDEX          = "generic_mapping"
	HERO_STATS_INDEX               = "stats"
	AI_RESOURCES_INDEX             = "ai_resources"
	PRECIZE_CREATIONS_INDEX        = "precize_creations"
	CUST_ENTITY_CONTEXT_INDEX      = "cust_entity_context"
	JIRA_DATA_INDEX                = "jira_data"
	CLOUD_ASSET_COSTS_INDEX        = "cloud_asset_costs"
	CLOUD_IDENTITY_INDEX           = "cloud_identity"
	JIRA_RESOURCES_INDEX           = "jira_resources"
	CLOUD_ACTIVITY_INDEX           = "cloud_activity"
	EXTERNAL_CLOUD_RESOURCES_INDEX = "external_cloud_resources"
	SCANS_INDEX                    = "scans"
	CONTEXT_NEIGHBOURS_INDEX       = "context_neighbours"
)

func initializeIndices() (err error) {

	if err = initializeIacIndices(); err != nil {
		return
	}

	if err = initializeEnhancerIndices(); err != nil {
		return
	}

	if err = initializeAiIndices(); err != nil {
		return
	}

	if err = initializeIdpIndices(); err != nil {
		return
	}

	if err = initializeIncidentsIndices(); err != nil {
		return
	}

	if err = initializeJiraIndices(); err != nil {
		return
	}

	if err = initializeMiscIndices(); err != nil {
		return
	}

	if err = initializeExternalRscIndices(); err != nil {
		return
	}

	if err = initializeContextorIndices(); err != nil {
		return
	}

	return
}
