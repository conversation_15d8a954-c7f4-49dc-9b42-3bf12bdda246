package elastic

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"strconv"
	"strings"
	"time"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/elastic/go-elasticsearch/v7/esapi"

	"github.com/precize/config"
	"github.com/precize/logger"
)

const (
	DATE_FORMAT        = "2006-01-02T15:04:05.000Z"
	DATE_FORMAT_SECOND = "2006-01-02T15:04:05Z"
	WINDOW_TOO_LARGE   = "Result window is too large"
	ORCA_DATE_FORMAT   = "2006-01-02T15:04:05-07:00"
	WIZ_DATE_FORMAT    = "2006-01-02T15:04:05Z"
)

var esClient *elasticsearch.Client

type ElasticRequest struct {
	Req    esapi.Request
	Data   string
	Action string
	Index  string
}

type ElasticErrorResp struct {
	Error struct {
		CausedBy struct {
			Reason string `json:"reason"`
		} `json:"caused_by"`
	} `json:"error"`
}

func ConnectToElasticSearch() (err error) {

	elasticConfig := config.AppConfig.Spring.ElasticSearch

	var cert []byte

	if len(elasticConfig.CACertPath) > 0 {
		if cert, err = ioutil.ReadFile(elasticConfig.CACertPath); err != nil {
			logger.Print(logger.ERROR, "Got error reading cert", elasticConfig.CACertPath, err)
			return
		}
	}

	cfg := elasticsearch.Config{
		Addresses: []string{elasticConfig.Scheme + "://" + elasticConfig.Host + ":" + elasticConfig.Port},
		Username:  elasticConfig.Username,
		Password:  elasticConfig.Password,
		CACert:    cert,
	}

	esClient, err = elasticsearch.NewClient(cfg)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating elastic client", cfg, err)
		return
	}

	res, err := esClient.Info()
	if err != nil {
		logger.Print(logger.ERROR, "Got error connecting to elasticsearch", cfg, err)
		return
	}

	defer res.Body.Close()
	logger.Print(logger.INFO, "Connected to Elasticsearch", res)

	if err = initializeIndices(); err != nil {
		return
	}

	return
}

func ConvertEpochToDateTime(epoch int64) string {
	if epoch == 0 {
		return ""
	}

	t := time.UnixMilli(epoch).UTC()
	return t.Format(DATE_FORMAT)

}

func DateTime(date time.Time) string {

	return date.UTC().Format(DATE_FORMAT)
}

func ParseDateTimeWithSeconds(dateString string) (time.Time, error) {
	return time.Parse(DATE_FORMAT_SECOND, dateString)
}

func ParseDateTime(dateString string) (time.Time, error) {
	return time.Parse(DATE_FORMAT, dateString)
}

func IndexExists(index string) (exists bool, err error) {

	req := esapi.IndicesExistsRequest{
		Index: []string{index},
	}

	_, err = requestElastic(ElasticRequest{
		Req:    req,
		Action: "IndexExists",
		Index:  index,
	})
	if err != nil {
		return
	}

	exists = true

	return
}

func CreateIndex(index, mapping string) (err error) {

	exists, _ := IndexExists(index)

	if exists {
		logger.Print(logger.INFO, "Index "+index+" exists")
		return
	}

	req := esapi.IndicesCreateRequest{
		Index: index,
		Body:  bytes.NewReader([]byte(mapping)),
	}

	res, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   mapping,
		Action: "Create Index",
		Index:  index,
	})
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating index", err)
		return
	}

	logger.Print(logger.INFO, "Creation Successful", index, string(res))
	return
}

func InsertDocument(tenantID, index string, data any, optionalDocID ...string) (docID string, err error) {

	dataJSON, err := json.Marshal(data)
	if err != nil {
		logger.Print(logger.ERROR, "Got error marshalling", err)
		return
	}

	req := esapi.IndexRequest{
		Index: index,
		Body:  bytes.NewReader(dataJSON),
	}

	if len(optionalDocID) > 0 {
		req.DocumentID = optionalDocID[0]
	}

	respBytes, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   string(dataJSON),
		Action: "Insert Document",
		Index:  index,
	})
	if err != nil {
		logger.Print(logger.ERROR, "Got error inserting document", err)
		return
	}

	if respBytes == nil {
		return
	}

	var r map[string]any
	if err = json.Unmarshal(respBytes, &r); err != nil {
		logger.Print(logger.ERROR, "Got error decoding response", err)
		return
	}

	docID, _ = r["_id"].(string)

	logger.Print(logger.INFO, "Insertion Successful", []string{tenantID}, index, docID)
	return
}

func GetDocument(index, documentID string) (map[string]any, error) {

	if exists, _ := DocumentExists(index, documentID); !exists {
		return nil, nil
	}

	var document = make(map[string]any)

	req := esapi.GetRequest{
		Index:      index,
		DocumentID: documentID,
	}

	respBytes, err := requestElastic(ElasticRequest{
		Req:    req,
		Action: "Retrieve Document",
		Index:  index,
	})
	if err != nil {
		return document, err
	}

	if respBytes == nil {
		return document, nil
	}

	var r map[string]any
	if err = json.Unmarshal(respBytes, &r); err != nil {
		logger.Print(logger.ERROR, "Got error decoding response", err)
		return document, err
	}

	document, _ = r["_source"].(map[string]any)

	return document, nil
}

func BulkDocumentsAPI(tenantID, index string, data string) error {

	req := esapi.BulkRequest{
		Index:        index,
		DocumentType: "_doc",
		Body:         bytes.NewReader([]byte(data)),
	}

	_, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   data,
		Action: "Bulk Insert",
		Index:  index,
	})
	if err != nil {
		return err
	}

	return nil
}

func DocumentExists(index, documentID string) (exists bool, err error) {
	req := esapi.ExistsRequest{
		Index:      index,
		DocumentID: documentID,
	}

	_, err = requestElastic(ElasticRequest{
		Req:    req,
		Action: "Document Exists",
		Index:  index,
	})
	if err != nil {
		return
	}

	exists = true
	return
}

// To get < 10k documents
func ExecuteSearchQuery(indices []string, query string) ([]map[string]any, error) {

	var (
		from, size int
		documents  []map[string]any
	)

	size = 100

	for {

		req := esapi.SearchRequest{
			Index: indices,
			Body:  strings.NewReader(query),
			From:  &from,
			Size:  &size,
		}

		respBytes, err := requestElastic(ElasticRequest{
			Req:    req,
			Data:   query,
			Action: "Retrieve Documents",
		})
		if err != nil {
			return documents, err
		}

		if respBytes == nil {
			return documents, nil
		}

		var r map[string]any
		if err = json.Unmarshal(respBytes, &r); err != nil {
			logger.Print(logger.ERROR, "Got error decoding response", err)
			return documents, err
		}

		if outerHits, ok := r["hits"].(map[string]any); ok {

			if innerHits, ok := outerHits["hits"].([]any); ok {

				if len(innerHits) > 0 {

					for _, innerHit := range innerHits {
						if hit, ok := innerHit.(map[string]any); ok {
							doc := hit["_source"].(map[string]any)
							doc["_id"] = hit["_id"].(string)
							documents = append(documents, doc)
						}
					}

					from = from + size
					continue
				}
			}
		}

		break
	}

	return documents, nil
}

// To get > 10k documents
func ExecuteLargeSearchQuery(indices []string, query string, searchAfter any) (map[string]map[string]any, any, error) {

	var (
		size         = 5000
		sortResponse any
		documents    = make(map[string]map[string]any)
	)

	var m map[string]any
	err := json.Unmarshal([]byte(query), &m)
	if err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling query", query, err)
		return documents, sortResponse, err
	}
	if _, ok := m["sort"]; !ok {
		m["sort"] = []map[string]any{
			{"_id": map[string]any{"order": "desc"}},
		}
	}

	if searchAfter != nil {
		m["search_after"] = searchAfter
	}
	queryBytes, err := json.Marshal(m)
	if err != nil {
		logger.Print(logger.ERROR, "Got error marshalling query", query, err)
		return documents, sortResponse, err
	}

	query = string(queryBytes)

	req := esapi.SearchRequest{
		Index: indices,
		Body:  strings.NewReader(query),
		Size:  &size,
	}

	respBytes, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   query,
		Action: "Retrieve Paginated Documents",
	})
	if err != nil {
		return documents, sortResponse, err
	}

	if respBytes == nil {
		return documents, sortResponse, nil
	}

	var r map[string]any
	if err = json.Unmarshal(respBytes, &r); err != nil {
		logger.Print(logger.ERROR, "Got error decoding response", err)
		return documents, sortResponse, err
	}

	if outerHits, ok := r["hits"].(map[string]any); ok {

		if innerHits, ok := outerHits["hits"].([]any); ok {

			if len(innerHits) > 0 {

				for _, innerHit := range innerHits {
					if hit, ok := innerHit.(map[string]any); ok {
						docID := hit["_id"].(string)
						documents[docID] = hit["_source"].(map[string]any)
						sortResponse = hit["sort"]
					}
				}
			}
		}
	}

	return documents, sortResponse, nil
}

// To get a specific number of records (size specified in query itself)
func ExecuteSearchQueryWithoutPagination(indices []string, query string) (map[string]map[string]any, error) {

	documents := make(map[string]map[string]any)

	req := esapi.SearchRequest{
		Index: indices,
		Body:  strings.NewReader(query),
	}

	respBytes, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   query,
		Action: "Retrieve Specific Documents",
	})
	if err != nil {
		return documents, err
	}

	if respBytes == nil {
		return documents, nil
	}

	var r map[string]any
	if err = json.Unmarshal(respBytes, &r); err != nil {
		logger.Print(logger.ERROR, "Got error decoding response", err)
		return documents, err
	}

	if outerHits, ok := r["hits"].(map[string]any); ok {

		if innerHits, ok := outerHits["hits"].([]any); ok {

			if len(innerHits) > 0 {

				for _, innerHit := range innerHits {
					if hit, ok := innerHit.(map[string]any); ok {
						docID := hit["_id"].(string)
						documents[docID] = hit["_source"].(map[string]any)
					}
				}
			}
		}
	}

	return documents, nil
}

// To get aggregation count data alone
func ExecuteSearchForAggregationCount(indices []string, query string) (map[string]float64, error) {

	aggCount := make(map[string]float64)

	req := esapi.SearchRequest{
		Index: indices,
		Body:  strings.NewReader(query),
	}

	respBytes, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   query,
		Action: "Aggregated Count",
	})
	if err != nil {
		return nil, err
	}

	if respBytes == nil {
		return aggCount, nil
	}

	var r map[string]any
	if err = json.Unmarshal(respBytes, &r); err != nil {
		logger.Print(logger.ERROR, "Got error decoding response", err)
		return aggCount, err
	}

	if aggs, ok := r["aggregations"].(map[string]any); ok {
		for _, v := range aggs {
			if aggMap, ok := v.(map[string]any); ok {
				if aggBuckets, ok := aggMap["buckets"].([]any); ok {
					for _, aggBucket := range aggBuckets {
						if bucket, ok := aggBucket.(map[string]any); ok {
							if key, ok := bucket["key"].(string); ok {
								aggCount[key] = bucket["doc_count"].(float64)
							}
						}
					}
				}
			}
		}
	}

	return aggCount, nil
}

// To get aggregation data
func ExecuteSearchForAggregation(indices []string, query string) (aggregations map[string]any, err error) {

	req := esapi.SearchRequest{
		Index: indices,
		Body:  strings.NewReader(query),
	}

	respBytes, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   query,
		Action: "Search for Aggregation",
	})
	if err != nil {
		logger.Print(logger.INFO, "Failed query", query)
		return
	}

	if respBytes == nil {
		return
	}

	var r map[string]any
	if err = json.Unmarshal(respBytes, &r); err != nil {
		logger.Print(logger.ERROR, "Got error decoding response", err)
		return
	}

	if aggs, ok := r["aggregations"].(map[string]any); ok {
		aggregations = aggs
	}

	return
}

// not being used
func RefreshIndex(index string) error {
	refreshReq := esapi.IndicesRefreshRequest{
		Index: []string{index},
	}

	_, err := requestElastic(ElasticRequest{
		Req:    refreshReq,
		Data:   "",
		Action: "Refresh Index",
		Index:  index,
	})
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Refresh Successful", index)
	return nil
}

func UpdateWithRefreshDocument(index, documentID string, data any) (err error) {

	body, _ := json.Marshal(data)

	req := esapi.UpdateRequest{
		Index:      index,
		DocumentID: documentID,
		Refresh:    "true",
		Body:       bytes.NewReader([]byte(fmt.Sprintf(`{"doc":%s}`, body))),
	}

	res, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   string(body),
		Action: "Update With Refresh",
		Index:  index,
	})
	if err != nil {
		return
	}

	logger.Print(logger.INFO, "Update Successful", index, res)
	return
}

func UpdateDocument(index, documentID string, data any) (err error) {

	body, _ := json.Marshal(data)

	req := esapi.UpdateRequest{
		Index:      index,
		DocumentID: documentID,
		Body:       bytes.NewReader([]byte(fmt.Sprintf(`{"doc":%s}`, body))),
	}

	res, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   "",
		Action: "Update Document",
		Index:  index,
	})
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Update Successful", index, string(res))
	return
}

func UpdateByQuery(index, query string) error {

	req := esapi.UpdateByQueryRequest{
		Index:        []string{index},
		DocumentType: []string{"_doc"},
		Body:         strings.NewReader(query),
	}

	_, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   query,
		Action: "Update Document By Query",
		Index:  index,
	})
	if err != nil {
		return err
	}

	return nil
}

func DeleteByQuery(index, query string) error {

	req := esapi.DeleteByQueryRequest{
		Index:        []string{index},
		DocumentType: []string{"_doc"},
		Body:         strings.NewReader(query),
	}

	_, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   query,
		Action: "Delete Document By Query",
		Index:  index,
	})
	if err != nil {
		return err
	}

	return nil
}

func DeleteDocument(index, documentID string) (err error) {
	req := esapi.DeleteRequest{
		Index:      index,
		DocumentID: documentID,
	}

	res, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   "",
		Action: "Delete Document",
		Index:  index,
	})
	if err != nil {
		return err
	}

	logger.Print(logger.INFO, "Delete Successful", index, res)
	return
}

func GetIndexCount(index string, query string) (int64, error) {

	req := esapi.CountRequest{
		Index: []string{index},
		Body:  strings.NewReader(query),
	}

	respBytes, err := requestElastic(ElasticRequest{
		Req:    req,
		Data:   "",
		Action: "Index Count",
		Index:  index,
	})
	if err != nil {
		return 0, err
	}

	if respBytes == nil {
		return 0, nil
	}

	var r map[string]any
	if err = json.Unmarshal(respBytes, &r); err != nil {
		logger.Print(logger.ERROR, "Got error decoding response", err)
		return 0, err
	}

	count, ok := r["count"].(float64)
	if !ok {
		err = errors.New("error retrieving count from response")
		return 0, err
	}

	return int64(count), nil
}

func ExecuteSearchWithAggs(indices []string, query string) (hits []map[string]any, aggregations map[string]any, err error) {
	var (
		from int
		size = 100
	)

	for {
		req := esapi.SearchRequest{
			Index: indices,
			Body:  strings.NewReader(query),
			From:  &from,
			Size:  &size,
		}

		respBytes, reqErr := requestElastic(ElasticRequest{
			Req:    req,
			Data:   query,
			Action: "Search with Aggregations",
		})
		if reqErr != nil {
			err = reqErr
			return
		}
		if respBytes == nil {
			return
		}

		var r map[string]any
		if err = json.Unmarshal(respBytes, &r); err != nil {
			logger.Print(logger.ERROR, "Got error decoding response", err)
			return
		}

		if outerHits, ok := r["hits"].(map[string]any); ok {
			if innerHits, ok := outerHits["hits"].([]any); ok {
				if len(innerHits) > 0 {
					for _, innerHit := range innerHits {
						if hit, ok := innerHit.(map[string]any); ok {
							doc := hit["_source"].(map[string]any)
							doc["_id"] = hit["_id"].(string)
							hits = append(hits, doc)
						}
					}
					from += size
					continue
				}
			}
		}

		if aggs, ok := r["aggregations"].(map[string]any); ok {
			aggregations = aggs
		}

		break
	}

	return
}

func requestElastic(req ElasticRequest) (respBytes []byte, err error) {

	res, err := req.Req.Do(context.Background(), esClient)
	if err != nil {
		logger.Print(logger.ERROR, "Got error processing document", err)
		return
	}
	defer res.Body.Close()

	respBytes, err = io.ReadAll(res.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Got error reading response", err)
		return
	}

	switch res.StatusCode {
	case 429:
		for i := 0; i < 4; i++ {

			res, err = req.Req.Do(context.Background(), esClient)
			if err != nil {
				logger.Print(logger.ERROR, "Got error processing documents", err)
				return
			}
			defer res.Body.Close()

			if res.StatusCode == 429 {
				backOffTime := 2 * (i + 1)
				logger.Print(logger.INFO, "Got 429 too many requests, backing off for "+strconv.Itoa(backOffTime)+" seconds")
				time.Sleep(time.Duration(backOffTime) * time.Second)
				continue
			}

			if res.IsError() {
				err = errors.New("Got response error bulk inserting documents")
				logger.Print(logger.ERROR, "Got error performing elasticsearch action", err, res)
				return
			}

			break
		}
	case 413:
		lines := strings.Split(req.Data, "\n")
		if lines[len(lines)-1] == "" {
			lines = lines[:len(lines)-1]
		}

		if len(lines) <= 2 {
			return nil, errors.New("Resquest body too large and cannot be partitioned further")
		}

		mid := len(lines) / 2

		if mid > 1 && strings.Contains(lines[mid-1], "\"update\"") || strings.Contains(lines[mid-1], "\"index\"") {
			mid -= 1
		}

		part1 := strings.Join(lines[:mid], "\n") + "\n"
		part2 := strings.Join(lines[mid:], "\n") + "\n"

		req := esapi.BulkRequest{
			Index:        req.Index,
			DocumentType: "_doc",
			Body:         bytes.NewReader([]byte(part1)),
		}

		if _, err = requestElastic(ElasticRequest{
			Req:   req,
			Data:  part1,
			Index: req.Index,
		}); err != nil {
			return
		}

		req = esapi.BulkRequest{
			Index:        req.Index,
			DocumentType: "_doc",
			Body:         bytes.NewReader([]byte(part2)),
		}

		if _, err = requestElastic(ElasticRequest{
			Req:  req,
			Data: part2,
		}); err != nil {
			return
		}
	}

	if res.IsError() {

		switch req.Action {
		case "Retrieve Documents", "Retrieve Paginated Documents", "Retrieve Specific Documents", "Aggregated Count", "Search for Aggregation":
			var elasticErrorResp ElasticErrorResp
			err = errors.New("Got response error performing elasticsearch action")

			if err1 := json.NewDecoder(res.Body).Decode(&elasticErrorResp); err1 != nil {
				logger.Print(logger.ERROR, "Got error decoding error", err1)
			} else {
				err = errors.New(elasticErrorResp.Error.CausedBy.Reason)
			}

			if strings.Contains(err.Error(), WINDOW_TOO_LARGE) {
				logger.Print(logger.INFO, "Got error performing elasticsearch action", err)
			} else {
				logger.Print(logger.ERROR, "Got error performing elasticsearch action", err, res)
			}

			return

		case "IndexExists", "Document Exists":
			err = errors.New("Got response error getting document")
			return
		default:
			if res.StatusCode != 413 && res.StatusCode != 429 {
				// other handled error codes to be added here
				err = errors.New("Got response error performing action")
				logger.Print(logger.ERROR, "Got error performing elasticsearch action", err, res, req.Action)

				return
			}
		}
	}

	// document exists will always have empty body
	if len(string(respBytes)) == 0 {
		return
	}

	var respMap map[string]any
	if err = json.Unmarshal(respBytes, &respMap); err != nil {
		logger.Print(logger.ERROR, "Got error decoding response", err)
		return
	}

	if isError, ok := respMap["errors"].(bool); ok && isError {
		logger.Print(logger.INFO, "Failed query", respMap, req.Data)
		logger.Print(logger.ERROR, "Got error performing elasticsearch action", req.Action)

		return
	}

	return
}
