package elastic

func initializeContextorIndices() error {

	err := CreateIndex(CONTEXT_NEIGHBOURS_INDEX,
		`{
		    "settings":
		    {
		        "analysis":
		        {
		            "analyzer":
		            {
		                "default":
		                {
		                    "type": "keyword"
		                }
		            }
		        },
		        "number_of_shards": 1,
		        "number_of_replicas": 0
		    },
		    "mappings":
		    {
		        "properties":
		        {
		            "type":
		            {
		                "type": "keyword",
		                "fields":
		                {
		                    "keyword":
		                    {
		                        "type": "keyword",
		                        "ignore_above": 256
		                    }
		                }
		            },
		            "resourceA":
		            {
		                "type": "nested",
		                "properties":
		                {
		                    "entityId":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "entityType":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "value":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    }
		                }
		            },
		            "resourceB":
		            {
		                "type": "nested",
		                "properties":
		                {
		                    "entityId":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "entityType":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    },
		                    "value":
		                    {
		                        "type": "text",
		                        "fields":
		                        {
		                            "keyword":
		                            {
		                                "type": "keyword",
		                                "ignore_above": 256
		                            }
		                        }
		                    }
		                }
		            },
		            "score":
		            {
		                "type": "float"
		            },
		            "tenantId":
		            {
		                "type": "keyword"
		            },
		            "serviceId":
		            {
		                "type": "keyword"
		            },
		            "id":
		            {
		                "type": "keyword"
		            },
		            "deleted":
		            {
		                "type": "boolean"
		            },
		            "insertTime":
		            {
		                "type": "date",
		                "format": "date_time"
		            }
		        }
		    }
		}`,
	)
	if err != nil {
		return err
	}

	return nil
}
