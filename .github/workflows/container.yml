name: Container Build

on:
  # push:
   # branches: [ "main","qa-stable" ]
  workflow_dispatch:
    inputs:
      env:
        type: choice
        description: Choose environment
        options:
          - qa
          - preprod
          - prod
        required: true

jobs:
  build-and-deploy:
    runs-on: self-hosted
    environment: ${{ inputs.env }}
    steps:

      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Docker Login to ECR
        run: |
          aws ecr get-login-password --region ${{ vars.ECR_REGION }} | \
            sudo docker login --username AWS --password-stdin ${{ vars.ECR_REGISTRY }}

      - name: Build and Push Contextor
        run: |
          ECR_IMAGE=${{ vars.ECR_REGISTRY }}/precize-contextor:${{ inputs.env }}
          sudo docker build --platform linux/amd64 -t precize-contextor -f contextor/Dockerfile .
          sudo docker tag precize-contextor $ECR_IMAGE
          sudo docker push $ECR_IMAGE
