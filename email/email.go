package email

import (
	"log"
	"strings"
	"time"

	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"

	"github.com/precize/config"
)

func SendEmail(subject string, body string, recipients map[string]string, cc map[string]string, bcc map[string]string) error {

	m := mail.NewV3Mail()

	from := mail.NewEmail("Precize Notify", "<EMAIL>")
	content := mail.NewContent("text/plain", body)

	m.SetFrom(from)
	m.AddContent(content)

	personalization := mail.NewPersonalization()
	to := make([]*mail.Email, 0)

	for name, email := range recipients {
		to = append(to, mail.NewEmail(name, email))
	}

	personalization.AddTos(to...)
	to = make([]*mail.Email, 0)

	for name, email := range cc {
		to = append(to, mail.NewEmail(name, email))
	}

	personalization.AddCCs(to...)
	to = make([]*mail.Email, 0)

	for name, email := range bcc {
		to = append(to, mail.NewEmail(name, email))
	}

	personalization.AddBCCs(to...)

	personalization.Subject = subject

	m.AddPersonalizations(personalization)

	request := sendgrid.GetRequest("*********************************************************************", "/v3/mail/send", "https://api.sendgrid.com")
	request.Method = "POST"
	request.Body = mail.GetRequestBody(m)
	response, err := sendgrid.API(request)
	if err != nil || (response.StatusCode < 200 && response.StatusCode >= 300) {
		log.Println("Email sending failed: ", err, response.StatusCode)
		return err
	}

	return nil
}

var LastPanicEmail time.Time

func SendPanicEmail(service string) {

	if time.Now().UTC().Sub(LastPanicEmail) <= 12*time.Hour {
		return
	}

	LastPanicEmail = time.Now().UTC()

	body := "Panic occured: " + service + " in " + config.Environment + " environment"
	recipients := map[string]string{
		"Abhay":  "<EMAIL>",
		"Aniket": "<EMAIL>",
	}

	SendEmail("Panic occured", body, recipients, nil, nil)
}

func SendErrorEmail(logFile, subject string, errorMessages []string) {

	var body string

	if subject == "Error threshold reached" {
		body = "Error threshold reached in: " + logFile + " in " + config.Environment + " environment." + "\n\nErrors:\n\n" + strings.Join(errorMessages, "\n\n")
	} else {
		body = "Collating errors in " + logFile + " in " + config.Environment + " environment." + "\n\nErrors:\n\n" + strings.Join(errorMessages, "\n\n")
	}

	recipients := map[string]string{
		"Abhay":  "<EMAIL>",
		"Aniket": "<EMAIL>",
	}

	SendEmail(subject, body, recipients, nil, nil)
}
