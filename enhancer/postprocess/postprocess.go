package postprocess

import (
	"path/filepath"
	"slices"
	"strings"
	"sync"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/internal/property"
	"github.com/precize/enhancer/rcontext"
)

func PostProcessRelatedResources(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, resourceContextMap *sync.Map, enhancedUniqueOwners, enhancedUniqueApps, enhancedUniqueTeams *[]string) {

	var additionalRelations bool

	if relatedResources, ok := resourceContext.GetRelatedResourceList(docID); ok {

		var updatedRelatedResources = append([]rcontext.RelatedResource{}, relatedResources...)

		for _, relatedResource := range relatedResources {
			if len(relatedResource.TransitiveResourceType) > 0 {
				if level2RelatedResources, ok := resourceContext.GetRelatedResourceList(relatedResource.ResourceDocID); ok {
					for _, level2RelatedResource := range level2RelatedResources {
						if level2RelatedResource.ResourceType == relatedResource.TransitiveResourceType {
							updatedRelatedResources = append(updatedRelatedResources, level2RelatedResource)
							additionalRelations = true
						}
					}
				}
			}
		}

		for _, relatedResource := range updatedRelatedResources {
			if relatedResource.ContextualRelation {
				if dataEntry, ok := resourceContextMap.Load(relatedResource.ResourceDocID); ok {
					if rData, ok := dataEntry.(*context.ContextUniqueList); ok && rData != nil {

						if owners, ok := rData.Get(property.OWNER_PROPERTY_NAME); ok && len(owners) > 0 {
							if relatedResource.Parent && len(owners) > 0 {
								// Parent relation only
								directParentOwner := owners[0]

								rctxItem := context.GetContextItem(directParentOwner, common.PARENT_RESOURCE_OWNER_TYPE,
									"User owns parent "+relatedResource.ResourceType+" of this resource",
								)

								if !slices.Contains(*enhancedUniqueOwners, directParentOwner) {
									rctxItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
									*enhancedUniqueOwners = append(*enhancedUniqueOwners, directParentOwner)
								}

								resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, rctxItem)
							}
						}

						if uniqueResourceApps, ok := rData.Get(property.APP_PROPERTY_NAME); ok && len(uniqueResourceApps) > 0 {

							for _, uniqueResourceApp := range uniqueResourceApps {
								if !slices.Contains(*enhancedUniqueApps, uniqueResourceApp) {
									resourceContextDoc.ResourceAppTypes.DerivedApp = append(resourceContextDoc.ResourceAppTypes.DerivedApp, context.GetContextItem(uniqueResourceApp, common.RELATED_RESOURCE_APP_TYPE, ""))
									*enhancedUniqueApps = append(*enhancedUniqueApps, uniqueResourceApp)
								}
							}
						}

						if uniqueResourceTeams, ok := rData.Get(property.TEAM_PROPERTY_NAME); ok && len(uniqueResourceTeams) > 0 {

							for _, uniqueResourceTeam := range uniqueResourceTeams {
								if !slices.Contains(*enhancedUniqueTeams, uniqueResourceTeam) {
									resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextDoc.ResourceTeamTypes.DerivedTeam, context.GetContextItem(uniqueResourceTeam, common.RELATED_RESOURCE_TEAM_TYPE, ""))
									*enhancedUniqueTeams = append(*enhancedUniqueTeams, uniqueResourceTeam)
								}
							}
						}
					}
				}
			}
		}

		if additionalRelations {
			resourceContext.SetRelatedResourceList(docID, updatedRelatedResources)
		}
	}
}

func PostProcessMaxRelatedOwner(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, resourceContextMap *sync.Map, enhancedUniqueOwners *[]string) {

	if len(*enhancedUniqueOwners) <= 0 {

		var (
			maxCount        int
			maxOwner        string
			relatedOwners   = make(map[string]context.RelatedOwner)
			processedDocIDs = make(map[string]struct{})
		)

		context.GetMaxRelatedOwner(resourceContext, resourceContextDoc, docID, resourceContextMap, &maxCount, &maxOwner, "", relatedOwners, processedDocIDs, 1, false)

		if len(maxOwner) > 0 {

			var (
				desc         string
				relRes       []string
				iacRes       []string
				directString = "indirectly"
			)

			for resourceType, resources := range relatedOwners[maxOwner].Resources {
				if relatedOwners[maxOwner].IacRelation {
					for _, resource := range resources {
						if len(iacRes) < 2 {
							str := resource + " (" + resourceType + ")"
							if !slices.Contains(iacRes, str) {
								iacRes = append(iacRes, str)
							}
						}
					}
				} else {
					if relatedOwners[maxOwner].Direct {
						directString = "directly"
					}
					for _, resource := range resources {
						if len(relRes) < 2 {
							str := resource + " (" + resourceType + ")"
							if !slices.Contains(relRes, str) {
								relRes = append(relRes, str)
							}
						}
					}
				}
			}

			if len(iacRes) > 0 {
				var (
					initialWording    = "a resource"
					conclusionWording = ": "
				)
				if len(iacRes) > 1 {
					initialWording = "multiple resources"
					conclusionWording = ", including "
				}
				desc = "User owns " + initialWording + " from the same IaC deployment" + conclusionWording + strings.Join(iacRes, " and ")
			}

			if len(relRes) > 0 {
				if len(desc) > 0 {
					desc += ". "
				}
				var (
					initialWording    = "a resource " + directString
					conclusionWording = ": "
				)
				if len(relRes) > 1 {
					initialWording = "multiple resources " + directString
					conclusionWording = ", including "
				}
				desc += "User owns " + initialWording + " related to this resource" + conclusionWording + strings.Join(relRes, " and ")
			}

			if len(desc) > 0 {
				if len(relatedOwners[maxOwner].Via) > 0 {
					desc += " via " + strings.Join(relatedOwners[maxOwner].Via, " and ")
				}
			} else {
				desc = "User owns resources related to this resource"
			}

			resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, context.GetContextItem(maxOwner, common.RELATED_RESOURCE_OWNER_TYPE,
				desc, context.WithObjectType(common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)), context.WithAdditional(map[string]any{"direct": directString == "directly"})),
			)

			*enhancedUniqueOwners = append(*enhancedUniqueOwners, maxOwner)
		}
	}
}

type maxOwner struct {
	count    int
	resource string
}

func PostProcessSimilarResourceNames(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, resourceContextMap *sync.Map, enhancedUniqueOwners, enhancedUniqueApps, enhancedUniqueTeams *[]string) {

	var (
		maxOwnerMap                     = make(map[string]maxOwner)
		maxCount, totalSimilarResources int
		maxOwner, resource              string
	)

	if similarResourceNameDocIDs, ok := resourceContext.GetSimilarResourceNameList(docID); ok {

		for _, similarResourceDocID := range similarResourceNameDocIDs {

			totalSimilarResources = len(similarResourceNameDocIDs)

			if dataEntry, ok := resourceContextMap.Load(similarResourceDocID); ok {
				if rData, ok := dataEntry.(*context.ContextUniqueList); ok && rData != nil {

					if uniqueResourceOwners, ok := rData.Get(property.OWNER_PROPERTY_NAME); ok && len(uniqueResourceOwners) > 0 {
						if similarResourceInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(similarResourceDocID); ok {

							for _, uniqueResourceOwner := range uniqueResourceOwners {
								tmp := maxOwnerMap[uniqueResourceOwner]
								tmp.count++
								if len(tmp.resource) <= 0 {
									resourceID := filepath.Base(similarResourceInsertDoc.ResourceID)
									tmp.resource = resourceID + " (" + similarResourceInsertDoc.ResourceType + ")"
								}
								maxOwnerMap[uniqueResourceOwner] = tmp
							}
						}
					}

					if uniqueResourceApps, ok := rData.Get(property.APP_PROPERTY_NAME); ok && len(uniqueResourceApps) > 0 {

						for _, uniqueResourceApp := range uniqueResourceApps {
							if !slices.Contains(*enhancedUniqueApps, uniqueResourceApp) {

								resourceContextDoc.ResourceAppTypes.DerivedApp = append(resourceContextDoc.ResourceAppTypes.DerivedApp, context.GetContextItem(uniqueResourceApp, common.SIMILAR_RESOURCENAME_APP_TYPE, "", context.WithAdditional(map[string]any{"uniqueApps": len(uniqueResourceApps)})))

								*enhancedUniqueApps = append(*enhancedUniqueApps, uniqueResourceApp)
							}
						}
					}

					if uniqueResourceTeams, ok := rData.Get(property.TEAM_PROPERTY_NAME); ok && len(uniqueResourceTeams) > 0 {
						for _, uniqueResourceTeam := range uniqueResourceTeams {
							if !slices.Contains(*enhancedUniqueTeams, uniqueResourceTeam) {

								resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextDoc.ResourceTeamTypes.DerivedTeam, context.GetContextItem(uniqueResourceTeam, common.SIMILAR_RESOURCENAME_TEAM_TYPE, "", context.WithAdditional(map[string]any{"uniqueTeams": len(uniqueResourceTeams)})))

								*enhancedUniqueTeams = append(*enhancedUniqueTeams, uniqueResourceTeam)
							}
						}
					}
				}
			}
		}
	}

	if len(*enhancedUniqueOwners) <= 0 {

		// Owner is through majority
		for owner, details := range maxOwnerMap {
			if details.count > maxCount {
				maxCount = details.count
				maxOwner = owner
				resource = details.resource
			}
		}

		if len(maxOwner) > 0 {

			rctxItem := context.GetContextItem(maxOwner, common.SIMILAR_RESOURCENAME_OWNER_TYPE, "User owns resource(s) including "+resource+" that are likely to be related to this resource", context.WithAdditional(map[string]any{"resourceCount": totalSimilarResources, "totalOwnerCount": len(maxOwnerMap), "ownerMaxCount": maxCount}))

			if !slices.Contains(*enhancedUniqueOwners, maxOwner) {
				rctxItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				*enhancedUniqueOwners = append(*enhancedUniqueOwners, maxOwner)
			}

			resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, rctxItem)
		}
	}
}

func PostProcessSameAppResources(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, resourceContextMap *sync.Map, enhancedUniqueOwners, enhancedUniqueTeams *[]string) {

	var (
		maxOwnerMap                     = make(map[string]maxOwner)
		maxCount, totalSameAppResources int
		maxOwner, resource              string
	)

	if similarAppDocIDs, ok := resourceContext.GetSameAppResourceList(docID); ok {

		for _, sameAppDocID := range similarAppDocIDs {

			totalSameAppResources = len(similarAppDocIDs)

			if dataEntry, ok := resourceContextMap.Load(sameAppDocID); ok {
				if rData, ok := dataEntry.(*context.ContextUniqueList); ok && rData != nil {

					if sameAppResourceDoc, ok := resourceContext.GetResourceContextInsertDoc(sameAppDocID); ok {

						if uniqueResourceOwners, ok := rData.Get(property.OWNER_PROPERTY_NAME); ok && len(uniqueResourceOwners) > 0 {
							for _, uniqueResourceOwner := range uniqueResourceOwners {
								tmp := maxOwnerMap[uniqueResourceOwner]
								tmp.count++
								if len(tmp.resource) <= 0 {
									resourceID := filepath.Base(sameAppResourceDoc.ResourceID)
									tmp.resource = resourceID + " (" + sameAppResourceDoc.ResourceType + ")"
								}
								maxOwnerMap[uniqueResourceOwner] = tmp
							}
						}
					}

					if uniqueResourceTeams, ok := rData.Get(property.TEAM_PROPERTY_NAME); ok && len(uniqueResourceTeams) > 0 {
						for _, uniqueResourceTeam := range uniqueResourceTeams {
							if !slices.Contains(*enhancedUniqueTeams, uniqueResourceTeam) {

								resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(
									resourceContextDoc.ResourceTeamTypes.DerivedTeam,
									context.GetContextItem(uniqueResourceTeam, common.SAME_APP_TEAM_TYPE, "", context.WithAdditional(map[string]any{"uniqueTeams": len(uniqueResourceTeams)})),
								)

								*enhancedUniqueTeams = append(*enhancedUniqueTeams, uniqueResourceTeam)
							}
						}
					}
				}
			}
		}
	}

	// Owner is through majority
	for owner, details := range maxOwnerMap {
		if details.count > maxCount {
			maxCount = details.count
			maxOwner = owner
			resource = details.resource
		}
	}

	if len(maxOwner) > 0 {

		rctxItem := context.GetContextItem(maxOwner, common.SAME_APP_OWNER_TYPE, "User owns resource(s) including "+resource+" that are part of the same application as this resource", context.WithAdditional(map[string]any{"resourceCount": totalSameAppResources, "totalOwnerCount": len(maxOwnerMap), "ownerMaxCount": maxCount}))

		if !slices.Contains(*enhancedUniqueOwners, maxOwner) {
			rctxItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			*enhancedUniqueOwners = append(*enhancedUniqueOwners, maxOwner)
		}

		resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, rctxItem)
	}
}

func PostProcessSameTagResources(docID string, resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, resourceContextMap *sync.Map, enhancedUniqueOwners, enhancedUniqueApps, enhancedUniqueTeams *[]string) {

	var (
		maxOwnerMap                     = make(map[string]maxOwner)
		maxCount, totalSameTagResources int
		maxOwner, resource              string
	)

	if similarTagDocIDs, ok := resourceContext.GetSameTagResourceList(docID); ok {

		for _, sameTagDocID := range similarTagDocIDs {

			totalSameTagResources = len(similarTagDocIDs)

			if dataEntry, ok := resourceContextMap.Load(sameTagDocID); ok {
				if rData, ok := dataEntry.(*context.ContextUniqueList); ok && rData != nil {

					if sameTagResourceDoc, ok := resourceContext.GetResourceContextInsertDoc(sameTagDocID); ok {

						if uniqueResourceOwners, ok := rData.Get(property.OWNER_PROPERTY_NAME); ok && len(uniqueResourceOwners) > 0 {
							for _, uniqueResourceOwner := range uniqueResourceOwners {
								tmp := maxOwnerMap[uniqueResourceOwner]
								tmp.count++
								if len(tmp.resource) <= 0 {
									resourceID := filepath.Base(sameTagResourceDoc.ResourceID)
									tmp.resource = resourceID + " (" + sameTagResourceDoc.ResourceType + ")"
								}
								maxOwnerMap[uniqueResourceOwner] = tmp
							}
						}
					}

					if uniqueResourceApps, ok := rData.Get(property.APP_PROPERTY_NAME); ok && len(uniqueResourceApps) > 0 {
						for _, uniqueResourceApp := range uniqueResourceApps {
							if !slices.Contains(*enhancedUniqueApps, uniqueResourceApp) {
								resourceContextDoc.ResourceAppTypes.DerivedApp = append(resourceContextDoc.ResourceAppTypes.DerivedApp, context.GetContextItem(uniqueResourceApp, common.SAME_TAG_APP_TYPE, "", context.WithAdditional(map[string]any{"uniqueApps": len(uniqueResourceApps)})))
								*enhancedUniqueApps = append(*enhancedUniqueApps, uniqueResourceApp)
							}
						}
					}

					if uniqueResourceTeams, ok := rData.Get(property.TEAM_PROPERTY_NAME); ok && len(uniqueResourceTeams) > 0 {
						for _, uniqueResourceTeam := range uniqueResourceTeams {
							if !slices.Contains(*enhancedUniqueTeams, uniqueResourceTeam) {
								resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextDoc.ResourceTeamTypes.DerivedTeam, context.GetContextItem(uniqueResourceTeam, common.SAME_TAG_TEAM_TYPE, "", context.WithAdditional(map[string]any{"uniqueTeams": len(uniqueResourceTeams)})))
								*enhancedUniqueTeams = append(*enhancedUniqueTeams, uniqueResourceTeam)
							}
						}
					}
				}
			}
		}
	}

	if len(*enhancedUniqueOwners) <= 0 {

		// Owner is through majority
		for owner, details := range maxOwnerMap {
			if details.count > maxCount {
				maxCount = details.count
				maxOwner = owner
				resource = details.resource
			}
		}

		if len(maxOwner) > 0 {

			rctxItem := context.GetContextItem(maxOwner, common.SAME_TAG_OWNER_TYPE, "User owns resource(s) including "+resource+" that have the same tags as this resource", context.WithAdditional(map[string]any{"resourceCount": totalSameTagResources, "totalOwnerCount": len(maxOwnerMap), "ownerMaxCount": maxCount}))

			if !slices.Contains(*enhancedUniqueOwners, maxOwner) {
				rctxItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
				*enhancedUniqueOwners = append(*enhancedUniqueOwners, maxOwner)
			}

			resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, rctxItem)
		}
	}
}

func PostProcessResourceTypeOwner(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, enhancedUniqueOwners *[]string) {

	if len(resourceContextDoc.Account) > 0 {

		if rscTypeOwners, ok := resourceContext.GetResourceTypeOwner(resourceContextDoc.Account); ok {

			if resourceCounts, ok := resourceContext.GetResourceCounts(resourceContextDoc.Account); ok {

				resourceTypeCount := resourceCounts.ResourceTypeCount[resourceContextDoc.ResourceType]

				resourceTypeOwners, max := context.GetMaxActivityOwnersOfResourceType(rscTypeOwners, resourceContextDoc.ResourceType, resourceTypeCount)

				for _, resourceTypeOwner := range resourceTypeOwners {

					rctxItem := context.GetContextItem(resourceTypeOwner, common.RESOURCE_TYPE_OWNER_TYPE, "", context.WithAdditional(map[string]any{"resourceTypeCount": resourceTypeCount, "totalOwnerCount": len(rscTypeOwners[resourceContextDoc.ResourceType]), "ownerMaxCount": max}))

					if !slices.Contains(*enhancedUniqueOwners, resourceTypeOwner) {
						rctxItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
						*enhancedUniqueOwners = append(*enhancedUniqueOwners, resourceTypeOwner)
					}

					resourceContextDoc.ResourceOwnerTypes.OpsOwners = append(resourceContextDoc.ResourceOwnerTypes.OpsOwners, rctxItem)
				}
			}
		}
	}
}

func PostProcessSmallAccounts(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, resourceContextMap *sync.Map, enhancedUniqueOwners *[]string) {

	if len(*enhancedUniqueOwners) <= 0 {

		switch resourceContextDoc.ResourceType {
		case common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE,
			common.OPENAI_ORG_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE:
			// no parent owner

		default:

			if resourceCounts, ok := resourceContext.GetResourceCounts(resourceContextDoc.Account); ok {

				if parentChildOwner, _ := resourceContext.GetParentChildOwner(resourceContextDoc.Account); len(parentChildOwner) == 0 {
					// Child resources doesn't have any owners, so possibly one/two person account

					var parentResourceDocID string

					switch resourceContext.ServiceID {
					case common.AWS_SERVICE_ID:
						if resourceContextDoc.ResourceType == common.AWS_ACCOUNT_RESOURCE_TYPE {
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AWS_ORG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)

							if _, loadOk := resourceContextMap.Load(parentResourceDocID); !loadOk {
								parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AWS_ORGUNIT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
							}
						} else {
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AWS_ACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						}

					case common.AZURE_SERVICE_ID:
						switch resourceContextDoc.ResourceType {
						case common.AZURE_MGMTGRP_RESOURCE_TYPE:
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_TENANT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE:
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_MGMTGRP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
							if _, loadOk := resourceContextMap.Load(parentResourceDocID); !loadOk {
								parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_TENANT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
							}
						default:
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						}
					case common.GCP_SERVICE_ID:
						switch resourceContextDoc.ResourceType {
						case common.GCP_PROJECT_RESOURCE_TYPE:
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_FOLDER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
							if _, loadOk := resourceContextMap.Load(parentResourceDocID); !loadOk {
								parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_ORG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
							}
						case common.GCP_FOLDER_RESOURCE_TYPE:
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_FOLDER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
							if _, loadOk := resourceContextMap.Load(parentResourceDocID); !loadOk {
								parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_ORG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
							}
						default:
							parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_PROJECT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
						}

					case common.OPENAI_SERVICE_ID:
						parentResourceDocID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.OPENAI_PROJECT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.TenantID)
					}

					if dataEntry, loadOk := resourceContextMap.Load(parentResourceDocID); loadOk {
						if rData, ok := dataEntry.(*context.ContextUniqueList); ok && rData != nil {
							if parentOwners, ok := rData.Get(property.OWNER_PROPERTY_NAME); ok && len(parentOwners) > 0 {

								if len(parentOwners) > 2 {
									parentOwners = parentOwners[:2]
								}

								for _, parentOwner := range parentOwners {
									resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, context.GetContextItem(parentOwner, common.NO_CHILD_OWNERS_PARENT_OWNER_TYPE, "", context.WithObjectType(common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)), context.WithAdditional(map[string]any{"resourceCount": resourceCounts.AccountCount})))

									*enhancedUniqueOwners = append(*enhancedUniqueOwners, parentOwner)
								}
							}
						}
					}

				} else if len(parentChildOwner) <= 2 {
					// Account resources have very few owners, so possibly one/two person account

					var parentChildOwners []string

					for childOwner := range parentChildOwner {
						parentChildOwners = append(parentChildOwners, childOwner)
					}

					slices.SortFunc(parentChildOwners, context.SortUsernameString)

					for _, childOwner := range parentChildOwners {
						resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, context.GetContextItem(childOwner, common.FEW_CHILD_OWNERS_PARENT_OWNER_TYPE, "", context.WithObjectType(common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)), context.WithAdditional(map[string]any{"resourceCount": resourceCounts.AccountCount})))

						*enhancedUniqueOwners = append(*enhancedUniqueOwners, childOwner)
					}
				}
			}
		}
	}
}

func PostProcessParentComplianceAndSensitivity(docID string, resourceContextDoc *common.ResourceContextInsertDoc, uniqueSensitivity, uniqueCompliance []string, accountSensitivity, accountCompliance *sync.Map) {

	switch resourceContextDoc.ResourceType {
	case common.AWS_ACCOUNT_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE,
		common.AZURE_RG_RESOURCE_TYPE:

		// propagate child sensitivity to parent resource
		existingSensitivities := make(map[string]struct{})

		for _, s := range uniqueSensitivity {
			existingSensitivities[s] = struct{}{}
		}

		if childSensitivitiesVal, ok := accountSensitivity.Load(docID); ok {
			childSensitivities := childSensitivitiesVal.(map[string]struct{})
			for childSensitivity := range childSensitivities {
				resourceContextDoc.ResourceSensitivityTypes.DerivedSensitivity = append(resourceContextDoc.ResourceSensitivityTypes.DerivedSensitivity, context.GetContextItem(childSensitivity, common.CHILD_SENSITIVITY_TYPE, ""))

				if _, ok := existingSensitivities[childSensitivity]; !ok {
					uniqueSensitivity = append(uniqueSensitivity, childSensitivity)
				}
			}
		}

		// propagate child compliance to parent resource
		existingCompliances := make(map[string]struct{})
		for _, s := range uniqueCompliance {
			existingCompliances[s] = struct{}{}
		}

		if childCompliancesVal, ok := accountCompliance.Load(docID); ok {
			childCompliances := childCompliancesVal.(map[string]struct{})
			for childCompliance := range childCompliances {
				resourceContextDoc.ResourceComplianceTypes.DerivedCompliance = append(resourceContextDoc.ResourceComplianceTypes.DerivedCompliance, context.GetContextItem(childCompliance, common.CHILD_COMPLIANCE_TYPE, ""))

				if _, ok := existingCompliances[childCompliance]; !ok {
					uniqueCompliance = append(uniqueCompliance, childCompliance)
				}
			}
		}
	}
}

func PostProcessTeamsOfOwner(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, enhancedUniqueOwners, enhancedUniqueTeams *[]string) {

	for _, uniqueOwner := range *enhancedUniqueOwners {
		if addr, err := common.ParseAddress(uniqueOwner); err == nil {
			if userResource, ok := resourceContext.GetUserResource(addr.Address); ok {
				for team := range userResource.Team {
					if !slices.Contains(*enhancedUniqueTeams, team) {
						resourceContextDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextDoc.ResourceTeamTypes.DerivedTeam, context.GetContextItem(team, common.RESOURCE_OWNER_TEAM_TYPE, context.GetStaticDescriptionOfTeamType(common.RESOURCE_OWNER_TEAM_TYPE)+strings.TrimPrefix(uniqueOwner, contextutils.EX_EMPLOYEE_PREFIX), context.WithAdditional(map[string]any{"via": uniqueOwner})))

						*enhancedUniqueTeams = append(*enhancedUniqueTeams, team)
					}
				}
			}
		}
	}
}

func EnhanceContextItems(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueMap map[string][]string) {

	if len(uniqueMap[property.OWNER_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForOwner(resourceContext, resourceContextDoc, uniqueMap[property.OWNER_PROPERTY_NAME])
	}

	if len(uniqueMap[property.ENVIRONMENT_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForEnv(resourceContext, resourceContextDoc, uniqueMap[property.ENVIRONMENT_PROPERTY_NAME])
	}

	if len(uniqueMap[property.APP_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForApp(resourceContext, resourceContextDoc, uniqueMap[property.APP_PROPERTY_NAME])
	}

	if len(uniqueMap[property.SOFTWARE_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForSoftware(resourceContext, resourceContextDoc, uniqueMap[property.SOFTWARE_PROPERTY_NAME])
	}

	if len(uniqueMap[property.COMPLIANCE_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForCompliance(resourceContext, resourceContextDoc, uniqueMap[property.COMPLIANCE_PROPERTY_NAME])
	}

	if len(uniqueMap[property.SENSITIVITY_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForSensitivity(resourceContext, resourceContextDoc, uniqueMap[property.SENSITIVITY_PROPERTY_NAME])
	}

	if len(uniqueMap[property.COSTCENTER_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForCostCenter(resourceContext, resourceContextDoc, uniqueMap[property.COSTCENTER_PROPERTY_NAME])
	}

	if len(uniqueMap[property.TEAM_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForTeam(resourceContext, resourceContextDoc, uniqueMap[property.TEAM_PROPERTY_NAME])
	}

	if len(uniqueMap[property.DEPLOYMENT_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForDeployment(resourceContext, resourceContextDoc, uniqueMap[property.DEPLOYMENT_PROPERTY_NAME])
	}

	if len(uniqueMap[property.TTL_PROPERTY_NAME]) > 0 {
		context.EnhanceContextItemForTTL(resourceContext, resourceContextDoc, uniqueMap[property.TTL_PROPERTY_NAME])
	}
}
