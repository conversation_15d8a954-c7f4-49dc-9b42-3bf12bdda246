package postprocess

import (
	"encoding/json"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/identity"
	"github.com/precize/enhancer/internal/property"
	"github.com/precize/enhancer/neighbours"

	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/source/customer"
	"github.com/precize/evaluator/service"
	"github.com/precize/logger"
)

const (
	MAX_THREADS_STAGE3 = 10
)

func PostProcessThirdStage(resourceContext *rcontext.ResourceContext, resourcesHash []string, persistEnhancerData bool, lastCollectedAt string, currentTime time.Time, identitiesMap *sync.Map, identityCount *int32, identityMapLock *sync.RWMutex,
	resourceContextMap *sync.Map, rctxDocIDs []string) {

	if config.Environment == config.QA_ENV {
		neighbours.DeriveContextFromClosestNeighbours(resourceContext, resourcesHash, resourceContextMap)
	}

	var wg sync.WaitGroup
	sem := make(chan struct{}, MAX_THREADS_STAGE3)

	var (
		bulkResourceContextRequest, bulkCloudResourceRequest strings.Builder
		recordsCount                                         int
		bulkMutex                                            sync.Mutex
	)

	for _, docID := range rctxDocIDs {
		sem <- struct{}{}
		wg.Add(1)

		go func(docID string) {
			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occurred", r, docID)
					email.SendPanicEmail("enhancer")
				}
				wg.Done()
				<-sem
			}()

			var uniqueUsers = make([]string, 0, 20)

			resourceContextDoc, ok := resourceContext.GetResourceContextInsertDoc(docID)
			if !ok {
				return
			}

			if resourceContextDoc.SkipContext {
				owner := "Precize Support <" + strings.ToLower("<EMAIL>") + ">"
				resourceContextDoc.ResourceOwnerTypes.DefinedOwners = append(
					resourceContextDoc.ResourceOwnerTypes.DefinedOwners,
					context.GetContextItem(owner, common.PRECIZE_DETECTED_OWNER_TYPE, "", context.WithIdentity("<EMAIL>", "", common.IntPtr(common.VALID_IDENTITY_STATUS))),
				)

				resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
				resourceContextInsertDoc, err := json.Marshal(resourceContextDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling document", err)
					return
				}

				bulkMutex.Lock()
				bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
				bulkResourceContextRequest.WriteString("\n")
				bulkResourceContextRequest.Write(resourceContextInsertDoc)
				bulkResourceContextRequest.WriteString("\n")
				bulkMutex.Unlock()
			}

			dataEntry, ok := resourceContextMap.Load(docID)
			if !ok {
				logger.Print(logger.INFO, "No resource data found for docID", docID)
				return
			}
			rData := dataEntry.(*context.ContextUniqueList)

			getDataOrDefault := func(key string) []string {
				if val, ok := rData.Get(key); ok && len(val) > 0 {
					return val
				}
				return []string{"NONE"}
			}

			ownerList := getDataOrDefault(property.ENHANCED_OWNER_PROPERTY_NAME)
			envList := getDataOrDefault(property.ENVIRONMENT_PROPERTY_NAME)
			appList := getDataOrDefault(property.ENHANCED_APP_PROPERTY_NAME)
			softwareList := getDataOrDefault(property.SOFTWARE_PROPERTY_NAME)
			teamList := getDataOrDefault(property.ENHANCED_TEAM_PROPERTY_NAME)
			depList := getDataOrDefault(property.DEPLOYMENT_PROPERTY_NAME)
			compList := getDataOrDefault(property.COMPLIANCE_PROPERTY_NAME)
			sensList := getDataOrDefault(property.SENSITIVITY_PROPERTY_NAME)
			ccList := getDataOrDefault(property.COSTCENTER_PROPERTY_NAME)
			ttlList := getDataOrDefault(property.TTL_PROPERTY_NAME)
			uaList := getDataOrDefault(property.USER_AGENT_PROPERTY_NAME)

			uniqueMap := map[string][]string{
				property.OWNER_PROPERTY_NAME:       ownerList,
				property.ENVIRONMENT_PROPERTY_NAME: envList,
				property.APP_PROPERTY_NAME:         appList,
				property.SOFTWARE_PROPERTY_NAME:    softwareList,
				property.TEAM_PROPERTY_NAME:        teamList,
				property.DEPLOYMENT_PROPERTY_NAME:  depList,
				property.COMPLIANCE_PROPERTY_NAME:  compList,
				property.SENSITIVITY_PROPERTY_NAME: sensList,
				property.COSTCENTER_PROPERTY_NAME:  ccList,
				property.TTL_PROPERTY_NAME:         ttlList,
				property.USER_AGENT_PROPERTY_NAME:  uaList,
			}

			customer.RemoveCustomerEntityExcludeContextOfResource(resourceContext, &resourceContextDoc, uniqueMap)

			if !resourceContextDoc.SkipContext && !resourceContextDoc.IsDefaultResource {
				service.DeriveContextLabels(&resourceContextDoc)
			}

			relatedResourceList := make([]rcontext.RelatedResource, 0)
			if rList, ok := resourceContext.GetRelatedResourceList(docID); ok {
				relatedResourceList = rList
			}

			relatedResources, err := json.Marshal(relatedResourceList)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			commitInfo, err := json.Marshal(resourceContextDoc.CommitInfo)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			resourceContext.SetResourceContextInsertDoc(docID, resourceContextDoc)

			if persistEnhancerData {
				identity.ProcessResourceContextForIdentityCreation(
					identitiesMap,
					&resourceContextDoc,
					docID,
					&uniqueUsers,
					resourceContext,
					identityCount,
					identityMapLock,
				)
			}

			resourceContextInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			resourceContextInsertDoc, err := json.Marshal(resourceContextDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				return
			}

			cloudResourceUpdateMetadata := `{"update": {"_id": "` + resourceContextDoc.CloudResourceDocID + `"}}`

			var cloudResourceUpdateDoc strings.Builder
			cloudResourceUpdateDoc.WriteString(`{"doc":{"relatedResources":`)
			cloudResourceUpdateDoc.Write(relatedResources)
			cloudResourceUpdateDoc.WriteString(`, "contextLabels":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(resourceContextDoc.ContextLabels, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "owner":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(ownerList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "environment":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(envList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "app":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(appList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "software":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(softwareList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "deployment":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(depList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "compliance":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(compList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "sensitivity":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(sensList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "costCenter":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(ccList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "ttl":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(ttlList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "team":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(teamList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "users":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(uniqueUsers, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "stageCompleted":["dc","enhancer"], "userAgent":["`)
			cloudResourceUpdateDoc.WriteString(strings.Join(uaList, `","`))
			cloudResourceUpdateDoc.WriteString(`"], "extContext": {"commitInfo": `)
			cloudResourceUpdateDoc.Write(commitInfo)
			cloudResourceUpdateDoc.WriteString(`}}}`)

			bulkMutex.Lock()
			bulkResourceContextRequest.WriteString(resourceContextInsertMetadata)
			bulkResourceContextRequest.WriteString("\n")
			bulkResourceContextRequest.Write(resourceContextInsertDoc)
			bulkResourceContextRequest.WriteString("\n")

			bulkCloudResourceRequest.WriteString(cloudResourceUpdateMetadata)
			bulkCloudResourceRequest.WriteString("\n")
			bulkCloudResourceRequest.WriteString(cloudResourceUpdateDoc.String())
			bulkCloudResourceRequest.WriteString("\n")
			bulkMutex.Unlock()

			recordsCount++

			relatedResources = nil
			commitInfo = nil
			resourceContextInsertDoc = nil
		}(docID)
	}

	wg.Wait()
	close(sem)

	if recordsCount > 0 && persistEnhancerData {
		if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkResourceContextRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Resource context bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID}, lastCollectedAt)

		if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.CLOUD_RESOURCES_INDEX, bulkCloudResourceRequest.String()); err != nil {
			return
		}

		logger.Print(logger.INFO, "Cloud resource bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID}, lastCollectedAt)
	}
}
