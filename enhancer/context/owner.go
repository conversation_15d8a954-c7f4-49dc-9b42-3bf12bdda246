package context

import (
	"cmp"
	"encoding/json"
	"regexp"
	"slices"
	"strings"
	"sync"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/internal/confidence"
	emailutils "github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/internal/property"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func IsOwnerKey(tagKey string) bool {

	if _, ok := contextutils.OwnerTagKeys[strings.ToLower(tagKey)]; ok {
		return true
	}

	for k := range contextutils.OwnerTagKeys {
		if strings.Contains(strings.ToLower(tagKey), k) {
			return true
		}
	}

	for r := range contextutils.OwnerTagKeysRegex {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func isOwnerText(text, tenantID string) (isOwner bool) {

	nameList := map[string][]string{text: {""}}
	exampleNames := map[string]bool{
		"John Doe":    true,
		"Engineering": false,
		"Aniket":      true,
		"DevOps":      false,
	}

	resp := common.HumanOrNonHumanName(nameList, exampleNames, tenantID)
	for _, hasName := range resp {
		if hasName {
			isOwner = true
		}
	}

	return
}

func IsCreatorKey(tagKey string) bool {

	if _, ok := contextutils.CreatorTagKeys[strings.ToLower(tagKey)]; ok {
		return true
	}

	for k := range contextutils.CreatorTagKeys {
		if strings.Contains(strings.ToLower(tagKey), k) {
			return true
		}
	}

	for r := range contextutils.CreatorTagKeysRegex {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func GetStaticDescriptionOfUserType(userType string) (desc string) {

	switch userType {

	case common.ACCOUNT_OWNER_TYPE:
		desc = "User has been assigned the owner of the account"
	case common.ACCOUNT_CONTACT_OWNER_TYPE:
		desc = "User has been assigned as an alternate contact for the account"
	case common.PROJECT_OWNER_TYPE:
		desc = "User has owner or equivalent role in the project"
	case common.ORG_OWNER_TYPE:
		desc = "User has owner or equivalent role in the organization"
	case common.FOLDER_OWNER_TYPE:
		desc = "User has owner or equivalent role in the folder"
	case common.PARENTFOLDER_OWNER_TYPE:
		desc = "User has owner or equivalent role in a parent folder"
	case common.SUBSCRIPTION_CONTACT_OWNER_TYPE:
		desc = "User has been assigned as an alternate contact for the subscription"
	case common.NO_CHILD_OWNERS_PARENT_OWNER_TYPE:
		desc = "Account owner(s) is the sole user of all resources under it"
	case common.FEW_CHILD_OWNERS_PARENT_OWNER_TYPE:
		desc = "Account is being used only by 1 or 2 users"
	case common.RELATED_RESOURCE_OWNER_TYPE:
		desc = "User owns one or more resources related to this resource"
	case common.RESOURCE_NAME_OWNER_TYPE:
		desc = "Resource is named after the user"
	case common.RESOURCE_TYPE_OWNER_TYPE:
		desc = "Service is being managed mostly by this user in this account"
	case common.RESOURCE_OWNER_TYPE:
		desc = "User has been specified as an owner in resource properties"
	case common.RESOURCE_OWNERAPP_OWNER_TYPE:
		desc = "Application has been specified as an owner in resource properties"
	case common.RESOURCE_OWNERIAMROLE_OWNER_TYPE:
		desc = "IAM Role has been specified as an owner in resource properties"
	case common.OPENAIPROJECT_OWNER_TYPE:
		desc = "User has been assigned the owner of the project"
	case common.OPENAIORG_OWNER_TYPE:
		desc = "User has been assigned the owner of the organization"
	case common.CUSTOMER_DEFINED_OWNER_TYPE:
		desc = "User has been assigned as the owner from Precize console"
	case common.ORG_ACCOUNT_OWNER_TYPE:
		desc = "User has been assigned the owner of the organization"
	case common.PRECIZE_DEFINED_OWNER_TYPE:
		desc = "User has been derived as the owner by Precize"
	case common.RESOURCE_OPS_CONTACT_OWNER_TYPE:
		desc = "User has been assigned as the ops contact for the resource"
	case common.PRECIZE_DETECTED_OWNER_TYPE:
		desc = "Precize detected this resource"
	case common.MANAGER_OWNER_TYPE:
		desc = "User is the manager of this identity"
	case common.NEIGHBOUR_OWNER_TYPE:
		desc = "User is the owner of a close neighbour resource"
	}

	return
}

var SortUsername = func(a, b common.ResourceContextItem) int {

	if (!strings.HasPrefix(a.Name, contextutils.EX_EMPLOYEE_PREFIX) && strings.HasPrefix(b.Name, contextutils.EX_EMPLOYEE_PREFIX)) || (!strings.HasPrefix(a.Name, contextutils.INVALID_EMPLOYEE_PREFIX) && strings.HasPrefix(b.Name, contextutils.INVALID_EMPLOYEE_PREFIX)) {
		return -1
	} else if (strings.HasPrefix(a.Name, contextutils.EX_EMPLOYEE_PREFIX) && !strings.HasPrefix(b.Name, contextutils.EX_EMPLOYEE_PREFIX)) || (strings.HasPrefix(a.Name, contextutils.INVALID_EMPLOYEE_PREFIX) && !strings.HasPrefix(b.Name, contextutils.INVALID_EMPLOYEE_PREFIX)) {
		return 1
	}

	notEmailA, _ := common.ParseAddress(a.Name)
	notEmailB, _ := common.ParseAddress(b.Name)

	if notEmailA != nil && notEmailB == nil {
		return 1
	} else if notEmailA == nil && notEmailB != nil {
		return -1
	}

	return cmp.Compare(strings.ToLower(a.Name), strings.ToLower(b.Name))
}

var SortUsernameString = func(a, b string) int {

	if (!strings.HasPrefix(a, contextutils.EX_EMPLOYEE_PREFIX) && strings.HasPrefix(b, contextutils.EX_EMPLOYEE_PREFIX)) || (!strings.HasPrefix(a, contextutils.INVALID_EMPLOYEE_PREFIX) && strings.HasPrefix(b, contextutils.INVALID_EMPLOYEE_PREFIX)) {
		return -1
	} else if (strings.HasPrefix(a, contextutils.EX_EMPLOYEE_PREFIX) && !strings.HasPrefix(b, contextutils.EX_EMPLOYEE_PREFIX)) || (strings.HasPrefix(a, contextutils.INVALID_EMPLOYEE_PREFIX) && !strings.HasPrefix(b, contextutils.INVALID_EMPLOYEE_PREFIX)) {
		return 1
	}

	notEmailA, _ := common.ParseAddress(a)
	notEmailB, _ := common.ParseAddress(b)

	if notEmailA != nil && notEmailB == nil {
		return 1
	} else if notEmailA == nil && notEmailB != nil {
		return -1
	}

	return cmp.Compare(strings.ToLower(a), strings.ToLower(b))
}

func GetInheritedOwners(parentDoc common.ResourceContextInsertDoc, parentType string) (inheritedOwners []common.ResourceContextItem) {

	for _, parentDefined := range parentDoc.DefinedOwners {

		inheritedOwners = append(inheritedOwners, GetContextItem(parentDefined.Name, "Parent "+common.ConvertToTitleCase(parentType)+" Defined Owner", "User is the owner of the parent "+common.ConvertToTitleCase(parentType), WithAdditional(map[string]any{"inherited": true})))
	}

	if len(parentDoc.ResourceName) > 0 {
		inheritedOwners = append(inheritedOwners, GetContextItem(parentDoc.ResourceName+contextutils.PLACEHOLDER_USER_SUFFIX, "Parent "+common.ConvertToTitleCase(parentType)+" Derived Owner", "User is the owner of the parent "+common.ConvertToTitleCase(parentType), WithAdditional(map[string]any{"inherited": true})))
	}

	for _, parentDerived := range parentDoc.DerivedOwners {

		inheritedOwners = append(inheritedOwners, GetContextItem(parentDerived.Name, "Parent "+common.ConvertToTitleCase(parentType)+" Derived Owner", "User is the owner of the parent "+common.ConvertToTitleCase(parentType), WithAdditional(map[string]any{"inherited": true})))
	}

	return
}

func GetInheritedOwnersForDefaultResources(parentDoc common.ResourceContextInsertDoc, parentType string) (inheritedOwners []common.ResourceContextItem) {

	for _, parentDefined := range parentDoc.DefinedOwners {

		inheritedOwners = append(inheritedOwners, GetContextItem(parentDefined.Name, "Parent "+common.ConvertToTitleCase(parentType)+" Defined Owner", "User owns the parent "+strings.ToLower(parentType)+" of this default resource created by the service provider", WithAdditional(map[string]any{"default": true})))
	}

	if len(parentDoc.ResourceName) > 0 {
		inheritedOwners = append(inheritedOwners, GetContextItem(parentDoc.ResourceName+contextutils.PLACEHOLDER_USER_SUFFIX, "Parent "+common.ConvertToTitleCase(parentType)+" Derived Owner", "User owns the parent "+strings.ToLower(parentType)+" of this default resource created by the service provider", WithAdditional(map[string]any{"default": true})))
	}

	for _, parentDerived := range parentDoc.DerivedOwners {

		inheritedOwners = append(inheritedOwners, GetContextItem(parentDerived.Name, "Parent "+common.ConvertToTitleCase(parentType)+" Derived Owner", "User owns the parent "+strings.ToLower(parentType)+" of this default resource created by the service provider", WithAdditional(map[string]any{"default": true})))
	}

	return
}

func sortInheritedOwners(inheritedOwners []common.ResourceContextItem) []common.ResourceContextItem {

	var (
		defined, derived []common.ResourceContextItem
	)

	for _, inheritedOwner := range inheritedOwners {
		if strings.Contains(inheritedOwner.Type, "Defined") {
			defined = append(defined, inheritedOwner)
		} else {
			derived = append(derived, inheritedOwner)
		}
	}

	slices.SortFunc(defined, SortUsername)

	return append(defined, derived...)
}

func IncrementParentChildOwnerCount(resourceContext *rcontext.ResourceContext, owner, accountID string) {

	if len(accountID) > 0 {
		var ownerCount map[string]int
		if eOwnerCount, ok := resourceContext.GetParentChildOwner(accountID); ok {
			ownerCount = eOwnerCount
		} else {
			ownerCount = make(map[string]int)
		}

		ownerCount[owner]++
		resourceContext.SetParentChildOwner(accountID, ownerCount)
	}
}

func GetMaxActivityOwnersOfResourceType(resourceTypeOwners map[string]map[string]int, resourceType string, resourceTypeCount int) (owners []string, max int) {

	if typeOwners, ok := resourceTypeOwners[resourceType]; ok {

		for ownerKey, count := range typeOwners {
			if count > max {
				max = count
				owners = []string{ownerKey}
			} else if count == max {
				owners = append(owners, ownerKey)
			}
		}
	}

	if max <= resourceTypeCount/100 {
		// if less than 1 percent of total count of resources in that resource type
		owners = []string{}
		return
	}

	slices.SortFunc(owners, SortUsernameString)

	// Upto two max owners allowed in case of tie
	if len(owners) > 2 {
		owners = []string{}
	}

	return
}

func IncrementResourceTypeOwnerCount(resourceContext *rcontext.ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, uniqueOwners []string) {
	if len(resourceContextDoc.Account) > 0 {

		resourceTypeOwnerAccMap, ok := resourceContext.GetResourceTypeOwner(resourceContextDoc.Account)
		if !ok {
			resourceTypeOwnerAccMap = make(map[string]map[string]int)
			resourceContext.SetResourceTypeOwner(resourceContextDoc.Account, resourceTypeOwnerAccMap)
		}

		if len(resourceContextDoc.ResourceType) > 0 {
			if _, ok := resourceTypeOwnerAccMap[resourceContextDoc.ResourceType]; !ok {
				resourceTypeOwnerAccMap[resourceContextDoc.ResourceType] = make(map[string]int)
			}

			for _, derivedOwner := range resourceContextDoc.ResourceOwnerTypes.DerivedOwners {
				if derivedOwner.Type == common.ACTIVITY_OWNER_TYPE {
					if slices.Contains(uniqueOwners, derivedOwner.Name) {
						resourceTypeOwnerAccMap[resourceContextDoc.ResourceType][derivedOwner.Name]++
					}
				}
			}

			// Store the updated map back to the concurrent map
			resourceContext.SetResourceTypeOwner(resourceContextDoc.Account, resourceTypeOwnerAccMap)
		}
	}
}

type ownerListParams struct {
	owners           []string
	uniqueOwners     map[string]int
	reassignedOwners []common.ResourceContextItem
}

func PostProcessOwners(resourceContextDoc *common.ResourceContextInsertDoc, rCtx *rcontext.ResourceContext) []string {

	var (
		ownerList  ownerListParams
		ownerTypes = resourceContextDoc.ResourceOwnerTypes
	)

	ownerList.uniqueOwners = make(map[string]int)

	for i := range ownerTypes.DefinedOwners {
		rctxItem := ownerTypes.DefinedOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, true, false, resourceContextDoc.Account, resourceContextDoc.ResourceID)
		ownerTypes.DefinedOwners[i] = rctxItem
	}
	resourceutils.GetUniqueContext(&ownerTypes.DefinedOwners, nil)
	slices.SortFunc(ownerTypes.DefinedOwners, SortUsername)
	slices.SortFunc(ownerList.owners, SortUsernameString)

	for i := range ownerTypes.CodeOwners {
		rctxItem := ownerTypes.CodeOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, true, false, resourceContextDoc.Account, resourceContextDoc.ResourceID)
		ownerTypes.CodeOwners[i] = rctxItem
	}
	resourceutils.GetUniqueContext(&ownerTypes.CodeOwners, nil)

	for i := range ownerTypes.DerivedOwners {
		rctxItem := ownerTypes.DerivedOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, true, false, resourceContextDoc.Account, resourceContextDoc.ResourceID)
		ownerTypes.DerivedOwners[i] = rctxItem
	}
	resourceutils.GetUniqueContext(&ownerTypes.DerivedOwners, nil)

	for i := range ownerTypes.InheritedOwners {

		var isOwner bool

		if strings.Contains(ownerTypes.InheritedOwners[i].Type, common.AZURE_RG_RESOURCE_TYPE) {
			isOwner = true
		}

		rctxItem := ownerTypes.InheritedOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, isOwner, true, resourceContextDoc.Account, resourceContextDoc.ResourceID)
		ownerTypes.InheritedOwners[i] = rctxItem
	}
	resourceutils.GetUniqueContext(&ownerTypes.InheritedOwners, nil)
	ownerTypes.InheritedOwners = sortInheritedOwners(ownerTypes.InheritedOwners)

	for i := range ownerTypes.CostOwners {
		rctxItem := ownerTypes.CostOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, false, false, resourceContextDoc.Account, resourceContextDoc.ResourceID)
		ownerTypes.CostOwners[i] = rctxItem
	}
	resourceutils.GetUniqueContext(&ownerTypes.CostOwners, nil)

	for i := range ownerTypes.SecurityOwners {
		rctxItem := ownerTypes.SecurityOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, false, false, resourceContextDoc.Account, resourceContextDoc.ResourceID)
		ownerTypes.SecurityOwners[i] = rctxItem
	}
	resourceutils.GetUniqueContext(&ownerTypes.SecurityOwners, nil)

	for i := range ownerTypes.OpsOwners {
		rctxItem := ownerTypes.OpsOwners[i]
		rctxItem.Name = processAndSetUniqueOwner(&rctxItem, &ownerList, rCtx, false, false, resourceContextDoc.Account, resourceContextDoc.ResourceID)
		ownerTypes.OpsOwners[i] = rctxItem
	}
	resourceutils.GetUniqueContext(&ownerTypes.OpsOwners, nil)

	if len(resourceContextDoc.ResourceName) > 0 {

		isOwner := true

		switch resourceContextDoc.ResourceType {
		case common.AZURE_ADUSER_RESOURCE_TYPE, common.AWS_SSOUSER_RESOURCE_TYPE, common.GCP_IAM_RESOURCE_TYPE, common.AWS_ROOTUSER_RESOURCE_TYPE, common.AZURE_USEROWNER_RESOURCE_TYPE:
			isOwner = false

		case common.AWS_IAM_USER_RESOURCE_TYPE:
			if !resourceContextDoc.HasConsoleLogin {
				// IAM Identities without login should be added to users list
				isOwner = true
			} else {
				// Don't add as owner, as the person the identity is for, might not be the one incharge of that resource
				isOwner = false
			}
		}

		rctxItem := common.ResourceContextItem{Name: resourceContextDoc.ResourceName + contextutils.RESOURCE_NAME_TEMP_USER_SUFFIX}

		derivedOwnerFromResourceName := processAndSetUniqueOwner(
			&rctxItem,
			&ownerList, rCtx, isOwner, false, resourceContextDoc.Account, resourceContextDoc.ResourceID)

		if len(derivedOwnerFromResourceName) > 0 {
			ownerTypes.DerivedOwners = append([]common.ResourceContextItem{
				GetContextItem(derivedOwnerFromResourceName, common.RESOURCE_NAME_OWNER_TYPE, "", WithIdentity(rctxItem.IdentityId, rctxItem.ChildIdentityID, nil)),
			}, ownerTypes.DerivedOwners...)
		}
	}

	resourceutils.GetUniqueContext(&ownerList.reassignedOwners, nil)
	ownerTypes.DerivedOwners = append(ownerTypes.DerivedOwners, ownerList.reassignedOwners...)

	resourceContextDoc.ResourceOwnerTypes = ownerTypes

	return ownerList.owners
}

func processAndSetUniqueOwner(rscContextItem *common.ResourceContextItem, ownerList *ownerListParams,
	r *rcontext.ResourceContext, isOwner, isInherited bool, accountID, resourceID string) string {

	var (
		ok, placeholderUser, resourceNameUser bool
		userResource                          *rcontext.UserContext
		name                                  = rscContextItem.Name
		nameBeforeProcessing                  = name
		includedOwner                         bool
		partialMatchValue                     rcontext.PartialNameCache
	)

	if len(name) <= 0 {
		return name
	}

	if addr, err := common.ParseAddress(name); err == nil {

		// replace partner identity with parent identity
		if primaryEmail, ok := r.GetChildPrimaryEmail(rscContextItem.IdentityId); ok {
			rscContextItem.ChildIdentityID = rscContextItem.IdentityId
			addr.Address = primaryEmail
			rscContextItem.IdentityId = primaryEmail
		} else if primaryEmail, ok := r.GetChildPrimaryEmail(addr.Address); ok {
			rscContextItem.ChildIdentityID = addr.Address
			addr.Address = primaryEmail
			rscContextItem.IdentityId = primaryEmail
		}

		if len(addr.Name) <= 0 {
			if userResource, ok = r.GetUserResource(addr.Address); ok && len(userResource.Name) > 0 {
				name = emailutils.FormCompleteEmailFormat(userResource.Name, userResource.Email)
			} else {
				identityName := common.GetFormattedNameFromEmail(addr.Address)
				name = emailutils.FormCompleteEmailFormat(identityName, addr.Address)
			}
		} else {
			if userResource, ok = r.GetUserResource(addr.Address); ok && len(userResource.Name) > 0 {
				// Already complete email format
			}
		}
	} else {

		if strings.HasSuffix(name, contextutils.RESOURCE_NAME_TEMP_USER_SUFFIX) {
			name = strings.TrimSuffix(name, contextutils.RESOURCE_NAME_TEMP_USER_SUFFIX)
			resourceNameUser = true
		}

		if strings.HasSuffix(name, contextutils.PLACEHOLDER_USER_SUFFIX) {
			// Placeholder to replace/ignore for resource name user of dependent resource
			name = strings.TrimSuffix(name, contextutils.PLACEHOLDER_USER_SUFFIX)
			placeholderUser = true
		}

		if strings.HasSuffix(name, contextutils.IAM_USER_SUFFIX) || strings.HasSuffix(name, contextutils.APP_USER_SUFFIX) ||
			strings.HasSuffix(name, contextutils.IAM_ROLE_SUFFIX) || strings.HasSuffix(name, contextutils.SERVICEACCOUNT_USER_SUFFIX) ||
			strings.HasSuffix(name, contextutils.ACCOUNT_USER_SUFFIX) || strings.HasSuffix(name, contextutils.AWSSERVICE_USER_SUFFIX) {

			if isInherited {
				return ""
			}

			return name
		}

		loweredName := strings.ToLower(name)
		cleanedName := common.RemoveSpecialCharactersFromString(strings.ToLower(loweredName))

		// Partial match
		if fetchedFromCache, ok := r.GetPartialNameMatch(cleanedName); !ok {

			var (
				matchedUsrRscKey    string
				matchedLength       int
				matchedFirstNameHit bool
				multiMatch          bool
			)

			r.RangeUserResources(func(userResourceKey string, usrRsrc *rcontext.UserContext) bool {

				if !strings.HasPrefix(userResourceKey, contextutils.NAME_ONLY_PREFIX) { // Want to match only emails first

					if (resourceNameUser || placeholderUser) && !usrRsrc.IsUser {
						// Resource named after user should only match human users
						return true
					}

					// for cloud users (ad, sso , iam etc) if email is already present do not match to a different person with a different email
					if addr, err := common.ParseAddress(resourceID); err == nil {
						if addr.Address != userResourceKey {
							return true
						}
					}

					for userDepartment := range usrRsrc.Department {
						if _, ok := contextutils.ExcludedDepartments[strings.ToLower(userDepartment)]; ok {
							for userJobTitle := range usrRsrc.JobTitle {
								cloudUser := false
								for _, keyword := range contextutils.TechKeywords {
									if strings.Contains(strings.ToLower(userJobTitle), keyword) {
										cloudUser = true
										break
									}
								}

								if !cloudUser {
									return true
								}
							}
						}
					}

					var (
						currentLength       int
						currentFirstNameHit bool
						userNameSplit       = common.UniqueSplitStringBySpecialCharacters(usrRsrc.Name)
					)

					// Check if each word in name can match

					for i, splitUser := range userNameSplit {

						splitUser = strings.ToLower(splitUser)

						if ownerMatchIncluded(r, splitUser, cleanedName, loweredName) {
							currentLength += len(splitUser)
							currentFirstNameHit = currentFirstNameHit || i == 0
							includedOwner = true
							continue
						}

						if len(splitUser) >= 5 && len(cleanedName) >= 5 {
							if strings.Contains(cleanedName, splitUser) {
								// Direct contains match if word length >= 5

								// jo_hnbucket should not be matched to john
								// john-bucket should be matched to john
								splitResourceName := common.SplitStringBySpecialCharacters(loweredName)
								properMatch := false

								for _, splitResource := range splitResourceName {
									if strings.Contains(splitResource, splitUser) {
										properMatch = true
										break
									}
								}

								if !properMatch {
									continue
								}

								if ownerMatchExcluded(r, splitUser, cleanedName) {
									continue
								}

								currentLength += len(splitUser)
								currentFirstNameHit = currentFirstNameHit || i == 0

							} else if strings.Contains(splitUser, cleanedName) {
								if ownerMatchExcluded(r, splitUser, cleanedName) {
									continue
								}

								currentLength += len(cleanedName)
								currentFirstNameHit = currentFirstNameHit || i == 0
							}
						} else if len(splitUser) >= 3 && len(cleanedName) >= 3 {
							// If 3-5 letter word check for exact match of word in b/w special characters
							splitName := common.SplitByNonAlphaNumericEfficient(strings.ToLower(name))

							for _, partialName := range splitName {
								if splitUser == partialName {
									if ownerMatchExcluded(r, splitUser, partialName) {
										continue
									}

									currentLength += len(splitUser)
									currentFirstNameHit = currentFirstNameHit || i == 0
									break
								}

								if currentLength <= 0 {
									// only if above check fails check for the abbreviated pattern eg. johnD, jDoe, etc
									if strings.Contains(partialName, splitUser) || strings.Contains(splitUser, partialName) {
										if checkAbbreviationPattern(splitUser, partialName, userNameSplit, i) {
											if ownerMatchExcluded(r, splitUser, partialName) {
												continue
											}

											currentLength += len(splitUser)
											currentFirstNameHit = currentFirstNameHit || i == 0
											break
										}
									}
								}
							}
						}
					}

					if currentLength <= 0 {
						// Check for exact match of emailName in b/w special characters
						splitName := common.SplitByNonAlphaNumericEfficient(strings.ToLower(name))
						emailName := common.GetEmailNameWithoutSpecialCharacters(userResourceKey)

						if slices.Contains(splitName, emailName) {
							currentLength += len(userResourceKey)
							currentFirstNameHit = true
						}
					}

					comparator := compareCurrentUserWithMatchedUser(r, name, userResourceKey, matchedUsrRscKey, currentLength, matchedLength, currentFirstNameHit, matchedFirstNameHit, accountID, ownerList.owners)

					switch comparator {
					case 1:
						matchedLength = currentLength
						matchedUsrRscKey = userResourceKey
						matchedFirstNameHit = currentFirstNameHit
						multiMatch = false
					case 2:
						multiMatch = true
					}
				}

				return true
			})

			if len(matchedUsrRscKey) > 0 {

				// if matchedUsrRscKey email has a primary email replace with that
				if primaryEmail, ok := r.GetChildPrimaryEmail(matchedUsrRscKey); ok {
					if _, ok := r.GetUserResource(primaryEmail); ok {
						matchedUsrRscKey = primaryEmail
					}
				}

				if userResource, ok = r.GetUserResource(matchedUsrRscKey); ok {
					// Handle the case where there's no multiMatch or there is multi match the userResource is Sddl
					if !multiMatch || (multiMatch && userResource.IsSddl) {

						// replace partner identity with parent identity
						if primaryEmail, ok := r.GetChildPrimaryEmail(userResource.Email); ok {

							if tempUsr, ok := r.GetUserResource(primaryEmail); ok {

								userResource = tempUsr
								rscContextItem.ChildIdentityID = rscContextItem.IdentityId
								rscContextItem.IdentityId = primaryEmail
								matchedUsrRscKey = primaryEmail

							}
						}

						name = emailutils.FormCompleteEmailFormat(userResource.Name, userResource.Email)
						partialMatchValue = rcontext.PartialNameCache{Name: name, UserResourceKey: matchedUsrRscKey}
						r.SetPartialNameMatch(cleanedName, partialMatchValue)
					} else {
						logger.Print(logger.INFO, "Multi match confirmed", name)
						partialMatchValue = rcontext.PartialNameCache{Name: "", UserResourceKey: ""}
						r.SetPartialNameMatch(cleanedName, partialMatchValue)
						return ""
					}
				}
			} else if userResource, ok = r.GetUserResource(contextutils.NAME_ONLY_PREFIX + strings.ToLower(name)); ok {
				partialMatchValue = rcontext.PartialNameCache{Name: name, UserResourceKey: ""}
				r.SetPartialNameMatch(cleanedName, partialMatchValue)
			} else {
				partialMatchValue = rcontext.PartialNameCache{Name: "", UserResourceKey: ""}
				r.SetPartialNameMatch(cleanedName, partialMatchValue)
				return ""
			}
		} else if len(fetchedFromCache.Name) > 0 && len(fetchedFromCache.UserResourceKey) > 0 {
			name = fetchedFromCache.Name
			userResource, _ = r.GetUserResource(fetchedFromCache.UserResourceKey)

		} else if len(fetchedFromCache.Name) > 0 {
			name = fetchedFromCache.Name
		} else {
			return ""
		}

	}

	if userResource != nil && (len(userResource.Email) > 0 && (!userResource.Active || userResource.IsInvalid)) {

		if userResource.IsInvalid {
			name = contextutils.INVALID_EMPLOYEE_PREFIX + name
		} else {
			name = contextutils.EX_EMPLOYEE_PREFIX + name
		}

		// Reassigned user for resource

		if isOwner {

			for _, reassigned := range userResource.Reassigned {
				ownerList.reassignedOwners = append(ownerList.reassignedOwners, reassigned)
				if _, ok := ownerList.uniqueOwners[reassigned.Name]; !ok {
					ownerList.uniqueOwners[reassigned.Name]++
					ownerList.owners = append(ownerList.owners, reassigned.Name)
				}
			}
		}
	}

	trimmedPlaceholderUser := strings.TrimSuffix(nameBeforeProcessing, contextutils.PLACEHOLDER_USER_SUFFIX)
	if userResource != nil && (placeholderUser && (name == trimmedPlaceholderUser || !userResource.IsUser || !common.IsValidOwnerAttribution(trimmedPlaceholderUser, name, r.TenantID, includedOwner))) {
		if name != trimmedPlaceholderUser {
			logger.Print(logger.INFO, "Placeholder user match rejected", trimmedPlaceholderUser, name)
		}
		return ""
	}

	trimmedResourceNameUser := strings.TrimSuffix(nameBeforeProcessing, contextutils.RESOURCE_NAME_TEMP_USER_SUFFIX)
	if userResource != nil && (resourceNameUser && (name == trimmedResourceNameUser || !userResource.IsUser || !common.IsValidOwnerAttribution(trimmedResourceNameUser, name, r.TenantID, includedOwner))) {
		if name != trimmedResourceNameUser {
			logger.Print(logger.INFO, "Resource name match rejected", trimmedResourceNameUser, name)
		}
		return ""
	}

	if len(partialMatchValue.Name) > 0 {
		logger.Print(logger.INFO, "Partial name matched", nameBeforeProcessing, partialMatchValue)
	}

	if userResource != nil && (isInherited && !userResource.IsUser) {
		return ""
	}

	if userResource != nil && (len(userResource.Email) > 0 && !userResource.IsUser) {
		isOwner = false
	}

	if isOwner {

		if resourceNameUser {
			// Resource name user owner should come in the beginning.
			// Delete if already exists somewhere else in the list
			if slices.Contains(ownerList.owners, name) && ownerList.owners[0] != name {
				ownerList.owners = slices.DeleteFunc(ownerList.owners, func(s string) bool {
					return s == name
				})
			}

			rscContextItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			ownerList.owners = append([]string{name}, ownerList.owners...)

		} else if _, ok := ownerList.uniqueOwners[name]; !ok {
			rscContextItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
			ownerList.uniqueOwners[name] = 0
			ownerList.owners = append(ownerList.owners, name)
		}
	}

	return name
}

func ownerMatchIncluded(r *rcontext.ResourceContext, str1, str2, resourceNameUnmodified string) bool {

	if vals, ok := r.GetOwnerInclusions(str1); ok {
		for _, val := range vals {
			if len(val) <= 3 {
				// For small values, check if word is a whole word match
				if common.MatchWordBoundary(val, resourceNameUnmodified) {
					return true
				}
			} else {
				if strings.Contains(str2, val) || strings.Contains(val, str2) {
					return true
				}
			}
		}
	}

	return false
}

func ownerMatchExcluded(r *rcontext.ResourceContext, str1, str2 string) bool {

	if len(GetAppNameFromValue(str1)) > 0 {
		logger.Print(logger.INFO, "Excluded owner match because of app name", str1, str2)
		return true
	}

	if len(GetSoftwareNameFromValue(str1)) > 0 {
		logger.Print(logger.INFO, "Excluded owner match because of software name", str1, str2)
		return true
	}

	if vals, ok := r.GetOwnerExceptions(str1); ok {
		for _, val := range vals {
			if strings.Contains(str2, val) {
				return true
			}
		}
	}

	return false
}

func compareCurrentUserWithMatchedUser(r *rcontext.ResourceContext, name, currentUsrRscKey string, matchedUsrRscKey string, currentLength int, matchedLength int, currentFirstNameHit bool, matchedFirstNameHit bool, currentRscAccID string, owners []string) int {

	// 0 is no change - matched remains
	// 1 is change - current replaces
	// 2 is multimatch

	if currentLength == 0 {
		return 0
	}

	if matchedLength == 0 {
		return 1
	}

	matchedUserResource, _ := r.GetUserResource(matchedUsrRscKey)
	currentUserResource, _ := r.GetUserResource(currentUsrRscKey)

	// if one of the users is already an owner of the resource prefer that over the other
	if currentUserResource != nil && matchedUserResource != nil {
		for _, owner := range owners {
			if addr, err := common.ParseAddress(owner); err == nil {

				switch addr.Address {
				case currentUserResource.Email:
					logger.Print(logger.INFO, "User match changed due to existing owner", name, currentUsrRscKey, matchedUsrRscKey)
					return 1
				case matchedUserResource.Email:
					logger.Print(logger.INFO, "User match persisted due to existing owner", name, matchedUsrRscKey, currentUsrRscKey)
					return 0
				}
			}
		}
	}

	// If one is human and the other is not

	if currentUserResource != nil && matchedUserResource != nil {
		if currentUserResource.IsUser && !matchedUserResource.IsUser {
			logger.Print(logger.INFO, "User match changed due to human preference", name, currentUsrRscKey, matchedUsrRscKey)
			return 1
		} else if matchedUserResource.IsUser && !currentUserResource.IsUser {
			logger.Print(logger.INFO, "User match persisted due to human preference", name, matchedUsrRscKey, currentUsrRscKey)
			return 0
		}
	}

	// If match length is more
	if currentLength > matchedLength {
		logger.Print(logger.INFO, "User match changed due to more character match", name, currentUsrRscKey, matchedUsrRscKey)
		return 1
	} else if matchedLength > currentLength {
		logger.Print(logger.INFO, "User match persisted due to more character match", name, matchedUsrRscKey, currentUsrRscKey)
		return 0
	}

	// If one matches first name and the other doesn't
	if currentFirstNameHit && !matchedFirstNameHit {
		logger.Print(logger.INFO, "User match changed due to first name preference", name, currentUsrRscKey, matchedUsrRscKey)
		return 1
	} else if matchedFirstNameHit && !currentFirstNameHit {
		logger.Print(logger.INFO, "User match persisted due to first name preference", name, matchedUsrRscKey, currentUsrRscKey)
		return 0
	}

	var (
		currentUsrRscKeyPriority, matchedUsrRscKeyPriority int
	)

	for i, primaryDomain := range r.PrimaryDomains {
		if strings.HasSuffix(currentUsrRscKey, primaryDomain) {
			currentUsrRscKeyPriority = len(r.PrimaryDomains) - i
		}

		if strings.HasSuffix(matchedUsrRscKey, primaryDomain) {
			matchedUsrRscKeyPriority = len(r.PrimaryDomains) - i
		}
	}

	if currentUsrRscKeyPriority > matchedUsrRscKeyPriority {
		// If one is primary domain and another is not or lesser priority primary domain
		logger.Print(logger.INFO, "User match changed due to higher priority domain", name, currentUsrRscKey, matchedUsrRscKey)
		return 1
	} else if matchedUsrRscKeyPriority > currentUsrRscKeyPriority {
		logger.Print(logger.INFO, "User match persisted due to higher priority domain", name, matchedUsrRscKey, currentUsrRscKey)
		return 0
	}

	if currentUserResource != nil && matchedUserResource != nil {

		_, matchedUscRscAccountMatch := matchedUserResource.GetActiveAccounts(currentRscAccID)
		_, currentUscRscAccountMatch := currentUserResource.GetActiveAccounts(currentRscAccID)

		if matchedUscRscAccountMatch && !currentUscRscAccountMatch {
			// If the matched user is active in the same account as the resource
			logger.Print(logger.INFO, "Multi match avoided as matched user is active in the same account as the resource", name, matchedUsrRscKey, currentRscAccID)
			return 0
		} else if currentUscRscAccountMatch && !matchedUscRscAccountMatch {
			// If the current user is active in the same account as the resource
			logger.Print(logger.INFO, "Multi match avoided as current user is active in the same account", name, currentUsrRscKey, currentRscAccID)
			return 1
		}
	}

	logger.Print(logger.INFO, "Potential multi match", name, currentUsrRscKey, matchedUsrRscKey)
	return 2
}

func GetNamesFromDescription(desc, tenantID string) []string {

	namesList := make([]string, 0)
	namesStr := common.DeriveNamesStringFromDescription(desc, tenantID)

	if namesStr == "" {
		return namesList
	}

	namesList = strings.Split(namesStr, ",")
	for i, name := range namesList {
		name = strings.Trim(name, " ")
		namesList[i] = name
	}

	return namesList
}

func PostProcessServiceIdentities(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, uniqueOwners []string, enhancedUniqueOwners *[]string, resourceContextMap *sync.Map) {

	switch resourceContextDoc.ServiceID {

	case common.GCP_SERVICE_ID_INT:

		if resourceContextDoc.ResourceType == common.GCP_SERVICEACCOUNT_RESOURCE_TYPE || resourceContextDoc.ResourceType == common.GCP_SAPOLICYBINDING_RESOURCE_TYPE {
			serviceAccountContextID := common.GenerateCombinedHashID(resourceContextDoc.ResourceID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)
			if resourceContextDoc.ResourceType == common.GCP_SAPOLICYBINDING_RESOURCE_TYPE {
				serviceAccountContextID = common.GenerateCombinedHashID(resourceContextDoc.ResourceID, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)
			}
			if relatedResources, ok := resourceContext.GetRelatedResourceList(serviceAccountContextID); ok {
				if len(relatedResources) <= 2 {
					for _, relatedResource := range relatedResources {
						if relatedResource.ResourceType != common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE &&
							relatedResource.ResourceType != common.GCP_SERVICEACCOUNT_RESOURCE_TYPE &&
							relatedResource.ResourceType != common.GCP_SAPOLICYBINDING_RESOURCE_TYPE {

							if len(uniqueOwners) > 0 {

								attachedResourcePrimaryOwner := uniqueOwners[0]

								rctxItem := GetContextItem(attachedResourcePrimaryOwner, common.ATTACHEDRESOURCE_OWNER_TYPE, "User owns a resource that has this Service Account attached to it", WithAdditional(map[string]any{"direct": true}))

								if !slices.Contains(*enhancedUniqueOwners, attachedResourcePrimaryOwner) {
									rctxItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
									*enhancedUniqueOwners = append(*enhancedUniqueOwners, attachedResourcePrimaryOwner)
								}

								resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, rctxItem)
							}
						}
					}
				}
			}
		}

		for _, derivedOwner := range resourceContextDoc.ResourceOwnerTypes.DerivedOwners {
			if strings.HasSuffix(derivedOwner.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX) {
				serviceAccount := strings.TrimSuffix(derivedOwner.Name, contextutils.SERVICEACCOUNT_USER_SUFFIX)
				serviceAccountContextID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)
				if _, ok := resourceContext.GetResourceContextInsertDoc(serviceAccountContextID); !ok {
					serviceAccountContextID = common.GenerateCombinedHashID(serviceAccount, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)
				}

				var attachedResourcePrimaryOwners []string

				if servAccRelatedRsc, ok := resourceContext.GetRelatedResourceList(serviceAccountContextID); ok {
					for _, relatedResource := range servAccRelatedRsc {
						if relatedResource.ResourceType != common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE &&
							relatedResource.ResourceType != common.GCP_SERVICEACCOUNT_RESOURCE_TYPE &&
							relatedResource.ResourceType != common.GCP_SAPOLICYBINDING_RESOURCE_TYPE {

							if dataEntry, ok := resourceContextMap.Load(relatedResource.ResourceDocID); ok {
								if rData, ok := dataEntry.(*ContextUniqueList); ok && rData != nil {
									if owners, ok := rData.Get(property.OWNER_PROPERTY_NAME); ok && len(owners) > 0 {
										attachedResourcePrimaryOwners = append(attachedResourcePrimaryOwners, owners[0])
									}
								}
							}
						}
					}
				}

				if len(attachedResourcePrimaryOwners) <= 2 {

					for _, attachedResourcePrimaryOwner := range attachedResourcePrimaryOwners {

						rctxItem := GetContextItem(attachedResourcePrimaryOwner, common.ATTACHEDRESOURCE_OWNER_TYPE, "User owns a resource that has the Service Account "+serviceAccount+" attached which owns this resource", WithAdditional(map[string]any{"direct": false}))

						if !slices.Contains(*enhancedUniqueOwners, attachedResourcePrimaryOwner) {
							rctxItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
							*enhancedUniqueOwners = append(*enhancedUniqueOwners, attachedResourcePrimaryOwner)
						}

						resourceContextDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextDoc.ResourceOwnerTypes.DerivedOwners, rctxItem)
					}
				}
			}
		}
	}
}

type RelatedOwner struct {
	Count               int
	Resources           map[string][]string
	Via                 []string
	Direct, IacRelation bool
}

func GetMaxRelatedOwner(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, docID string, resourceContextMap *sync.Map,
	maxCount *int, maxOwner *string, via string, relatedOwners map[string]RelatedOwner, processedDocIDs map[string]struct{}, depth int, iacRelation bool) {

	var (
		// For direct relations
		firstDepthMaxCount      int
		firstDepthMaxOwner      string
		firstDepthRelatedOwners = make(map[string]RelatedOwner)
	)

	processedDocIDs[docID] = struct{}{}

	if relatedResources, ok := resourceContext.GetRelatedResourceList(docID); ok {

		for i, relatedResource := range relatedResources {

			if relatedResource.ResourceDocID == docID {
				continue
			}

			if relatedResource.ResourceType == resourceContextDoc.ResourceType {
				// Do not consider relations of the same resource type
				continue
			}

			if !relatedResource.ContextualRelation {
				// Do not consider relations which should not exchange contextual information
				continue
			}

			resourceIDSplit := strings.Split(relatedResource.ResourceID, "/")
			resource := resourceIDSplit[len(resourceIDSplit)-1]
			resourceType := relatedResource.ResourceType

			if dataEntry, ok := resourceContextMap.Load(relatedResource.ResourceDocID); ok {
				if rData, ok := dataEntry.(*ContextUniqueList); ok && rData != nil {
					if owners, ok := rData.Get(property.OWNER_PROPERTY_NAME); ok {

						for _, relatedOwner := range owners {
							temp := relatedOwners[relatedOwner]
							temp.Count++

							if len(temp.Resources) <= 0 {
								temp.Resources = make(map[string][]string)
							}
							if len(temp.Resources) < 2 && len(temp.Resources[resourceType]) < 2 {

								if !slices.Contains(temp.Resources[resourceType], resource) {
									temp.Resources[resourceType] = append(temp.Resources[resourceType], resource)

									if depth > 1 {
										if !slices.Contains(temp.Via, via) {
											temp.Via = append(temp.Via, via)
										}

										if iacRelation {
											temp.IacRelation = true
										}
									}
								}
							}

							if temp.Count > *maxCount {
								*maxCount = temp.Count
								*maxOwner = relatedOwner
							}

							relatedOwners[relatedOwner] = temp

							if depth == 1 {

								if relatedResource.Priority {

									// Priority resources for direct relationships - like EC2 key for EC2

									*maxOwner = relatedOwner

									temp.Resources = make(map[string][]string)
									temp.Resources[resourceType] = append([]string{resource}, temp.Resources[resourceType]...)
									temp.Direct = true

									relatedOwners[relatedOwner] = temp
									return
								}

								firstDepthTemp := firstDepthRelatedOwners[relatedOwner]
								firstDepthTemp.Count++
								firstDepthTemp.Direct = true

								if len(firstDepthTemp.Resources) <= 0 {
									firstDepthTemp.Resources = make(map[string][]string)
								}
								if len(firstDepthTemp.Resources) < 2 && len(firstDepthTemp.Resources[resourceType]) < 2 {

									if !slices.Contains(firstDepthTemp.Resources[resourceType], resource) {
										firstDepthTemp.Resources[resourceType] = append(firstDepthTemp.Resources[resourceType], resource)
									}
								}

								if firstDepthTemp.Count > firstDepthMaxCount {
									firstDepthMaxCount = firstDepthTemp.Count
									firstDepthMaxOwner = relatedOwner
								}
								firstDepthRelatedOwners[relatedOwner] = firstDepthTemp
							}
						}
					}
				}
			}

			if i == len(relatedResources)-1 && len(firstDepthMaxOwner) > 0 {
				// If direct relationships has a proper max owner, prioritise that
				*maxOwner = firstDepthMaxOwner

				for k := range relatedOwners {
					delete(relatedOwners, k)
				}

				for k, v := range firstDepthRelatedOwners {
					relatedOwners[k] = v
				}

				return
			}

			if _, ok := processedDocIDs[relatedResource.ResourceDocID]; !ok && depth < 2 && !iacRelation {

				if relatedResource.ResourceType == common.AWS_CFTSTACK_RESOURCE_TYPE || relatedResource.ResourceType == common.PRECIZEINTERNAL_COMMITFILE_RESOURCE_TYPE {
					iacRelation = true
				}

				if depth == 1 {
					// Directly related resource through which other indirect relations happen
					via = resource + " (" + relatedResource.ResourceType + ")"
				}

				GetMaxRelatedOwner(resourceContext, resourceContextDoc, relatedResource.ResourceDocID, resourceContextMap, maxCount, maxOwner, via, relatedOwners, processedDocIDs, depth+1, iacRelation)
			}
		}
	}
}

func GetPrimaryUserSource(tenantID string) (primarySource string) {

	primarySourceQuery := `{"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}}]}}}`

	primarySourceDocs, err := elastic.ExecuteSearchQuery([]string{elastic.COMPANY_METADATA_INDEX}, primarySourceQuery)
	if err != nil {
		return
	}

	if len(primarySourceDocs) == 1 {
		primarySource, _ = primarySourceDocs[0]["userPrimarySource"].(string)
	}

	return
}

func PopulateExampleNames(tenantID string, exampleNames map[string]bool) {

	txtLookupQuery := `{"query":{"bool":{"filter":[{"term":{"tenantId":"` + tenantID + `"}}],"must_not":[{"wildcard":{"text":"*format*"}},{"wildcard":{"text":"*ticket*"}},{"wildcard":{"text":"*system*"}}]}},"aggs":{"hasName_true":{"filter":{"term":{"hasName":true}},"aggs":{"top_hasName_true":{"top_hits":{"size":100,"_source":["text"]}}}},"hasName_false":{"filter":{"term":{"hasName":false}},"aggs":{"top_hasName_false":{"top_hits":{"size":100,"_source":["text"]}}}}},"size":0}`
	aggsResp, err := elastic.ExecuteSearchForAggregation([]string{elastic.TEXT_LOOKUP_INDEX}, txtLookupQuery)
	if err == nil {
		jsonData, err := json.Marshal(aggsResp)
		if err != nil {
			return
		}

		var textLookUpAgsResp emailutils.TextExampleAggs
		if err := json.Unmarshal(jsonData, &textLookUpAgsResp); err != nil {
			return
		}

		for _, bucket := range textLookUpAgsResp.HasNameTrue.TopHasNameTrue.Hits.Hits {
			text := bucket.Source.Text
			if len(strings.Split(text, " ")) < 4 {
				exampleNames[bucket.Source.Text] = true
			}

			if len(exampleNames) > 6 {
				break
			}
		}

		for _, bucket := range textLookUpAgsResp.HasNameFalse.TopHasNameFalse.Hits.Hits {
			text := bucket.Source.Text
			if len(strings.Split(text, " ")) < 4 {
				exampleNames[bucket.Source.Text] = false
			}

			if len(exampleNames) > 8 {
				break
			}
		}
	}
}

// splitUser - John
// partialName - johnD
// userNameSplit - [John, Doe]
func checkAbbreviationPattern(splitUser, partialName string, userNameSplit []string, currentUserIndex int) bool {

	var abbreviatedName strings.Builder

	for j := range currentUserIndex {
		namePart := strings.ToLower(userNameSplit[j])
		if len(namePart) > 0 {
			abbreviatedName.WriteByte(namePart[0])
		}
	}

	abbreviatedName.WriteString(splitUser)

	for j := currentUserIndex + 1; j < len(userNameSplit); j++ {
		namePart := strings.ToLower(userNameSplit[j])
		if len(namePart) > 0 {
			abbreviatedName.WriteByte(namePart[0])
		}
	}

	if abbreviatedName.String() == partialName {
		return true
	}

	if currentUserIndex == 0 || currentUserIndex == len(userNameSplit)-1 {
		var simpleAbbreviation strings.Builder

		if currentUserIndex == 0 {
			// firstname + last_name_initial
			simpleAbbreviation.WriteString(splitUser)
			if len(userNameSplit) > 1 {
				lastNamePart := strings.ToLower(userNameSplit[len(userNameSplit)-1])
				if len(lastNamePart) > 0 {
					simpleAbbreviation.WriteByte(lastNamePart[0])
				}
			}
		} else {
			// first_name_initial + lastname
			firstNamePart := strings.ToLower(userNameSplit[0])
			if len(firstNamePart) > 0 {
				simpleAbbreviation.WriteByte(firstNamePart[0])
			}
			simpleAbbreviation.WriteString(splitUser)
		}

		if simpleAbbreviation.String() == partialName {
			return true
		}
	}

	return false

}

func EnhanceContextItemForOwner(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, owner []string) {

	for i, definedOwner := range resourceContextDoc.ResourceOwnerTypes.DefinedOwners {
		if len(definedOwner.Desc) == 0 {
			definedOwner.Desc = GetStaticDescriptionOfUserType(definedOwner.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &definedOwner)
		resourceContextDoc.ResourceOwnerTypes.DefinedOwners[i] = definedOwner
	}

	for i, derivedOwner := range resourceContextDoc.ResourceOwnerTypes.DerivedOwners {
		if len(derivedOwner.Desc) == 0 {
			derivedOwner.Desc = GetStaticDescriptionOfUserType(derivedOwner.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &derivedOwner)
		resourceContextDoc.ResourceOwnerTypes.DerivedOwners[i] = derivedOwner
	}

	for i, codeOwner := range resourceContextDoc.ResourceOwnerTypes.CodeOwners {
		if len(codeOwner.Desc) == 0 {
			codeOwner.Desc = GetStaticDescriptionOfUserType(codeOwner.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &codeOwner)
		resourceContextDoc.ResourceOwnerTypes.CodeOwners[i] = codeOwner
	}

	for i, inheritedOwner := range resourceContextDoc.ResourceOwnerTypes.InheritedOwners {
		if len(inheritedOwner.Desc) == 0 {
			inheritedOwner.Desc = GetStaticDescriptionOfUserType(inheritedOwner.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &inheritedOwner)
		resourceContextDoc.ResourceOwnerTypes.InheritedOwners[i] = inheritedOwner
	}

	for i, securityOwner := range resourceContextDoc.ResourceOwnerTypes.SecurityOwners {
		if len(securityOwner.Desc) == 0 {
			securityOwner.Desc = GetStaticDescriptionOfUserType(securityOwner.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &securityOwner)
		resourceContextDoc.ResourceOwnerTypes.SecurityOwners[i] = securityOwner
	}

	for i, costOwner := range resourceContextDoc.ResourceOwnerTypes.CostOwners {
		if len(costOwner.Desc) == 0 {
			costOwner.Desc = GetStaticDescriptionOfUserType(costOwner.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &costOwner)
		resourceContextDoc.ResourceOwnerTypes.CostOwners[i] = costOwner
	}

	for i, opsOwner := range resourceContextDoc.ResourceOwnerTypes.OpsOwners {
		if len(opsOwner.Desc) == 0 {
			opsOwner.Desc = GetStaticDescriptionOfUserType(opsOwner.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &opsOwner)
		resourceContextDoc.ResourceOwnerTypes.OpsOwners[i] = opsOwner
	}
}
