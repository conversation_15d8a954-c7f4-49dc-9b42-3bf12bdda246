package context

import (
	"strings"
	"sync"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	emailutils "github.com/precize/enhancer/internal/email"
	identityutils "github.com/precize/enhancer/internal/identity"
	"github.com/precize/enhancer/internal/sddl"
	"github.com/precize/enhancer/rcontext"
)

type ContextUniqueList struct {
	mu   sync.RWMutex
	Data map[string][]string
}

func (rcd *ContextUniqueList) Get(key string) ([]string, bool) {
	rcd.mu.RLock()
	defer rcd.mu.RUnlock()
	value, exists := rcd.Data[key]
	if !exists {
		return nil, false
	}

	result := make([]string, len(value))
	copy(result, value)
	return result, true
}

func (rcd *ContextUniqueList) Set(key string, value []string) {
	rcd.mu.Lock()
	defer rcd.mu.Unlock()
	if rcd.Data == nil {
		rcd.Data = make(map[string][]string)
	}

	valueCopy := make([]string, len(value))
	copy(valueCopy, value)
	rcd.Data[key] = valueCopy
}

func (rcd *ContextUniqueList) GetAll() map[string][]string {
	rcd.mu.RLock()
	defer rcd.mu.RUnlock()
	result := make(map[string][]string)
	for k, v := range rcd.Data {
		valueCopy := make([]string, len(v))
		copy(valueCopy, v)
		result[k] = valueCopy
	}
	return result
}

func (rcd *ContextUniqueList) SetAll(data map[string][]string) {
	rcd.mu.Lock()
	defer rcd.mu.Unlock()
	if rcd.Data == nil {
		rcd.Data = make(map[string][]string)
	}
	for k, v := range data {

		valueCopy := make([]string, len(v))
		copy(valueCopy, v)
		rcd.Data[k] = valueCopy
	}
}

type ContextItemOption struct {
	resourceContext   *rcontext.ResourceContext
	objectType        *int
	previousCollected string
	evaluateUser      bool
	identityID        string
	childIdentityID   string
	accountID         string
	event             *common.ResourceCtxEvent
	identityStatus    *int
	additional        map[string]any
}

type ContextItemOptions func(*ContextItemOption)

func WithUserEvaluate(r *rcontext.ResourceContext, identityID, accountID string, event *common.ResourceCtxEvent) ContextItemOptions {
	return func(c *ContextItemOption) {
		c.resourceContext = r
		c.evaluateUser = true
		c.identityID = identityID
		c.accountID = accountID
		c.event = event
	}
}

func WithPrevious(previousCollected string) ContextItemOptions {
	return func(c *ContextItemOption) {
		c.previousCollected = previousCollected
	}
}

func WithIdentity(identityID, childIdentityID string, identityStatus *int) ContextItemOptions {
	return func(c *ContextItemOption) {
		c.identityID = identityID
		c.childIdentityID = childIdentityID
		c.identityStatus = identityStatus
	}
}

func WithObjectType(objectType *int) ContextItemOptions {
	return func(c *ContextItemOption) {
		c.objectType = objectType
	}
}

func WithAdditional(additional map[string]any) ContextItemOptions {
	return func(c *ContextItemOption) {
		c.additional = make(map[string]any)
		for k, v := range additional {
			c.additional[k] = v
		}
	}
}

func GetContextItem(itemName, itemType, desc string, opts ...ContextItemOptions) (resourceCtxItem common.ResourceContextItem) {

	var contextItemOption ContextItemOption

	if len(itemName) <= 0 {
		return
	}

	for _, opt := range opts {
		opt(&contextItemOption)
	}

	switch {
	case contextItemOption.evaluateUser:
		resourceCtxItem = GetUserContextItem(contextItemOption.resourceContext, itemName, itemType, desc, contextItemOption.identityID, contextItemOption.accountID, contextItemOption.event)
	default:
		resourceCtxItem = common.ResourceContextItem{
			Name: itemName,
			Type: itemType,
			Desc: desc,
		}

		if len(contextItemOption.previousCollected) > 0 {
			resourceCtxItem.CollectedFrom = contextItemOption.previousCollected
		}

		if len(contextItemOption.identityID) > 0 {
			resourceCtxItem.IdentityId = contextItemOption.identityID
			resourceCtxItem.ChildIdentityID = contextItemOption.childIdentityID
		}

		if contextItemOption.objectType != nil {
			resourceCtxItem.ObjectType = contextItemOption.objectType
		}

		if contextItemOption.identityStatus != nil {
			resourceCtxItem.IdentityStatus = contextItemOption.identityStatus
		}
	}

	resourceCtxItem.Additional = make(map[string]any)
	for k, v := range contextItemOption.additional {
		resourceCtxItem.Additional[k] = v
	}

	return
}

func GetUserContextItem(r *rcontext.ResourceContext, username, userType, desc, identityId, accountID string, event *common.ResourceCtxEvent) (resourceCtxItem common.ResourceContextItem) {

	username = strings.TrimPrefix(username, common.EX_EMPLOYEE_PREFIX)
	if vals, ok := r.GetTypoExceptions(username); ok && len(vals) == 1 {
		username = vals[0]
	}

	var (
		name           string
		isEmailActive  = true
		isValidEmail   bool
		emailStatusMap map[string]bool
	)

	if strings.HasSuffix(username, contextutils.IAM_USER_SUFFIX) || strings.HasSuffix(username, contextutils.APP_USER_SUFFIX) ||
		strings.HasSuffix(username, contextutils.IAM_ROLE_SUFFIX) || strings.HasSuffix(username, contextutils.SERVICEACCOUNT_USER_SUFFIX) ||
		strings.HasSuffix(username, contextutils.ACCOUNT_USER_SUFFIX) || strings.HasSuffix(username, contextutils.AWSSERVICE_USER_SUFFIX) || strings.HasSuffix(username, contextutils.PLACEHOLDER_USER_SUFFIX) {

		usrRsc := rcontext.UserContext{
			Name: username,
		}

		usrRsc.SetActiveAccounts(accountID, 1)

		r.SetUserResource(contextutils.NAME_ONLY_PREFIX+strings.ToLower(username), &usrRsc)
	} else {

		if r.ServiceID == common.GCP_SERVICE_ID && strings.HasPrefix(userType, contextutils.TAG_PREFIX) {

			// Because GCP labels doesn't allow special characters, so @ not supported

			if _, err := common.ParseAddress(username); err != nil {

				for _, domain := range emailutils.EmailDomains {

					if strings.HasSuffix(username, domain) {

						// In House method
						derivedEmail, err := emailutils.DeriveEmailFromGCPTag(username, r)
						if err != nil && strings.Contains(err.Error(), contextutils.INVALID_EMAIL_ERROR) {
							isEmailActive = false
						} else if len(derivedEmail) > 0 {
							username = derivedEmail
						}
					}
				}
			}
		}

		if _, err := common.ParseAddress(username); err != nil {
			nameOnlyUserResource := username
			nameList := strings.Split(username, " ")
			if len(nameList) > 1 {
				// names with single word should not be converted to title case (might cause issues in email derivation)
				nameOnlyUserResource = common.ConvertToTitleCase(nameOnlyUserResource)
			}

			name = common.GetFormattedName(nameOnlyUserResource)
			if isOwnerText(name, r.TenantID) {
				email := emailutils.DeriveEmailFromName(name, r)
				if len(email) > 0 {
					username = email
				} else {
					usrRsc := rcontext.UserContext{
						Name: nameOnlyUserResource,
					}

					usrRsc.SetActiveAccounts(accountID, 1)
					r.SetUserResource(contextutils.NAME_ONLY_PREFIX+strings.ToLower(username), &usrRsc)
				}
			}
		}

		if originalEmailAddr, err := common.ParseAddress(username); err == nil {

			temp := rcontext.UserContext{
				Email: originalEmailAddr.Address,
				Name:  originalEmailAddr.Name,
			}

			temp.IsSddl = sddl.HandleUserContextWithSDDL(&temp.Email, &name, r)

			if strings.Contains(strings.ToLower(username), contextutils.EXT_KEYWORD) {
				identityutils.HandleIdentityWithExternalEmail(r, &temp)
			}

			if strings.Contains(strings.ToLower(username), contextutils.LIVE_MICROSOFT_KEYWORD) || strings.Contains(strings.ToLower(username), contextutils.MAIL_MICROSOFT_KEYWORD) {
				identityutils.HandlePersonalAndMicrosoftEmail(r, &temp)
			}

			if addr, err := common.ParseAddress(temp.Email); err == nil {

				if len(temp.Name) > 0 {
					name = temp.Name
				}

				if len(name) <= 0 {
					name = common.GetFormattedNameFromEmail(addr.Address)
				}

				if temp.IsSddl {
					name = sddl.RemoveSddlFromName(name)
				}

				if userResource, ok := r.GetUserResource(addr.Address); !ok {

					if isEmailActive {

						if isValidEmail, ok = r.GetEmailStatus(addr.Address); !ok && len(name) > 0 {
							emailNameMap := map[string]string{
								addr.Address:              name,
								originalEmailAddr.Address: name,
							}

							if emailStatusMap, err = emailutils.CheckEmailValidity(emailNameMap, false, r); err == nil {
								isValidEmail = len(emailStatusMap) > 0 && emailStatusMap[addr.Address]
								r.SetEmailStatus(addr.Address, isValidEmail)
							}
						}

						// if addr.Address status does not throw error update activate Employee flag
						if err == nil {
							isEmailActive = isValidEmail
						}
					}

					temp.Active = isEmailActive
					if ok := r.GetInvalidEmailCache(temp.Email); ok {
						temp.IsInvalid = true
					}

					if fullName, ok := r.GetEmailToFullName(addr.Address); ok && len(fullName) > len(name) {
						name = fullName
					}

					temp.Name = name
					identityutils.HandleChildEmail(r, &temp)

					temp.SetActiveAccounts(accountID, 1)

					r.SetUserResource(addr.Address, &temp)

				} else {

					if len(userResource.Name) < len(name) {
						userResource.Name = name
					}

					if accCount, ok := userResource.GetActiveAccounts(accountID); ok {
						userResource.SetActiveAccounts(accountID, accCount+1)
					} else {
						userResource.SetActiveAccounts(accountID, 1)
					}

					r.SetUserResource(userResource.Email, userResource)
				}

				// updating username since addr will contain trimmed email (sddl)
				username = addr.Address

			}
		}
	}

	resourceCtxItem = common.ResourceContextItem{
		Name:       username,
		Type:       userType,
		IdentityId: identityId,
	}

	if len(desc) > 0 {
		resourceCtxItem.Desc = desc
	} else {
		resourceCtxItem.Desc = GetStaticDescriptionOfUserType(resourceCtxItem.Type)
	}

	if event != nil {
		resourceCtxItem.Event = event
	}

	return resourceCtxItem
}
