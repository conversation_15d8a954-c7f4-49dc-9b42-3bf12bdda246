package context

import (
	"regexp"
	"slices"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/enhancer/internal/confidence"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
)

func GetSoftwareNameFromValue(str string) string {

	str = strings.ToLower(str)

	for software, values := range contextutils.SoftwareValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				return software
			}
		}
	}

	return ""
}

func GetSoftwareNameListFromValue(str string) []string {

	softwareNames := make([]string, 0)
	str = strings.ToLower(str)

	for software, values := range contextutils.SoftwareValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				softwareNames = append(softwareNames, software)
			}
		}
	}

	return softwareNames
}

func GetUniqueSoftwareContext(resourceContextDoc *common.ResourceContextInsertDoc) (software []string) {

	uniqueSoftware := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedSoftware, uniqueSoftware)
	resourceutils.GetUniqueContext(&resourceContextDoc.DerivedSoftware, uniqueSoftware)

	for softwareName := range uniqueSoftware {
		software = append(software, softwareName)
	}

	return
}

func GetStaticDescriptionOfSoftwareType(softwareType string) (desc string) {

	switch softwareType {
	case common.RESOURCE_NAME_SOFTWARE_TYPE:
		desc = "Software component has been derived from the resource name"
	case common.DEFAULT_PORT_SOFTWARE_TYPE:
		desc = "Software component has been derived from allowed ports"
	case common.IACTEMPLATE_SOFTWARE_TYPE:
		desc = "Software component has been derived from IaC template"
	case common.ORCACONTEXT_SOFTWARE_TYPE:
		desc = "Software component has been derived from Orca findings"
	case common.CUSTOMER_DEFINED_SOFTWARE_TYPE:
		desc = "Software component has been assigned from Precize console"
	case common.DEFENDERCONTEXT_SOFTWARE_TYPE:
		desc = "Software component has been derived from Defender findings"
	case common.JIRA_SOFTWARE_TYPE:
		desc = "Software component has been derived by extracting a corresponding Jira issue"
	case common.PRECIZE_DEFINED_SOFTWARE_TYPE:
		desc = "Software component has been assigned by Precize"
	case common.DESC_SOFTWARE_TYPE:
		desc = "Software component has been derived from the resource description"
	}

	if len(desc) == 0 && strings.Contains(softwareType, contextutils.TAG_PREFIX) {
		desc = "Software component has been derived from tag"
	}

	return
}

func EnhanceContextItemForSoftware(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, software []string) {

	for i, definedSoftware := range resourceContextDoc.ResourceSoftwareTypes.DefinedSoftware {
		if slices.Contains(software, definedSoftware.Name) {
			definedSoftware.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(definedSoftware.Desc) == 0 {
			definedSoftware.Desc = GetStaticDescriptionOfSoftwareType(definedSoftware.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &definedSoftware)
		resourceContextDoc.ResourceSoftwareTypes.DefinedSoftware[i] = definedSoftware
	}

	for i, derivedSoftware := range resourceContextDoc.ResourceSoftwareTypes.DerivedSoftware {
		if slices.Contains(software, derivedSoftware.Name) {
			derivedSoftware.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(derivedSoftware.Desc) == 0 {
			derivedSoftware.Desc = GetStaticDescriptionOfSoftwareType(derivedSoftware.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &derivedSoftware)
		resourceContextDoc.ResourceSoftwareTypes.DerivedSoftware[i] = derivedSoftware
	}
}
