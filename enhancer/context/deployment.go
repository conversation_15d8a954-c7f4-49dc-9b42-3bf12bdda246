package context

import (
	"regexp"
	"strings"

	"slices"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/enhancer/internal/confidence"
	resourceutils "github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
)

func GetDeploymentNamesFromValue(str string) (deployments []string) {

	str = strings.ToLower(str)

	for deployment, values := range contextutils.DeploymentKeyOrValues {
		for _, val := range values {

			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				deployments = append(deployments, deployment)
			}
		}
	}

	return
}

func GetUniqueDeploymentContext(resourceContextDoc *common.ResourceContextInsertDoc) (deployment []string) {

	uniqueDeployment := make(map[string]struct{})

	resourceutils.GetUniqueContext(&resourceContextDoc.DefinedDeployment, uniqueDeployment)
	resourceutils.GetUniqueContext(&resourceContextDoc.DerivedDeployment, uniqueDeployment)

	combinedDeployment(uniqueDeployment, resourceContextDoc)

	for deploymentName := range uniqueDeployment {
		deployment = append(deployment, deploymentName)
	}

	return
}

func combinedDeployment(uniqueDeployment map[string]struct{}, resourceContextDoc *common.ResourceContextInsertDoc) {

	deleteDeployment := func(deploymentName string) {
		delete(uniqueDeployment, deploymentName)

		resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment = filterResourceContextItems(
			resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment, deploymentName,
		)

		resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment = filterResourceContextItems(
			resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment, deploymentName,
		)
	}

	// If any of the IaC deployments are present, remove all other deployments
	iacDeployments := []string{contextutils.TERRAFORM_DEPLOYMENT, contextutils.CFT_DEPLOYMENT, contextutils.AZDO_TF_DEPLOYMENT}
	hasIaC := false

	for _, iacDeployment := range iacDeployments {
		if _, ok := uniqueDeployment[iacDeployment]; ok {
			hasIaC = true
			break
		}
	}

	if hasIaC {
		for _, item := range resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment {
			isIaC := slices.Contains(iacDeployments, item.Name)
			if !isIaC {
				resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment = filterResourceContextItems(
					resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment, item.Name,
				)
				delete(uniqueDeployment, item.Name)
			}
		}

		for _, item := range resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment {
			isIaC := slices.Contains(iacDeployments, item.Name)
			if !isIaC {
				resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment = filterResourceContextItems(
					resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment, item.Name,
				)
				delete(uniqueDeployment, item.Name)
			}
		}

		return
	}

	if _, ok := uniqueDeployment[contextutils.AZDO_TF_DEPLOYMENT]; ok {
		deleteDeployment(contextutils.AZDO_DEPLOYMENT)
		deleteDeployment(contextutils.TERRAFORM_DEPLOYMENT)
	}

	if _, ok := uniqueDeployment[contextutils.KUBERNETES_DEPLOYMENT]; ok {
		if _, ok := uniqueDeployment[contextutils.ARGO_DEPLOYMENT]; ok {
			uniqueDeployment[contextutils.ARGO_KUBERNETES_DEPLOYMENT] = struct{}{}

			for _, rctxItem := range resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment {
				if rctxItem.Name == contextutils.ARGO_DEPLOYMENT {
					resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment = append(
						resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment, GetContextItem(contextutils.ARGO_KUBERNETES_DEPLOYMENT, rctxItem.Type, ""),
					)
				}
			}

			for _, rctxItem := range resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment {
				if rctxItem.Name == contextutils.ARGO_DEPLOYMENT {
					resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment = append(
						resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment, GetContextItem(contextutils.ARGO_KUBERNETES_DEPLOYMENT, rctxItem.Type, ""),
					)
				}
			}

			deleteDeployment(contextutils.KUBERNETES_DEPLOYMENT)
			deleteDeployment(contextutils.ARGO_DEPLOYMENT)
		}
	}
}

func filterResourceContextItems(items []common.ResourceContextItem, nameToRemove string) []common.ResourceContextItem {
	var filtered []common.ResourceContextItem
	for _, item := range items {
		if item.Name != nameToRemove {
			filtered = append(filtered, item)
		}
	}
	return filtered
}

func GetStaticDescriptionOfDeploymentType(deploymentType string) (desc string) {

	switch deploymentType {
	case common.RESOURCE_NAME_DEPLOYMENT_TYPE:
		desc = "Deployment has been derived from the resource name"
	case common.ACTIVITY_DEPLOYMENT_TYPE:
		desc = "Deployment has been derived from activities"
	case common.RESOURCETYPE_DEPLOYMENT_TYPE:
		desc = "Deployment has been derived from the resource type"
	case common.CUSTOMER_DEFINED_DEPLOYMENT_TYPE:
		desc = "Deployment has been assigned from Precize console"
	case common.PRECIZE_DEFINED_DEPLOYMENT_TYPE:
		desc = "Deployment has been assigned by Precize"
	case common.RESOURCEPROPERTY_DEPLOYMENT_TYPE:
		desc = "Deployment has been derived from resource properties"
	case common.JIRA_DEPLOYMENT_TYPE:
		desc = "Deployment has been derived by extracting a corresponding Jira issue"
	case common.DESC_DEPLOYMENT_TYPE:
		desc = "Deployment has been derived from the resource description"
	case common.DEFAULT_RESOURCE_DEPLOYMENT_TYPE:
		desc = "Resource is provisioned by default"
	case common.NEIGHBOUR_DEPLOYMENT_TYPE:
		desc = "Deployment has been derived from a neighbouring resource"
	}

	if len(desc) == 0 && strings.Contains(deploymentType, contextutils.TAG_PREFIX) {
		desc = "Deployment has been derived from tag"
	}

	return
}

func EnhanceContextItemForDeployment(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, deployment []string) {

	for i, definedDeployment := range resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment {
		if slices.Contains(deployment, definedDeployment.Name) {
			definedDeployment.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(definedDeployment.Desc) == 0 {
			definedDeployment.Desc = GetStaticDescriptionOfDeploymentType(definedDeployment.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &definedDeployment)
		resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment[i] = definedDeployment
	}

	for i, derivedDeployment := range resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment {
		if slices.Contains(deployment, derivedDeployment.Name) {
			derivedDeployment.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_PRIMARY)
		}

		if len(derivedDeployment.Desc) == 0 {
			derivedDeployment.Desc = GetStaticDescriptionOfDeploymentType(derivedDeployment.Type)
		}

		confidence.SetConfidence(resourceContext, resourceContextDoc, &derivedDeployment)
		resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment[i] = derivedDeployment
	}
}
