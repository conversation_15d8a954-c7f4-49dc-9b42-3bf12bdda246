package exceptions

import (
	"encoding/json"

	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
	"github.com/precize/rds/exceptions"
)

func GetExceptions(resourceContext *rcontext.ResourceContext) {
	tenantID := resourceContext.TenantID

	cases := []struct {
		Type string
		Op   string
	}{
		{"owner_match", "ne"},
		{"owner_match", "eq"},
		{"typo", "eq"},
		{"email_name_match", "eq"},
		{"email_derivation", "ne"},
		{"email_derivation", "eq"},
		{"parent_child_email", "eq"},
		{"data_sensitivity", "ne"},
	}

	for _, c := range cases {

		records, err := exceptions.GetExceptionsForTenant(c.Type, c.Op, tenantID)
		if err != nil {
			continue
		}

		for _, rec := range records {

			var values []string

			if err := json.Unmarshal(rec.Values, &values); err != nil {
				logger.Print(logger.ERROR, "Unmarshal failed", err)
				continue
			}

			switch {
			case c.Type == "owner_match" && c.Op == "ne":
				existing, _ := resourceContext.GetOwnerExceptions(rec.Key)
				resourceContext.SetOwnerExceptions(rec.Key, append(existing, values...))

			case c.Type == "owner_match" && c.Op == "eq":
				existing, _ := resourceContext.GetOwnerInclusions(rec.Key)
				resourceContext.SetOwnerInclusions(rec.Key, append(existing, values...))

			case c.Type == "typo" && c.Op == "eq":
				existing, _ := resourceContext.GetTypoExceptions(rec.Key)
				resourceContext.SetTypoExceptions(rec.Key, append(existing, values...))

			case c.Type == "email_name_match" && c.Op == "eq":
				for _, v := range values {
					resourceContext.SetOwnerEmailName(rec.Key, v)
				}

			case c.Type == "email_derivation" && c.Op == "ne":
				if len(values) > 0 {
					resourceContext.SetDerivedEmailExclusions(rec.Key, values)
				}

			case c.Type == "email_derivation" && c.Op == "eq":
				if len(values) > 0 {
					resourceContext.SetDerivedEmailInclusions(rec.Key, values)
				}

			case c.Type == "parent_child_email" && c.Op == "eq":
				for _, v := range values {
					resourceContext.SetChildPrimaryEmail(rec.Key, v, true)
				}

			case c.Type == "data_sensitivity" && c.Op == "ne":
				if len(values) > 0 {
					resourceContext.SetSensitivityExceptions(rec.Key, values)
				}
			}
		}
	}
}
