package confidence

import (
	"math"

	"github.com/precize/common"
)

func adjustConfidenceForMajorityEnv(confidence *float32, resourceCtxItem *common.ResourceContextItem) {

	if source, ok := resourceCtxItem.Additional["source"].(string); ok {
		if resourceCount, ok := resourceCtxItem.Additional["resourceCount"].(int); ok {
			if envMaxCount, ok := resourceCtxItem.Additional["envMaxCount"].(int); ok {

				switch source {
				case "children": // Directly from child resources
					*confidence += 0.02
				case "account": // From lateral resources in account
					*confidence -= 0.02
				case "resourceGroup": // From lateral resources in resource group

				}

				if resourceCount > 0 {
					ratio := float32(envMaxCount) / float32(resourceCount) * 100
					if ratio > 25 {
						// For each 1% above 25%, add 0.002 confidence
						boost := math.Floor(float64(ratio-25)) * 0.002
						*confidence += float32(boost)
					}
				}

				if *confidence > 0.9 {
					*confidence = 0.9
				}
			}
		}
	}
}
