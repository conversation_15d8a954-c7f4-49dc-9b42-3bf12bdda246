package confidence

import (
	"github.com/precize/common"
)

var (
	ContextItemConfidence = map[string]float32{
		common.ACCOUNT_OWNER_TYPE:                 1,
		common.ACCOUNT_CONTACT_OWNER_TYPE:         1,
		common.PROJECT_OWNER_TYPE:                 1,
		common.PROJECT_GROUPOWNER_OWNER_TYPE:      0.8,
		common.ORG_OWNER_TYPE:                     1,
		common.ORG_GROUPOWNER_OWNER_TYPE:          0.8,
		common.FOLDER_OWNER_TYPE:                  1,
		common.FOLDER_GROUPOWNER_OWNER_TYPE:       0.8,
		common.PARENTFOLDER_OWNER_TYPE:            0.8,
		common.SUBSCRIPTION_OWNER_TYPE:            1,
		common.SUBSCRIPTION_CONTACT_OWNER_TYPE:    1,
		common.ACTIVITY_OWNER_TYPE:                0.95,
		common.POLICYBINDING_OWNER_TYPE:           0.85,
		common.REASSIGNED_OWNER_TYPE:              0.7,
		common.NO_CHILD_OWNERS_PARENT_OWNER_TYPE:  0.65,
		common.FEW_CHILD_OWNERS_PARENT_OWNER_TYPE: 0.65,
		common.RELATED_RESOURCE_OWNER_TYPE:        0.9,
		common.PARENT_RESOURCE_OWNER_TYPE:         0.9,
		common.SIMILAR_RESOURCENAME_OWNER_TYPE:    0.75,
		common.SAME_APP_OWNER_TYPE:                0.75,
		common.SAME_TAG_OWNER_TYPE:                0.75,
		common.RESOURCE_TYPE_OWNER_TYPE:           0.65,
		common.RESOURCE_NAME_OWNER_TYPE:           0.85,
		common.JIRA_OWNER_TYPE:                    0.8,
		common.RESOURCE_OWNER_TYPE:                1,
		common.RESOURCE_OWNERAPP_OWNER_TYPE:       0.85,
		common.RESOURCE_OWNERIAMROLE_OWNER_TYPE:   0.85,
		common.OPENAIPROJECT_OWNER_TYPE:           1,
		common.OPENAIORG_OWNER_TYPE:               1,
		common.ATTACHEDRESOURCE_OWNER_TYPE:        0.85,
		common.CUSTOMER_DEFINED_OWNER_TYPE:        1,
		common.PRECIZE_DEFINED_OWNER_TYPE:         1,
		common.GITHUB_OWNER_TYPE:                  0.85,
		common.GITLAB_OWNER_TYPE:                  0.85,
		common.BITBUCKET_OWNER_TYPE:               0.85,
		common.ORG_ACCOUNT_OWNER_TYPE:             1,
		common.RESOURCE_OPS_CONTACT_OWNER_TYPE:    1,
		common.PRECIZE_DETECTED_OWNER_TYPE:        1,
		common.DESC_OWNER_TYPE:                    0.95,
		common.MANAGER_OWNER_TYPE:                 0.8,
		// common.CREATOR_OWNER_TYPE:                       0.9,

		common.ACCOUNT_NAME_ENV_TYPE:       0.9,
		common.RG_NAME_ENV_TYPE:            0.9,
		common.SUBSCRIPTION_NAME_ENV_TYPE:  0.9,
		common.ORG_NAME_ENV_TYPE:           0.9,
		common.FOLDER_NAME_ENV_TYPE:        0.9,
		common.PROJECT_NAME_ENV_TYPE:       0.9,
		common.RESOURCE_NAME_ENV_TYPE:      0.9,
		common.MAJORITY_ENV_TYPE:           0.8,
		common.OPENAIPROJECT_NAME_ENV_TYPE: 0.9,
		common.CUSTOMER_DEFINED_ENV_TYPE:   1,
		common.PRECIZE_DEFINED_ENV_TYPE:    1,
		common.TENANT_NAME_ENV_TYPE:        0.9,
		common.MGMTGRP_NAME_ENV_TYPE:       0.9,
		common.ORGUNIT_NAME_ENV_TYPE:       0.9,
		common.JIRA_ENV_TYPE:               0.8,
		common.INHERITED_ENV_TYPE:          0.9,

		common.RESOURCE_NAME_APP_TYPE:        0.85,
		common.IACTEMPLATE_APP_TYPE:          0.85,
		common.CUSTOMER_DEFINED_APP_TYPE:     1,
		common.PRECIZE_DEFINED_APP_TYPE:      1,
		common.JIRA_APP_TYPE:                 0.8,
		common.RELATED_RESOURCE_APP_TYPE:     0.9,
		common.DESC_APP_TYPE:                 0.95,
		common.SIMILAR_RESOURCENAME_APP_TYPE: 0.8,
		common.SAME_TAG_APP_TYPE:             0.8,
		common.ACTIVITY_APP_TYPE:             0.95,
		// common.ORCACONTEXT_APP_TYPE:          0.9,
		// common.DEFENDERCONTEXT_APP_TYPE:      0.9,

		common.RESOURCE_NAME_DEPLOYMENT_TYPE:    0.85,
		common.ACTIVITY_DEPLOYMENT_TYPE:         0.95,
		common.RESOURCETYPE_DEPLOYMENT_TYPE:     1,
		common.CUSTOMER_DEFINED_DEPLOYMENT_TYPE: 1,
		common.PRECIZE_DEFINED_DEPLOYMENT_TYPE:  1,
		common.JIRA_DEPLOYMENT_TYPE:             0.8,
		common.RESOURCEPROPERTY_DEPLOYMENT_TYPE: 0.9,
		common.DESC_DEPLOYMENT_TYPE:             0.95,
		common.DEFAULT_RESOURCE_DEPLOYMENT_TYPE: 1,

		common.RESOURCE_NAME_SOFTWARE_TYPE:    0.85,
		common.DEFAULT_PORT_SOFTWARE_TYPE:     0.9,
		common.IACTEMPLATE_SOFTWARE_TYPE:      0.85,
		common.ORCACONTEXT_SOFTWARE_TYPE:      0.9,
		common.CUSTOMER_DEFINED_SOFTWARE_TYPE: 1,
		common.DEFENDERCONTEXT_SOFTWARE_TYPE:  0.9,
		common.JIRA_SOFTWARE_TYPE:             0.8,
		common.PRECIZE_DEFINED_SOFTWARE_TYPE:  1,
		common.DESC_SOFTWARE_TYPE:             0.95,

		common.ORGUNIT_NAME_TEAM_TYPE:         0.85,
		common.ACCOUNT_NAME_TEAM_TYPE:         0.85,
		common.MGMTGRP_NAME_TEAM_TYPE:         0.85,
		common.SUBSCRIPTION_NAME_TEAM_TYPE:    0.85,
		common.RG_NAME_TEAM_TYPE:              0.85,
		common.FOLDER_NAME_TEAM_TYPE:          0.85,
		common.PROJECT_NAME_TEAM_TYPE:         0.85,
		common.RESOURCE_NAME_TEAM_TYPE:        0.85,
		common.RESOURCE_OWNER_TEAM_TYPE:       0.9,
		common.CUSTOMER_DEFINED_TEAM_TYPE:     1,
		common.RELATED_RESOURCE_TEAM_TYPE:     0.9,
		common.PRECIZE_DEFINED_TEAM_TYPE:      1,
		common.DESC_TEAM_TYPE:                 0.95,
		common.SIMILAR_RESOURCENAME_TEAM_TYPE: 0.8,
		common.SAME_APP_TEAM_TYPE:             0.8,
		common.SAME_TAG_TEAM_TYPE:             0.8,
		common.INHERITED_TEAM_TYPE:            0.9,

		common.RESOURCE_NAME_COMPLIANCE_TYPE:    0.85,
		common.CUSTOMER_DEFINED_COMPLIANCE_TYPE: 1,
		common.PRECIZE_DEFINED_COMPLIANCE_TYPE:  1,
		common.DESC_COMPLIANCE_TYPE:             0.95,
		common.CHILD_COMPLIANCE_TYPE:            0.9,
		common.INHERITED_COMPLIANCE_TYPE:        0.9,

		common.RESOURCE_NAME_SENSITIVITY_TYPE:    0.85,
		common.CUSTOMER_DEFINED_SENSITIVITY_TYPE: 1,
		common.PRECIZE_DEFINED_SENSITIVITY_TYPE:  1,
		common.RESOURCE_DATA_SENSITIVITY_TYPE:    0.9,
		common.ORCACONTEXT_SENSITIVITY_TYPE:      0.9,
		common.JIRACONTEXT_SENSITIVITY_TYPE:      0.8,
		common.DESC_SENSITIVITY_TYPE:             0.95,
		common.CHILD_SENSITIVITY_TYPE:            0.9,
		common.INHERITED_SENSITIVITY_TYPE:        0.9,

		common.CUSTOMER_DEFINED_COSTCENTER_TYPE: 1,
		common.PRECIZE_DEFINED_COSTCENTER_TYPE:  1,
		common.INHERITED_COSTCENTER_TYPE:        0.9,
		// common.PARENT_PATH_COSTCENTER_TYPE:      0.8,

		common.RESOURCE_NAME_TTL_TYPE:    0.85,
		common.CUSTOMER_DEFINED_TTL_TYPE: 1,
		common.PRECIZE_DEFINED_TTL_TYPE:  1,
		common.INHERITED_TTL_TYPE:        0.9,
	}
)
