package confidence

import (
	"github.com/precize/common"
)

func adjustConfidenceForResourceOwnerTeam(confidence *float32, resourceCtxItem *common.ResourceContextItem, resourceContextDoc *common.ResourceContextInsertDoc) {

	if resourceContextDoc != nil {
		if ownerName, ok := resourceCtxItem.Additional["via"].(string); ok {
			// Team of resource owner

			var (
				found           bool
				ownerConfidence float32 = 1
			)

			ownerTypes := [][]common.ResourceContextItem{
				resourceContextDoc.ResourceOwnerTypes.DefinedOwners,
				resourceContextDoc.ResourceOwnerTypes.DerivedOwners,
				resourceContextDoc.ResourceOwnerTypes.CodeOwners,
				resourceContextDoc.ResourceOwnerTypes.OpsOwners,
			}

			for _, ownerType := range ownerTypes {
				for _, owner := range ownerType {
					if owner.Name == ownerName {
						// Owner confidence is evaluated before team confidence
						ownerConfidence = owner.Confidence
						found = true
						break
					}
				}

				if found {
					break
				}
			}

			*confidence = *confidence * (0.5 + 0.5*ownerConfidence)
		}
	}
}

func adjustConfidenceForSimilarityTeam(confidence *float32, resourceCtxItem *common.ResourceContextItem) {

	if uniqueTeams, ok := resourceCtxItem.Additional["uniqueTeams"].(int); ok {
		if uniqueTeams > 1 {
			reduction := float32(uniqueTeams-1) * 0.02
			*confidence -= reduction
		}
	}
}
