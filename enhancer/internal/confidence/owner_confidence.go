package confidence

import (
	"math"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
)

func adjustConfidenceForActivityOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {

	if resourceCtxItem.Event != nil {

		resourceCtxEvent := resourceCtxItem.Event

		if resourceCtxEvent.IndirectEvent {
			*confidence -= 0.1
		}

		if depth, ok := resourceCtxItem.Additional["depth"].(int); ok {
			if depth > 1 {
				reduction := float32(depth-1) * 0.02
				*confidence -= reduction
			}
		}

		if eventTime, err := elastic.ParseDateTime(resourceCtxEvent.Time); err == nil && !eventTime.IsZero() {
			monthsSince := int(time.Since(eventTime).Hours() / (24 * 30))
			if monthsSince > 12 {
				// For every month beyond 12 months, reduce slightly
				reduction := float32(monthsSince-12) * 0.01
				*confidence -= reduction
			}
		}

		if *confidence < 0.70 {
			*confidence = 0.70
		}
	}
}

func adjustConfidenceForRelatedOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {

	if direct, ok := resourceCtxItem.Additional["direct"].(bool); ok {
		if !direct {
			*confidence -= 0.1
		}
	}
}

func adjustConfidenceForResourceTypeOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {

	if resourceTypeCount, ok := resourceCtxItem.Additional["resourceTypeCount"].(int); ok {
		if totalOwnerCount, ok := resourceCtxItem.Additional["totalOwnerCount"].(int); ok {
			if ownerMaxCount, ok := resourceCtxItem.Additional["ownerMaxCount"].(int); ok {

				if resourceTypeCount > 10 {
					reduction := float32(resourceTypeCount-10) / 20 * 0.03
					*confidence -= reduction
				}

				if totalOwnerCount > 3 {
					reduction := float32(totalOwnerCount-3) * 0.01
					*confidence -= reduction
				}

				if resourceTypeCount > 0 {
					ratio := float32(ownerMaxCount) / float32(resourceTypeCount) * 100
					if ratio > 25 {
						// For each 1% above 25%, add 0.004 confidence
						boost := math.Floor(float64(ratio-25)) * 0.004
						*confidence += float32(boost)
					}
				}

				if *confidence < 0.4 {
					*confidence = 0.4
				}

				if *confidence > 0.8 {
					*confidence = 0.8
				}
			}
		}
	}
}

func adjustConfidenceForSmallAccountOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {

	if resourceCount, ok := resourceCtxItem.Additional["resourceCount"].(int); ok {
		if resourceCount > 20 {
			reduction := float32(resourceCount-20) / 20 * 0.03
			*confidence -= reduction
		}

		if *confidence < 0.4 {
			*confidence = 0.4
		}
	}
}

func adjustConfidenceForGroupOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {
	if totalGroupOwnerCount, ok := resourceCtxItem.Additional["totalGroupOwnerCount"].(int); ok {
		if totalGroupOwnerCount > 1 {
			reduction := float32(totalGroupOwnerCount-1) * 0.02
			*confidence -= reduction
		}
	}
}

func adjustConfidenceForReassignedOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {

	if reassignedVia, ok := resourceCtxItem.Additional["reassignedVia"].(string); ok {

		switch reassignedVia {
		case "customer":
			*confidence = 1
		case "employeeReassigned":
			*confidence = 0.8
		case "manager":
			*confidence = 0.7
		case "managerReassigned":
			*confidence = 0.6
		case "managerManager":
			*confidence = 0.5
		}
	}
}

func adjustConfidenceForSimilarityOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {

	if resourceCount, ok := resourceCtxItem.Additional["resourceCount"].(int); ok {
		if totalOwnerCount, ok := resourceCtxItem.Additional["totalOwnerCount"].(int); ok {
			if ownerMaxCount, ok := resourceCtxItem.Additional["ownerMaxCount"].(int); ok {

				if totalOwnerCount > 3 {
					reduction := float32(totalOwnerCount-3) * 0.01
					*confidence -= reduction
				}

				if resourceCount > 0 {
					ratio := float32(ownerMaxCount) / float32(resourceCount) * 100
					if ratio > 25 {
						// For each 1% above 25%, add 0.004 confidence
						boost := math.Floor(float64(ratio-25)) * 0.004
						*confidence += float32(boost)
					}
				}

				if *confidence < 0.5 {
					*confidence = 0.5
				}

				if *confidence > 0.9 {
					*confidence = 0.9
				}
			}
		}
	}
}

func adjustConfidenceForPolicyBindingOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {
	if depth, ok := resourceCtxItem.Additional["depth"].(int); ok {
		if depth > 1 {
			reduction := float32(depth-1) * 0.02
			*confidence -= reduction
		}
	}
}

func adjustConfidenceForDescOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem, resourceContextDoc *common.ResourceContextInsertDoc) {

	if !resourceContextDoc.CreatedDate.IsZero() {
		monthsSince := int(time.Since(resourceContextDoc.CreatedDate).Hours() / (24 * 30))
		if monthsSince > 12 {
			// For every month beyond 12 months, reduce slightly
			reduction := float32(monthsSince-12) * 0.003
			*confidence -= reduction
		}
	}
}

func adjustConfidenceForAttachedResourceOwner(confidence *float32, resourceCtxItem *common.ResourceContextItem) {
	if direct, ok := resourceCtxItem.Additional["direct"].(bool); ok {
		if !direct {
			*confidence -= 0.5
		}
	}
}
