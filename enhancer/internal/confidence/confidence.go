package confidence

import (
	"math"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/common/context"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func SetConfidence(resourceContext *rcontext.ResourceContext, resourceContextDoc *common.ResourceContextInsertDoc, resourceCtxItem *common.ResourceContextItem) {

	var (
		confidence  float32 = 0.1
		contextType string
		ok          bool
	)

	if strings.HasPrefix(resourceCtxItem.Type, contextutils.TAG_PREFIX) {
		confidence = 1
	} else {
		if contextType, ok = common.ContextItemToContextType[resourceCtxItem.Type]; ok {
			confidence = ContextItemConfidence[resourceCtxItem.Type]
		} else {

			if inheritedOwner, ok := resourceCtxItem.Additional["inherited"].(bool); ok && inheritedOwner {
				confidence = 0.4
			} else if defaultResource, ok := resourceCtxItem.Additional["default"].(bool); ok && defaultResource {
				confidence = 0.9
			} else {
				// Unknown type - needs handling
				confidence = -1
				logger.Print(logger.INFO, "Unknown type - no confidence", resourceCtxItem.Type)
				return
			}
		}
	}

	adjustConfidence(&confidence, resourceCtxItem, resourceContextDoc, contextType)

	confidence = float32(math.Floor(float64(confidence)*100) / 100)

	if confidence < 0.4 {
		confidence = 0.4
	}

	if confidence > 1 {
		confidence = 1
	}

	resourceCtxItem.Confidence = confidence
}

func adjustConfidence(confidence *float32, resourceCtxItem *common.ResourceContextItem, resourceContextDoc *common.ResourceContextInsertDoc, contextType string) {

	switch resourceCtxItem.Type {
	case common.ACTIVITY_OWNER_TYPE:
		adjustConfidenceForActivityOwner(confidence, resourceCtxItem)
	case common.RELATED_RESOURCE_OWNER_TYPE:
		adjustConfidenceForRelatedOwner(confidence, resourceCtxItem)
	case common.RESOURCE_TYPE_OWNER_TYPE:
		adjustConfidenceForResourceTypeOwner(confidence, resourceCtxItem)
	case common.FEW_CHILD_OWNERS_PARENT_OWNER_TYPE, common.NO_CHILD_OWNERS_PARENT_OWNER_TYPE:
		adjustConfidenceForSmallAccountOwner(confidence, resourceCtxItem)
	case common.ORG_GROUPOWNER_OWNER_TYPE, common.FOLDER_GROUPOWNER_OWNER_TYPE, common.PROJECT_GROUPOWNER_OWNER_TYPE:
		adjustConfidenceForGroupOwner(confidence, resourceCtxItem)
	case common.REASSIGNED_OWNER_TYPE:
		adjustConfidenceForReassignedOwner(confidence, resourceCtxItem)
	case common.SIMILAR_RESOURCENAME_OWNER_TYPE, common.SAME_APP_OWNER_TYPE, common.SAME_TAG_OWNER_TYPE:
		adjustConfidenceForSimilarityOwner(confidence, resourceCtxItem)
	case common.POLICYBINDING_OWNER_TYPE:
		adjustConfidenceForPolicyBindingOwner(confidence, resourceCtxItem)
	case common.DESC_OWNER_TYPE:
		adjustConfidenceForDescOwner(confidence, resourceCtxItem, resourceContextDoc)
	case common.ATTACHEDRESOURCE_OWNER_TYPE:
		adjustConfidenceForAttachedResourceOwner(confidence, resourceCtxItem)

	case common.MAJORITY_ENV_TYPE:
		adjustConfidenceForMajorityEnv(confidence, resourceCtxItem)

	case common.SIMILAR_RESOURCENAME_APP_TYPE, common.SAME_TAG_APP_TYPE:
		adjustConfidenceForSimilarityApp(confidence, resourceCtxItem)

	case common.RESOURCE_OWNER_TEAM_TYPE:
		adjustConfidenceForResourceOwnerTeam(confidence, resourceCtxItem, resourceContextDoc)
	case common.SIMILAR_RESOURCENAME_TEAM_TYPE, common.SAME_TAG_TEAM_TYPE, common.SAME_APP_TEAM_TYPE:
		adjustConfidenceForSimilarityTeam(confidence, resourceCtxItem)
	}

	if *confidence == 1 { // Defined, customer defined etc.
		if contextType != common.TTL_CONTEXT_TYPE { // Not applicable for TTL
			if !resourceContextDoc.CreatedDate.IsZero() {
				monthsSince := int(time.Since(resourceContextDoc.CreatedDate).Hours() / (24 * 30))
				if monthsSince > 12 {
					// For every month beyond 12 months, reduce slightly
					reduction := float32(monthsSince-12) * 0.003
					*confidence -= reduction
				}
			}
		}

		if *confidence < 0.85 {
			*confidence = 0.85
		}
	}
}
