package property

const (
	ASSET_CONTEXT_TYPE    = "asset"
	IDENTITY_CONTEXT_TYPE = "identity"
	ORG_CONTEXT_TYPE      = "org"

	OWNER_PROPERTY_NAME          = "owner"
	ENVIRONMENT_PROPERTY_NAME    = "environment"
	APP_PROPERTY_NAME            = "app"
	SOFTWARE_PROPERTY_NAME       = "software"
	SENSITIVITY_PROPERTY_NAME    = "sensitivity"
	COMPLIANCE_PROPERTY_NAME     = "compliance"
	DEPLOYMENT_PROPERTY_NAME     = "deployment"
	TTL_PROPERTY_NAME            = "ttl"
	COSTCENTER_PROPERTY_NAME     = "costcenter"
	TEAM_PROPERTY_NAME           = "team"
	USER_AGENT_PROPERTY_NAME     = "userAgent"
	ENHANCED_OWNER_PROPERTY_NAME = "enhancedOwner"
	ENHANCED_TEAM_PROPERTY_NAME  = "enhancedTeam"
	ENHANCED_APP_PROPERTY_NAME   = "enhancedApp"

	IDENTITYNAME_PROPERTY_NAME    = "name"
	EXUSER_PROPERTY_NAME          = "exUser"
	PARTNER_PROPERTY_NAME         = "isPartner"
	IDENTITY_STATUS_PROPERTY_NAME = "identityStatus"
	EMAIL_PROPERTY_NAME           = "email"
	DOMAIN_PROPERTY_NAME          = "domain"
	PARTNER_EMAIL_PROPERTY_NAME   = "partnerEmail"
	IDENTITYOWNER_PROPERTY_NAME   = "owners"
	REPLACEMENT_PROPERTY_NAME     = "replacement"

	DOMAINPROPERTY_HUMAN_VALUE     = "Human"
	DOMAINPROPERTY_NON_HUMAN_VALUE = "Non-Human"

	EMAILSTATUSPROPERTY_DELIVERABLE_VALUE   = "deliverable"
	EMAILSTATUSPROPERTY_UNDELIVERABLE_VALUE = "undeliverable"
)
