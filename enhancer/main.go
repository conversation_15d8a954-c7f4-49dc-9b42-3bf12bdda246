package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/enhance"
	"github.com/precize/logger"
	"github.com/precize/rds"
	"github.com/precize/transport"
)

func main() {
	var (
		appConfigPath   = flag.String("config", "application.yml", "Path to application.yml")
		tenantID        = flag.String("tenant", "0R8Da4gBoELr5xpoQ6Y3", "TenantId to run enhancer for")
		lastCollectedAt = flag.String("collected", "1762367094739", "Last scan time for tenantId")
		serviceID       = flag.String("serviceId", "1000", "ServiceId of the service")
		debug           = flag.Bool("debug", false, "Debug mode")
	)

	flag.Parse()

	logger.InitializeLogs("enhancer", *debug)

	if len(*tenantID) <= 0 {
		logger.Print(logger.ERROR, "TenantId not specified")
		os.Exit(1)
	} else if len(*lastCollectedAt) <= 0 {
		logger.Print(logger.ERROR, "Last collected not specified")
		os.Exit(1)
	}

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	if err = rds.ConnectToPostgres(); err != nil {
		return
	}

	gracefullyShutDown()

	enhance.StartContextProcessing(*tenantID, *lastCollectedAt, *serviceID, *debug)

	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
