package rcontext

import (
	"fmt"
	"slices"
	"strings"
	"sync"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	emailutils "github.com/precize/common/email"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

type ResourceContext struct {
	TenantID                         string
	LastCollectedAt                  string
	PreviousCollectedAt              string
	ServiceID                        string
	EnabledServices                  sync.Map // map[string]bool
	UserResources                    sync.Map // map[string]UserContext
	NetworkInboundPorts              sync.Map // map[string]map[int]struct{}
	GCPParentOwners                  sync.Map // map[string][]string
	GCPFirewallTargets               sync.Map // map[string]map[string][]string
	GCPProjectNumberToID             sync.Map // map[string]string
	GCPProjectIDToNumber             sync.Map // map[string]string
	GCPExternalServiceAccounts       sync.Map // map[string]struct{}
	GCPParentOwnerRoles              sync.Map // map[string]struct{}
	GCPServiceAccountKeyCreatorRoles sync.Map // map[string]struct{}
	GCPProjectOrg                    sync.Map // map[string]string
	AzureTenantIDs                   []string
	AzureManagedRegisteredApps       sync.Map // map[string]struct{}
	UserIDToEmail                    sync.Map
	ParentChildOwner                 sync.Map // map[string]map[string]int
	ParentChildEnv                   sync.Map // map[string]map[string]int
	RelatedResourceList              sync.Map // map[string][]RelatedResource
	SimilarResourceNameList          sync.Map // map[string][]string
	SameAppResourceList              sync.Map // map[string][]string
	SameTagResourceList              sync.Map // map[string][]string
	ResourceTypeOwner                sync.Map // map[string]map[string]map[string]int
	JiraOpsOwners                    sync.Map // map[string]map[string][]string
	IDPMappedUsers                   sync.Map // map[string]map[string]struct{}
	OwnerExceptions                  sync.Map // map[string][]string
	OwnerInclusions                  sync.Map // map[string][]string
	TypoExceptions                   sync.Map // map[string][]string
	EmailNameMatch                   sync.Map // map[string]string
	ResourceContextInsertDocs        sync.Map // map[string]common.ResourceContextInsertDoc
	EmailStatusCache                 sync.Map // map[string]bool
	InvalidEmailsCache               sync.Map // map[string]struct{}
	EmailFormats                     []string
	TenantTfApproach                 string
	SDDLStatus                       bool
	PartialNameMatchCache            sync.Map // map[string]PartialNameCache
	EmailToFullNameMap               sync.Map // map[string]string
	UpdateUndeliverableValidEmails   sync.Map // map[string]struct{}
	AliasUpdateMap                   sync.Map // map[string]bool
	CustomerEntityContext            sync.Map // map[string]common.CustomerEntityContextDoc
	PrimaryDomains                   []string
	ChildToPrimaryEmail              sync.Map // map[string]string
	SensitivityExceptions            sync.Map // map[string][]string
	DerivedEmailExclusions           sync.Map // map[string][]string
	DerivedEmailInclusions           sync.Map // map[string][]string
	IdentityApps                     sync.Map // map[string][]string
	ExEmployeeReplacements           sync.Map // map[string][]string
	ResourceCounts                   sync.Map // map[string]common.ResourceCounts
}

type UserContext struct {
	Name            string
	Email           string
	Active          bool
	IsUser          bool
	IsUserEvaluated bool
	IsInvalid       bool
	Type            map[string]struct{}
	Manager         map[string]string // Manager: Type
	JobTitle        map[string]struct{}
	Department      map[string]struct{}
	Team            map[string]struct{}
	Sources         []UserContextSource
	Groups          map[string]struct{}
	Reassigned      []common.ResourceContextItem
	IsSddl          bool
	SkipUser        bool
	ActiveAccounts  sync.Map // map[string]int
}

func (userContext *UserContext) GetActiveAccounts(accountID string) (int, bool) {
	value, exists := userContext.ActiveAccounts.Load(accountID)
	if !exists {
		return 0, false
	}
	return value.(int), true
}

func (r *UserContext) SetActiveAccounts(accountID string, count int) {
	r.ActiveAccounts.Store(accountID, count)
}

func (uc *UserContext) Print() string {
	var sb strings.Builder

	sb.WriteString("UserContext: ")
	sb.WriteString(fmt.Sprintf(" Name: %s ", uc.Name))
	sb.WriteString(fmt.Sprintf(" Email: %s ", uc.Email))
	sb.WriteString(fmt.Sprintf(" Active: %t ", uc.Active))
	sb.WriteString(fmt.Sprintf(" IsUser: %t ", uc.IsUser))
	sb.WriteString(fmt.Sprintf(" IsUserEvaluated: %t ", uc.IsUserEvaluated))
	sb.WriteString(fmt.Sprintf(" IsInvalid: %t ", uc.IsInvalid))
	sb.WriteString(fmt.Sprintf(" IsSddl: %t ", uc.IsSddl))
	sb.WriteString(fmt.Sprintf(" SkipUser: %t ", uc.SkipUser))
	sb.WriteString(fmt.Sprintf(" Type: %v ", uc.Type))
	sb.WriteString(fmt.Sprintf(" Manager: %v ", uc.Manager))
	sb.WriteString(fmt.Sprintf(" JobTitle: %v ", uc.JobTitle))
	sb.WriteString(fmt.Sprintf(" Department: %v ", uc.Department))
	sb.WriteString(fmt.Sprintf(" Team: %v ", uc.Team))
	sb.WriteString(fmt.Sprintf(" Reassigned: %v ", uc.Reassigned))

	sb.WriteString(" ActiveAccounts: ")
	uc.ActiveAccounts.Range(func(key, value any) bool {
		sb.WriteString(fmt.Sprintf("%v: %v ", key, value))
		return true
	})

	return sb.String()
}

type UserContextSource struct {
	ResourceID, AccountID, IdentityType string
}

type RelatedResource struct {
	ResourceID    string `json:"resourceId"`
	ResourceType  string `json:"resourceType"`
	ResourceDocID string `json:"resourceDocId"`

	Parent                 bool   `json:"-"`
	Priority               bool   `json:"-"`
	ContextualRelation     bool   `json:"-"`
	TransitiveResourceType string `json:"-"`
}

type AccessKeyUser struct {
	Name    string
	Account string
}

type PartialNameCache struct {
	Name, UserResourceKey string
}

type ContextCriteria struct {
	ResourceContextDoc *common.ResourceContextInsertDoc
}

type ContextCriteriaOptions func(*ContextCriteria)

func WithResourceContextDoc(resourceContextDoc *common.ResourceContextInsertDoc) ContextCriteriaOptions {
	return func(c *ContextCriteria) {
		c.ResourceContextDoc = resourceContextDoc
	}
}

func NewResourceContext(tenantID, lastCollectedAt, previousCollectedAt, serviceID string, tenantData tenant.TenantData) *ResourceContext {
	resourceContext := &ResourceContext{
		TenantID:            tenantID,
		LastCollectedAt:     lastCollectedAt,
		PreviousCollectedAt: previousCollectedAt,
		ServiceID:           serviceID,
	}

	enabledServices := tenant.GetEnabledServices(tenantData)
	for k, v := range enabledServices {
		resourceContext.SetEnabledService(k, v)
	}

	return resourceContext
}

func (r *ResourceContext) SetEnabledService(service string, enabled bool) {
	r.EnabledServices.Store(service, enabled)
}

func (r *ResourceContext) GetEnabledService(service string) (bool, bool) {
	value, exists := r.EnabledServices.Load(service)
	if !exists {
		return false, false
	}
	return value.(bool), true
}

func (r *ResourceContext) RangeEnabledServices(f func(service string, enabled bool) bool) {
	r.EnabledServices.Range(func(key, value any) bool {
		return f(key.(string), value.(bool))
	})
}

func (r *ResourceContext) SetUserResource(key string, user *UserContext) {
	r.UserResources.Store(key, user)
}

func (r *ResourceContext) DeleteUserResource(key string) {
	r.UserResources.Delete(key)
}

func (r *ResourceContext) GetUserResource(key string) (*UserContext, bool) {
	value, exists := r.UserResources.Load(key)
	if !exists {
		return nil, false
	}
	return value.(*UserContext), true
}

func (r *ResourceContext) RangeUserResources(f func(key string, user *UserContext) bool) {
	r.UserResources.Range(func(key, value any) bool {
		return f(key.(string), value.(*UserContext))
	})
}

func (r *ResourceContext) SetNetworkInboundPorts(resourceID string, ports map[int]struct{}) {
	r.NetworkInboundPorts.Store(resourceID, ports)
}

func (r *ResourceContext) GetNetworkInboundPorts(resourceID string) (map[int]struct{}, bool) {
	value, exists := r.NetworkInboundPorts.Load(resourceID)
	if !exists {
		return nil, false
	}
	return value.(map[int]struct{}), true
}

func (r *ResourceContext) SetGCPParentOwners(parentID string, owners []string) {
	r.GCPParentOwners.Store(parentID, owners)
}

func (r *ResourceContext) GetGCPParentOwners(parentID string) ([]string, bool) {
	value, exists := r.GCPParentOwners.Load(parentID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetGCPFirewallTargets(resourceID string, targets map[string][]string) {
	r.GCPFirewallTargets.Store(resourceID, targets)
}

func (r *ResourceContext) GetGCPFirewallTargets(resourceID string) (map[string][]string, bool) {
	value, exists := r.GCPFirewallTargets.Load(resourceID)
	if !exists {
		return nil, false
	}

	original := value.(map[string][]string)

	copyMap := make(map[string][]string)
	for k, v := range original {
		copySlice := make([]string, len(v))
		copy(copySlice, v)
		copyMap[k] = copySlice
	}

	return copyMap, true
}

func (r *ResourceContext) RangeGCPFirewallTargets(fn func(network string, targets map[string][]string) bool) {
	r.GCPFirewallTargets.Range(func(key, value any) bool {
		network := key.(string)
		targets := value.(map[string][]string)
		return fn(network, targets)
	})
}

func (r *ResourceContext) SetParentChildOwner(parentID string, childOwners map[string]int) {
	r.ParentChildOwner.Store(parentID, childOwners)
}

func (r *ResourceContext) GetParentChildOwner(parentID string) (map[string]int, bool) {
	value, exists := r.ParentChildOwner.Load(parentID)
	if !exists {
		return nil, false
	}
	originalMap, ok := value.(map[string]int)
	if !ok {
		return nil, false
	}

	copyMap := make(map[string]int, len(originalMap))
	for k, v := range originalMap {
		copyMap[k] = v
	}
	return copyMap, true
}

func (r *ResourceContext) SetParentChildEnv(parentID string, childEnvs map[string]int) {
	r.ParentChildEnv.Store(parentID, childEnvs)
}

func (r *ResourceContext) GetParentChildEnv(parentID string) (map[string]int, bool) {
	value, exists := r.ParentChildEnv.Load(parentID)
	if !exists {
		return nil, false
	}

	originalMap, ok := value.(map[string]int)
	if !ok {
		return nil, false
	}

	copyMap := make(map[string]int, len(originalMap))
	for k, v := range originalMap {
		copyMap[k] = v
	}
	return copyMap, true
}

func (r *ResourceContext) SetRelatedResourceList(resourceID string, relatedResources []RelatedResource) {
	r.RelatedResourceList.Store(resourceID, relatedResources)
}

func (r *ResourceContext) GetRelatedResourceList(resourceID string) ([]RelatedResource, bool) {
	value, exists := r.RelatedResourceList.Load(resourceID)
	if !exists {
		return nil, false
	}
	return value.([]RelatedResource), true
}

func (r *ResourceContext) RangeRelatedResourceList(f func(key string, user []RelatedResource) bool) {
	r.RelatedResourceList.Range(func(key, value any) bool {
		return f(key.(string), value.([]RelatedResource))
	})
}

func (r *ResourceContext) SetSimilarResourceNameList(resourceDocID string, similarDocIDs []string) {
	r.SimilarResourceNameList.Store(resourceDocID, similarDocIDs)
}

func (r *ResourceContext) GetSimilarResourceNameList(resourceDocID string) ([]string, bool) {
	value, exists := r.SimilarResourceNameList.Load(resourceDocID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetSameAppResourceList(resourceDocID string, similarDocIDs []string) {
	r.SameAppResourceList.Store(resourceDocID, similarDocIDs)
}

func (r *ResourceContext) GetSameAppResourceList(resourceDocID string) ([]string, bool) {
	value, exists := r.SameAppResourceList.Load(resourceDocID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetSameTagResourceList(resourceDocID string, similarDocIDs []string) {
	r.SameTagResourceList.Store(resourceDocID, similarDocIDs)
}

func (r *ResourceContext) GetSameTagResourceList(resourceDocID string) ([]string, bool) {
	value, exists := r.SameTagResourceList.Load(resourceDocID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetResourceTypeOwner(resourceType string, ownerInfo map[string]map[string]int) {
	r.ResourceTypeOwner.Store(resourceType, ownerInfo)
}

func (r *ResourceContext) GetResourceTypeOwner(resourceType string) (map[string]map[string]int, bool) {
	value, exists := r.ResourceTypeOwner.Load(resourceType)
	if !exists {
		return nil, false
	}

	original := value.(map[string]map[string]int)

	copyMap := make(map[string]map[string]int)
	for k, v := range original {
		innerMap := make(map[string]int)
		for innerK, innerV := range v {
			innerMap[innerK] = innerV
		}
		copyMap[k] = innerMap
	}

	return copyMap, true
}

func (r *ResourceContext) RangeResourceTypeOwner(f func(resourceType string, ownerInfo map[string]map[string]int) bool) {
	r.ResourceTypeOwner.Range(func(key, value any) bool {
		return f(key.(string), value.(map[string]map[string]int))
	})
}

func (r *ResourceContext) SetJiraOpsOwners(jiraID string, ownersByType map[string][]string) {
	r.JiraOpsOwners.Store(jiraID, ownersByType)
}

func (r *ResourceContext) GetJiraOpsOwners(jiraID string) (map[string][]string, bool) {
	value, exists := r.JiraOpsOwners.Load(jiraID)
	if !exists {
		return nil, false
	}
	original := value.(map[string][]string)
	copyMap := make(map[string][]string)

	for k, v := range original {
		copySlice := make([]string, len(v))
		copy(copySlice, v)
		copyMap[k] = copySlice
	}

	return copyMap, true
}

func (r *ResourceContext) GetIDPMappedUsers(idpID string) (map[string]struct{}, bool) {
	value, exists := r.IDPMappedUsers.Load(idpID)
	if !exists {
		return nil, false
	}

	originalMap, ok := value.(map[string]struct{})
	if !ok {
		return nil, false
	}

	copyMap := make(map[string]struct{}, len(originalMap))
	for k, v := range originalMap {
		copyMap[k] = v
	}

	return copyMap, true
}

func (r *ResourceContext) SetIDPMappedUser(idpID, userID string) {
	value, exists := r.IDPMappedUsers.Load(idpID)
	var users map[string]struct{}
	if exists {
		users = value.(map[string]struct{})
	} else {
		users = make(map[string]struct{})
	}

	users[userID] = struct{}{}
	r.IDPMappedUsers.Store(idpID, users)
}

func (r *ResourceContext) SetOwnerExceptions(resourceID string, exceptions []string) {
	r.OwnerExceptions.Store(resourceID, exceptions)
}

func (r *ResourceContext) GetOwnerExceptions(resourceID string) ([]string, bool) {
	value, exists := r.OwnerExceptions.Load(resourceID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetOwnerInclusions(resourceID string, exceptions []string) {
	r.OwnerInclusions.Store(resourceID, exceptions)
}

func (r *ResourceContext) GetOwnerInclusions(resourceID string) ([]string, bool) {
	value, exists := r.OwnerInclusions.Load(resourceID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetTypoExceptions(resourceID string, exceptions []string) {
	r.TypoExceptions.Store(resourceID, exceptions)
}

func (r *ResourceContext) GetTypoExceptions(resourceID string) ([]string, bool) {
	value, exists := r.TypoExceptions.Load(resourceID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetResourceContextInsertDoc(resourceID string, doc common.ResourceContextInsertDoc) {
	r.ResourceContextInsertDocs.Store(resourceID, doc)
}

func (r *ResourceContext) GetResourceContextInsertDoc(resourceID string) (common.ResourceContextInsertDoc, bool) {
	value, exists := r.ResourceContextInsertDocs.Load(resourceID)
	if !exists {
		return common.ResourceContextInsertDoc{}, false
	}
	return value.(common.ResourceContextInsertDoc), true
}

func (r *ResourceContext) RangeResourceContextInsertDocs(fn func(resourceID string, doc common.ResourceContextInsertDoc) bool) {
	r.ResourceContextInsertDocs.Range(func(key, value any) bool {
		resourceID := key.(string)
		resourceContextDoc := value.(common.ResourceContextInsertDoc)
		return fn(resourceID, resourceContextDoc)
	})
}

func (r *ResourceContext) SetEmailStatus(email string, status bool) {
	r.EmailStatusCache.Store(email, status)
}

func (r *ResourceContext) GetEmailStatus(email string) (bool, bool) {
	value, exists := r.EmailStatusCache.Load(email)
	if !exists {
		return false, false
	}
	return value.(bool), true
}

func (r *ResourceContext) SetInvalidEmailCache(email string) {
	r.InvalidEmailsCache.Store(email, struct{}{})
}

func (r *ResourceContext) DeleteInvalidEmailCache(email string) {
	r.InvalidEmailsCache.Delete(email)
}

func (r *ResourceContext) GetInvalidEmailCache(email string) bool {
	_, exists := r.InvalidEmailsCache.Load(email)
	return exists
}

func (r *ResourceContext) SetPartialNameMatch(partialName string, cache PartialNameCache) {
	r.PartialNameMatchCache.Store(partialName, cache)
}

func (r *ResourceContext) GetPartialNameMatch(partialName string) (PartialNameCache, bool) {
	value, exists := r.PartialNameMatchCache.Load(partialName)
	if !exists {
		return PartialNameCache{}, false
	}
	return value.(PartialNameCache), true
}

func (r *ResourceContext) SetEmailToFullName(email, fullName string) {
	r.EmailToFullNameMap.Store(email, fullName)
}

func (r *ResourceContext) GetEmailToFullName(email string) (string, bool) {
	value, exists := r.EmailToFullNameMap.Load(email)
	if !exists {
		return "", false
	}
	return value.(string), true
}

func (r *ResourceContext) SetUpdateUndeliverableValidEmail(email string) {
	r.UpdateUndeliverableValidEmails.Store(email, struct{}{})
}

func (r *ResourceContext) GetUpdateUndeliverableValidEmail(email string) bool {
	_, exists := r.UpdateUndeliverableValidEmails.Load(email)
	return exists
}

func (r *ResourceContext) DeleteUpdateUndeliverableValidEmail(email string) {
	r.UpdateUndeliverableValidEmails.Delete(email)
}

func (r *ResourceContext) RangeUpdateUndeliverableValidEmails(fn func(email string)) {
	r.UpdateUndeliverableValidEmails.Range(func(key, _ any) bool {
		if email, ok := key.(string); ok {
			fn(email)
		}
		return true
	})
}

func (r *ResourceContext) SetAliasUpdate(email string, update bool) {
	r.AliasUpdateMap.Store(email, update)
}

func (r *ResourceContext) GetAliasUpdate(email string) (bool, bool) {
	value, exists := r.AliasUpdateMap.Load(email)
	if !exists {
		return false, false
	}
	return value.(bool), true
}

func (r *ResourceContext) RangeAliasUpdateMap(fn func(email string, update bool) bool) {
	r.AliasUpdateMap.Range(func(key, value any) bool {
		email := key.(string)
		update := value.(bool)
		return fn(email, update)
	})
}

func (r *ResourceContext) SetCustomerEntityContext(entityID string, context common.CustomerEntityContextDoc) {

	if existingContext, exists := r.GetCustomerEntityContext(entityID); exists {

		for i, newProp := range context.ContextProperties {
			for _, existingProp := range existingContext.ContextProperties {

				if newProp.PropertyName == existingProp.PropertyName {

					for _, existingInclude := range existingProp.Include {
						found := slices.Contains(newProp.Include, existingInclude)
						if !found {
							context.ContextProperties[i].Include = append(context.ContextProperties[i].Include, existingInclude)
						}
					}

					for _, existingExclude := range existingProp.Exclude {
						found := slices.Contains(newProp.Exclude, existingExclude)
						if !found {
							context.ContextProperties[i].Exclude = append(context.ContextProperties[i].Exclude, existingExclude)
						}
					}
				}
			}
		}
	}

	r.CustomerEntityContext.Store(entityID, context)
}

func (r *ResourceContext) GetCustomerEntityContext(entityID string) (common.CustomerEntityContextDoc, bool) {
	value, exists := r.CustomerEntityContext.Load(entityID)
	if !exists {
		return common.CustomerEntityContextDoc{}, false
	}
	return value.(common.CustomerEntityContextDoc), true
}

func (r *ResourceContext) SetChildPrimaryEmail(childEmail, email string, forceMerge bool) bool {

	if !forceMerge {
		if len(emailutils.GetDomainFromEmail(childEmail)) > 0 && len(emailutils.GetDomainFromEmail(email)) > 0 {
			if !emailutils.EmailsTLDDiff(childEmail, email) {
				// email has proper domains
				if cEmail, ok := r.GetChildPrimaryEmail(email + contextutils.PARENT_CHILD_INVERSE); ok && cEmail != childEmail {
					personalEmail := emailutils.IsPersonalEmail(childEmail)
					if !personalEmail {
						logger.Print(logger.INFO, "Not overwriting child primary email as proxy exists", []string{r.TenantID}, childEmail, email, cEmail)
						return false
					}
				}
			}
		}
	}

	childEmail = strings.ToLower(childEmail)
	email = strings.ToLower(email)
	if childEmail != email {
		logger.Print(logger.INFO, "Setting child primary email", childEmail, email, forceMerge)
		r.ChildToPrimaryEmail.Store(childEmail, email)
	}

	return true
}

func (r *ResourceContext) GetChildPrimaryEmail(childEmail string) (string, bool) {
	value, exists := r.ChildToPrimaryEmail.Load(childEmail)
	if !exists {
		return "", false
	}

	// for inverse we don't need to go to deeper levels
	if !strings.Contains(childEmail, contextutils.PARENT_CHILD_INVERSE) {
		iteration := 0
		for {
			iteration++
			if nextValue, exists := r.ChildToPrimaryEmail.Load(value); exists {
				value = nextValue
			} else {
				break
			}

			if iteration > 5 {
				logger.Print(logger.INFO, "Too many iterations in GetChildPrimaryEmail", []string{r.TenantID}, childEmail, fmt.Sprintf("%v", value))
				break
			}
		}
	}
	return value.(string), true
}

func (r *ResourceContext) RangeChildPrimaryEmail(fn func(childEmail, email string) bool) {
	r.ChildToPrimaryEmail.Range(func(key, value any) bool {
		childEmail := key.(string)
		email := value.(string)
		return fn(childEmail, email)
	})
}

func (r *ResourceContext) SetSensitivityExceptions(resourceID string, exceptions []string) {
	r.SensitivityExceptions.Store(resourceID, exceptions)
}

func (r *ResourceContext) GetSensitivityExceptions(resourceID string) ([]string, bool) {
	value, exists := r.SensitivityExceptions.Load(resourceID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetGCPExternalServiceAccounts(serviceAccount string) {
	r.GCPExternalServiceAccounts.Store(serviceAccount, struct{}{})
}

func (r *ResourceContext) GetGCPExternalServiceAccounts(serviceAccount string) bool {
	_, exists := r.GCPExternalServiceAccounts.Load(serviceAccount)
	return exists
}

func (r *ResourceContext) SetAzureManagedRegisteredApps(registeredAppID, appName string) {
	r.AzureManagedRegisteredApps.Store(registeredAppID, appName)
}

func (r *ResourceContext) GetAzureManagedRegisteredApps(registeredAppID string) (string, bool) {
	value, exists := r.AzureManagedRegisteredApps.Load(registeredAppID)
	if !exists {
		return "", false
	}
	return value.(string), true
}

func (r *ResourceContext) SetGCPParentOwnerRoles(role string) {
	r.GCPParentOwnerRoles.Store(role, struct{}{})
}

func (r *ResourceContext) GetGCPParentOwnerRoles(role string) bool {
	_, exists := r.GCPParentOwnerRoles.Load(role)
	return exists
}

func (r *ResourceContext) SetGCPServiceAccountKeyCreatorRoles(role string) {
	r.GCPServiceAccountKeyCreatorRoles.Store(role, struct{}{})
}

func (r *ResourceContext) GetGCPServiceAccountKeyCreatorRoles(role string) bool {
	_, exists := r.GCPServiceAccountKeyCreatorRoles.Load(role)
	return exists
}

func (r *ResourceContext) SetGCPProjectNumberToID(projectNumber, projectID string) {
	r.GCPProjectNumberToID.Store(projectNumber, projectID)
}

func (r *ResourceContext) GetGCPProjectNumberToID(projectNumber string) (string, bool) {
	value, exists := r.GCPProjectNumberToID.Load(projectNumber)
	if !exists {
		return "", false
	}
	return value.(string), true
}

func (r *ResourceContext) SetGCPProjectIDToNumber(projectID, projectNumber string) {
	r.GCPProjectIDToNumber.Store(projectID, projectNumber)
}

func (r *ResourceContext) GetGCPProjectIDToNumber(projectID string) (string, bool) {
	value, exists := r.GCPProjectIDToNumber.Load(projectID)
	if !exists {
		return "", false
	}
	return value.(string), true
}

func (r *ResourceContext) SetGCPProjectOrg(project, org string) {
	r.GCPProjectOrg.Store(project, org)
}

func (r *ResourceContext) GetGCPProjectOrg(project string) (string, bool) {
	value, exists := r.GCPProjectOrg.Load(project)
	if !exists {
		return "", false
	}
	return value.(string), true
}

func (r *ResourceContext) SetOwnerEmailName(email, name string) {
	r.EmailNameMatch.Store(email, name)
}

func (r *ResourceContext) GetOwnerEmailName(email string) (string, bool) {
	value, exists := r.EmailNameMatch.Load(email)
	if !exists {
		return "", false
	}
	return value.(string), true
}

func (r *ResourceContext) SetUserIDToEmail(userID, email string) {
	r.UserIDToEmail.Store(userID, email)
}

func (r *ResourceContext) GetUserIDToEmail(userID string) (string, bool) {
	value, exists := r.UserIDToEmail.Load(userID)
	if !exists {
		return "", false
	}
	return value.(string), true
}

func (r *ResourceContext) SetDerivedEmailExclusions(resourceID string, exceptions []string) {
	r.DerivedEmailExclusions.Store(resourceID, exceptions)
}

func (r *ResourceContext) GetDerivedEmailExclusions(resourceID string) ([]string, bool) {
	value, exists := r.DerivedEmailExclusions.Load(resourceID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetDerivedEmailInclusions(resourceID string, exceptions []string) {
	r.DerivedEmailInclusions.Store(resourceID, exceptions)
}

func (r *ResourceContext) GetDerivedEmailInclusions(resourceID string) ([]string, bool) {
	value, exists := r.DerivedEmailInclusions.Load(resourceID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetIdentityApps(identityID string, apps []string) {
	r.IdentityApps.Store(identityID, apps)
}

func (r *ResourceContext) GetIdentityApps(identityID string) ([]string, bool) {
	value, exists := r.IdentityApps.Load(identityID)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) SetExEmployeeReplacements(exEmployeeEmail string, replacement []string) {
	r.ExEmployeeReplacements.Store(exEmployeeEmail, replacement)
}

func (r *ResourceContext) GetExEmployeeReplacements(exEmployeeEmail string) ([]string, bool) {
	value, exists := r.ExEmployeeReplacements.Load(exEmployeeEmail)
	if !exists {
		return nil, false
	}
	return value.([]string), true
}

func (r *ResourceContext) RangeExEmployeeReplacements(f func(key string, replacements []string) bool) {
	r.ExEmployeeReplacements.Range(func(key, value any) bool {
		return f(key.(string), value.([]string))
	})
}

func (r *ResourceContext) SetResourceCounts(accountID string, resourceCounts common.ResourceCounts) {
	r.ResourceCounts.Store(accountID, resourceCounts)
}

func (r *ResourceContext) IncrementResourceCounts(resourceContextDoc *common.ResourceContextInsertDoc) {

	resourceCounts, _ := r.GetResourceCounts(resourceContextDoc.Account)

	common.ResourceCountMutex.Lock()

	resourceCounts.AccountCount++

	if resourceCounts.ResourceTypeCount == nil {
		resourceCounts.ResourceTypeCount = make(map[string]int)
	}

	resourceCounts.ResourceTypeCount[resourceContextDoc.ResourceType]++

	if len(resourceContextDoc.ResourceGroup) > 0 {
		if resourceCounts.ResourceGroupCount == nil {
			resourceCounts.ResourceGroupCount = make(map[string]int)
		}

		resourceCounts.ResourceGroupCount[resourceContextDoc.ResourceGroup]++
	}

	r.SetResourceCounts(resourceContextDoc.Account, resourceCounts)

	common.ResourceCountMutex.Unlock()
}

func (r *ResourceContext) GetResourceCounts(accountID string) (common.ResourceCounts, bool) {
	value, exists := r.ResourceCounts.Load(accountID)
	if !exists {
		return common.ResourceCounts{}, false
	}
	return value.(common.ResourceCounts), true
}
