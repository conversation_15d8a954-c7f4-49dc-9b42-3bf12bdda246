package attribute

import (
	"encoding/json"
	"path"
	"strings"

	"github.com/precize/common"
	contextutils "github.com/precize/common/context"
	"github.com/precize/enhancer/activity"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/internal/resource"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/related"
	"github.com/precize/enhancer/source/code"
)

type TagStruct struct {
	Key   string
	Value string
}

func GetTagContextOfResource(resourceContext *rcontext.ResourceContext, resourcesDoc map[string]any,
	resourceContextInsertDoc *common.ResourceContextInsertDoc) (resourceNameTagValue string, tags []TagStruct) {

	var (
		uniqueOwners        = make(map[string]struct{})
		uniqueDeployments   = make(map[string]struct{})
		uniqueSoftwares     = make(map[string]struct{})
		uniqueApps          = make(map[string]struct{})
		uniqueCompliances   = make(map[string]struct{})
		uniqueSensitivities = make(map[string]struct{})
		uniqueEnvironments  = make(map[string]struct{})
		uniqueTeams         = make(map[string]struct{})
		uniqueFileNames     = make(map[string]struct{})
		uniqueRepoNames     = make(map[string]struct{})
		uniqueCommitIDs     = make(map[string]struct{})
	)

	if resourceTags, ok := resourcesDoc["tags"].([]any); ok {

		for _, resourceTag := range resourceTags {

			if resourceTagMap, ok := resourceTag.(map[string]any); ok {

				if tagValue, ok := resourceTagMap["value"].(string); ok {

					if tagKey, ok := resourceTagMap["key"].(string); ok {

						tags = append(tags, TagStruct{Key: tagKey, Value: tagValue})

						if context.IsOwnerKey(tagKey) {

							var values []string

							if strings.Contains(tagValue, ",") {
								values = strings.Split(tagValue, ",")
							} else {
								values = []string{tagValue}
							}

							for _, value := range values {

								if strings.HasPrefix(value, "arn:") {
									// Special case if value is an aws arn of user or role
									value = path.Base(value)
								}

								if _, ok := uniqueOwners["Defined"+value]; !ok {

									uniqueOwners["Defined"+value] = struct{}{}

									if emailutils.IsNonHumanEmail(value, resourceContext) {
										uniqueGroupEmailIdentities := make(map[string]struct{})
										activity.GetNonHumanEmailOwners(resourceContext, value, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+value+" has been defined as resource owner via tag: "+tagKey, 1)
									}

									rctxItem := context.GetContextItem(value, contextutils.TAG_PREFIX+tagKey, "User has been defined as resource owner via tag: "+tagKey, context.WithUserEvaluate(resourceContext, "", resourceContextInsertDoc.Account, nil))

									if context.IsCreatorKey(tagKey) {
										rctxItem.OwnerSubType = common.CREATOR_RCTXITEM_OWNERTYPE
									}
									resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
										rctxItem,
									)
								}
							}
						}

						if IsDescriptionKey(tagKey) {

							if softwareNames := context.GetSoftwareNameListFromValue(tagValue); len(softwareNames) > 0 {

								for _, softwareName := range softwareNames {
									if _, ok := uniqueSoftwares[softwareName]; !ok {

										uniqueSoftwares[softwareName] = struct{}{}

										resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(
											resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
											context.GetContextItem(softwareName, contextutils.TAG_PREFIX+tagKey, "Software has been defined via tag: "+tagKey),
										)
									}
								}
							}

							if appNames := context.GetAppNameListFromValue(tagValue, rcontext.WithResourceContextDoc(resourceContextInsertDoc)); len(appNames) > 0 {

								for _, appName := range appNames {
									if _, ok := uniqueApps[appName]; !ok {

										uniqueApps[appName] = struct{}{}

										resourceContextInsertDoc.ResourceAppTypes.DerivedApp = append(
											resourceContextInsertDoc.ResourceAppTypes.DerivedApp,
											context.GetContextItem(appName, contextutils.TAG_PREFIX+tagKey, "Application has been defined via tag: "+tagKey),
										)
									}
								}
							}

							if teamNames := context.GetTeamNameListFromValue(tagValue); len(teamNames) > 0 {

								for _, teamName := range teamNames {
									if _, ok := uniqueTeams[teamName]; !ok {

										uniqueTeams[teamName] = struct{}{}

										resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(
											resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
											context.GetContextItem(teamName, contextutils.TAG_PREFIX+tagKey, "Team has been defined via tag: "+tagKey),
										)
									}
								}
							}

							if deploymentNames := context.GetDeploymentNamesFromValue(tagValue); len(deploymentNames) > 0 {

								for _, deploymentName := range deploymentNames {
									if _, ok := uniqueDeployments[deploymentName]; !ok {

										uniqueDeployments[deploymentName] = struct{}{}

										resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
											resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
											context.GetContextItem(deploymentName, contextutils.TAG_PREFIX+tagKey, "Deployment has been defined via tag: "+tagKey),
										)
									}
								}
							}

							if sensitivityNames := context.GetSensitivityNameListFromValue(tagValue); len(sensitivityNames) > 0 {

								for _, sensitivityName := range sensitivityNames {
									if _, ok := uniqueSensitivities[sensitivityName]; !ok {

										uniqueSensitivities[sensitivityName] = struct{}{}

										resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(
											resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
											context.GetContextItem(sensitivityName, contextutils.TAG_PREFIX+tagKey, "Sensitivity has been defined via tag: "+tagKey),
										)
									}
								}
							}

							if complianceNames := context.GetComplianceNameListFromValue(tagValue); len(complianceNames) > 0 {

								for _, complianceName := range complianceNames {
									if _, ok := uniqueCompliances[complianceName]; !ok {

										uniqueCompliances[complianceName] = struct{}{}

										resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance = append(
											resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance,
											context.GetContextItem(complianceName, contextutils.TAG_PREFIX+tagKey, "Compliance has been defined via tag: "+tagKey),
										)
									}
								}
							}

							users := emailutils.SeparateEmailsFromDescription(tagValue)

							names := context.GetNamesFromDescription(tagValue, resourceContext.TenantID)

							names = emailutils.RemoveNamesOfTaggedEmails(names, users)
							users = append(users, names...)

							for _, user := range users {
								if _, ok := uniqueOwners["Derived"+user]; !ok {

									uniqueOwners["Derived"+user] = struct{}{}

									if emailutils.IsNonHumanEmail(user, resourceContext) {
										uniqueGroupEmailIdentities := make(map[string]struct{})
										activity.GetNonHumanEmailOwners(resourceContext, user, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+user+" has been derived from resource description", 1)
									}

									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
										resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
										context.GetContextItem(user, contextutils.TAG_PREFIX+tagKey, "User has been derived from resource description", context.WithUserEvaluate(resourceContext, "", resourceContextInsertDoc.Account, nil)),
									)
								}
							}
						}

						for envTagKey := range contextutils.EnvTagKeys {

							if strings.Contains(strings.ToLower(tagKey), envTagKey) {

								if envName := context.GetEnvironmentNameFromValue(tagValue); len(envName) > 0 {

									if _, ok := uniqueEnvironments[envName]; !ok {

										uniqueEnvironments[envName] = struct{}{}

										resourceContextInsertDoc.ResourceEnvTypes.DefinedEnv = append(
											resourceContextInsertDoc.ResourceEnvTypes.DefinedEnv,
											context.GetContextItem(envName, contextutils.TAG_PREFIX+tagKey, "Environment has been defined via tag: "+tagKey),
										)

										accountID, _ := resourcesDoc["accountId"].(string)
										resourceGroup, _ := resourcesDoc["resourceGroup"].(string)
										context.IncrementParentChildEnvCount(resourceContext, envName, accountID, resourceGroup)
									}
								}
							}
						}

						if context.IsTTLKey(tagKey) {

							entityJSONString, _ := resourcesDoc["entityJson"].(string)

							entityJSON := make(map[string]any)
							if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err != nil {
								continue
							}

							ttlValue := context.GetTTLFromValue(tagValue, true, resourceutils.GetCreateTimeOfResource(entityJSON), resourceContext.LastCollectedAt)

							if len(ttlValue) > 0 {
								resourceContextInsertDoc.ResourceTTLTypes.DefinedTTL = append(resourceContextInsertDoc.ResourceTTLTypes.DefinedTTL, context.GetContextItem(ttlValue, contextutils.TAG_PREFIX+tagKey, "TTL has been defined via tag: "+tagKey))
							}
						}

						// Software components are sometimes tagged as application. Hence need to check if tag value for app or application is software or not
						var tagValueSoftware bool

						if _, ok := contextutils.SoftwareTagKeys[strings.ToLower(tagKey)]; ok {

							if softwareName := context.GetSoftwareNameFromValue(tagValue); len(softwareName) > 0 {

								tagValueSoftware = true

								if _, ok := uniqueSoftwares[softwareName]; !ok {

									uniqueSoftwares[softwareName] = struct{}{}

									resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(
										resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
										context.GetContextItem(softwareName, contextutils.TAG_PREFIX+tagKey, "Software has been defined via tag: "+tagKey),
									)
								}
							}
						}

						var skipAppTags bool

						// Cluster resources automatically assigns kubernetes app inside app tag which can be random string
						if strings.Contains(resourceContextInsertDoc.ResourceID, "/k8s/") && resourceContextInsertDoc.ServiceID == common.GCP_SERVICE_ID_INT {
							switch resourceContextInsertDoc.ResourceType {
							case common.GCP_GKENODE_RESOURCE_TYPE:
							default:
								skipAppTags = true
							}
						}

						if _, ok := contextutils.AppTagKeys[strings.ToLower(tagKey)]; ok && len(tagValue) > 0 && !skipAppTags && !tagValueSoftware {

							var appNameTagValue string

							if appName := context.GetAppNameFromValue(tagValue, rcontext.WithResourceContextDoc(resourceContextInsertDoc)); len(appName) > 0 {
								appNameTagValue = appName
							} else {
								appNameTagValue = resourceutils.FormatContextValue(tagValue)
								// Add new global app - future scans will pick it up
								context.AddApplicationToGlobalApps(appNameTagValue, resourceContext.TenantID)
							}

							if _, ok := uniqueApps[appNameTagValue]; !ok {

								uniqueApps[appNameTagValue] = struct{}{}

								resourceContextInsertDoc.ResourceAppTypes.DefinedApp = append(
									resourceContextInsertDoc.ResourceAppTypes.DefinedApp,
									context.GetContextItem(appNameTagValue, contextutils.TAG_PREFIX+tagKey, "Application has been defined via tag: "+tagKey),
								)
							}
						}

						// move this from here
						if resourceContextInsertDoc.ResourceType == common.GCP_GKENODE_RESOURCE_TYPE ||
							resourceContextInsertDoc.ResourceType == common.GCP_GKECLUSTER_RESOURCE_TYPE ||
							strings.Contains(resourceContextInsertDoc.ResourceType, "k8s.io") {

							if _, ok := uniqueDeployments[contextutils.KUBERNETES_DEPLOYMENT]; !ok {

								uniqueDeployments[contextutils.KUBERNETES_DEPLOYMENT] = struct{}{}

								resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment = append(
									resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment,
									context.GetContextItem(contextutils.KUBERNETES_DEPLOYMENT, common.RESOURCETYPE_DEPLOYMENT_TYPE, "Deployment has been defined via tag: "+tagKey),
								)
							}
						}

						if context.IsTeamKey(tagKey) && len(tagValue) > 0 {

							team := resourceutils.FormatContextValue(tagValue)

							if teamName := context.GetTeamNameFromValue(tagValue); strings.ToLower(teamName) == strings.ToLower(team) {
								// If it is just a case change, get proper case so that it does not create a new team
								team = teamName
							} else {
								// Add new global team - future scans will pick it up
								context.AddTeamToGlobalTeams(team, resourceContext.TenantID)
							}

							if _, ok := uniqueTeams[team]; !ok {

								uniqueTeams[team] = struct{}{}

								resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam = append(
									resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam,
									context.GetContextItem(team, contextutils.TAG_PREFIX+tagKey, "Team has been defined via tag: "+tagKey),
								)
							}
						} else {

							if teamName := context.GetTeamNameFromValue(tagValue); len(teamName) > 0 {

								team := teamName

								if _, ok := uniqueTeams[team]; !ok {

									uniqueTeams[team] = struct{}{}

									resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam = append(
										resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam,
										context.GetContextItem(team, contextutils.TAG_PREFIX+tagKey, "Team has been defined via tag: "+tagKey),
									)
								}
							}
						}

						if deploymentNames := context.GetDeploymentNamesFromValue(tagKey); len(deploymentNames) > 0 {

							for _, deploymentName := range deploymentNames {

								if !strings.Contains(tagValue, "false") {

									if _, ok := uniqueDeployments[deploymentName]; !ok {

										uniqueDeployments[deploymentName] = struct{}{}

										resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment = append(
											resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment,
											context.GetContextItem(deploymentName, contextutils.TAG_PREFIX+tagKey, "Deployment has been defined via tag: "+tagKey),
										)
									}
								}
							}
						}

						if deploymentNames := context.GetDeploymentNamesFromValue(tagValue); len(deploymentNames) > 0 {

							for _, deploymentName := range deploymentNames {

								if _, ok := uniqueDeployments[deploymentName]; !ok {

									uniqueDeployments[deploymentName] = struct{}{}

									resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment = append(
										resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment,
										context.GetContextItem(deploymentName, contextutils.TAG_PREFIX+tagKey, "Deployment has been defined via tag: "+tagKey),
									)
								}
							}
						}

						if resourceutils.IsDataResourceType(resourceContextInsertDoc.ResourceType) {

							if complianceName := context.GetComplianceNameFromValue(tagKey); len(complianceName) > 0 {

								if !strings.Contains(tagValue, "false") {

									if _, ok := uniqueCompliances[complianceName]; !ok {

										uniqueCompliances[complianceName] = struct{}{}

										resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance = append(
											resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance,
											context.GetContextItem(complianceName, contextutils.TAG_PREFIX+tagKey, "Compliance has been defined via tag: "+tagKey),
										)
									}
								}
							}

							if complianceName := context.GetComplianceNameFromValue(tagValue); len(complianceName) > 0 {

								if _, ok := uniqueCompliances[complianceName]; !ok {

									uniqueCompliances[complianceName] = struct{}{}

									resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance = append(
										resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance,
										context.GetContextItem(complianceName, contextutils.TAG_PREFIX+tagKey, "Compliance has been defined via tag: "+tagKey),
									)
								}
							}

							if sensitivityName := context.GetSensitivityNameFromValue(tagKey); len(sensitivityName) > 0 {

								if !strings.Contains(tagValue, "false") {

									if _, ok := uniqueSensitivities[sensitivityName]; !ok {

										uniqueSensitivities[sensitivityName] = struct{}{}

										resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity = append(
											resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity,
											context.GetContextItem(sensitivityName, contextutils.TAG_PREFIX+tagKey, "Sensitivity has been defined via tag: "+tagKey),
										)
									}
								}
							}

							if sensitivityName := context.GetSensitivityNameFromValue(tagValue); len(sensitivityName) > 0 {

								if _, ok := uniqueSensitivities[sensitivityName]; !ok {

									uniqueSensitivities[sensitivityName] = struct{}{}

									resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity = append(
										resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity,
										context.GetContextItem(sensitivityName, contextutils.TAG_PREFIX+tagKey, "Sensitivity has been defined via tag: "+tagKey),
									)
								}
							}
						}

						if context.IsCostCenterKey(tagKey) {

							resourceContextInsertDoc.ResourceCostCenterTypes.DefinedCostCenter = append(
								resourceContextInsertDoc.ResourceCostCenterTypes.DefinedCostCenter,
								context.GetContextItem(common.ConvertToTitleCase(tagValue), contextutils.TAG_PREFIX+tagKey, "CostCenter has been defined via tag: "+tagKey),
							)
						}

						if code.IsRepoNameKey(tagKey) {
							if _, ok := uniqueRepoNames[tagValue]; !ok {
								uniqueRepoNames[tagValue] = struct{}{}

								resourceContextInsertDoc.CommitInfo.RepoName = tagValue
							}
						}

						if code.IsFileNameKey(tagKey) {
							if _, ok := uniqueFileNames[tagValue]; !ok {
								uniqueFileNames[tagValue] = struct{}{}

								resourceContextInsertDoc.CommitInfo.FileName = tagValue
							}
						}

						if code.IsCommitIDKey(tagKey) {
							if _, ok := uniqueCommitIDs[tagValue]; !ok {
								uniqueCommitIDs[tagValue] = struct{}{}
							}
						}

						if tagKey == "aws:cloudformation:stack-id" {
							// Assign cft stack as parent related resource for resources created by stack
							stackID := strings.ToLower(tagValue)
							stackDocID := common.GenerateCombinedHashID(stackID, common.AWS_CFTSTACK_RESOURCE_TYPE, resourceContextInsertDoc.Account, resourceContext.LastCollectedAt, resourceContextInsertDoc.TenantID)
							resourceDocID := common.GenerateCombinedHashID(resourceContextInsertDoc.ResourceID, resourceContextInsertDoc.ResourceType, resourceContextInsertDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)

							related.AssignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, resourceDocID, resourceContextInsertDoc.ResourceType,
								stackID, stackDocID, common.AWS_CFTSTACK_RESOURCE_TYPE, related.RelatedResourceOpts{Parent: true})
						}

						if tagKey == "aws:ec2launchtemplate:id" {
							launchTemplateID := strings.ToLower(tagValue)
							launchTemplateDocID := common.GenerateCombinedHashID(launchTemplateID, common.AWS_LAUNCHTEMPLATE_RESOURCE_TYPE, resourceContextInsertDoc.Account, resourceContext.LastCollectedAt, resourceContextInsertDoc.TenantID)
							resourceDocID := common.GenerateCombinedHashID(resourceContextInsertDoc.ResourceID, resourceContextInsertDoc.ResourceType, resourceContextInsertDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)

							related.AssignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, resourceDocID, resourceContextInsertDoc.ResourceType,
								launchTemplateID, launchTemplateDocID, common.AWS_LAUNCHTEMPLATE_RESOURCE_TYPE, related.RelatedResourceOpts{Parent: true})
						}

						if tagKey == "Name" {
							resourceNameTagValue = tagValue
						}
					}
				}
			}
		}
	}

	if len(uniqueCommitIDs) > 0 {
		code.GetCodeOwnerFromCommitIDs(uniqueCommitIDs, resourceContext, resourceContextInsertDoc)
	}

	return
}
