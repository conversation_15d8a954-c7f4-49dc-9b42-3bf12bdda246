package activity

import (
	"encoding/json"
	"sort"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

type IDPEventsAgg struct {
	NestedTargets struct {
		FilteredTargets struct {
			ByTarget ByTarget `json:"byTarget"`
		} `json:"filtered_targets"`
	} `json:"nested_targets"`
}

type ByTarget struct {
	Buckets []struct {
		Key    string `json:"key"`
		ToRoot struct {
			ByActor ByActor `json:"byActor"`
		} `json:"to_root"`
	} `json:"buckets"`
}

type ByActor struct {
	Buckets []struct {
		Key        string     `json:"key"`
		MatchedDoc matchedDoc `json:"matchedDoc"`
	} `json:"buckets"`
}

type matchedDoc struct {
	Hits struct {
		Hits []struct {
			Source struct {
				PublishedTime       string `json:"publishedTime"`
				EventDisplayMessage string `json:"eventDisplayMessage"`
				ActorType           string `json:"actorType"`
			} `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

func ProcessIDPActivityBatch(resourceContext *rcontext.ResourceContext, collectedDocIDsForActivityFetch []string, idpType string) (err error) {

	var (
		resourceIDToDocMap = make(map[string]string)
		resourceIDs        []string
	)

	for _, resourceDocID := range collectedDocIDsForActivityFetch {
		if rctxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(resourceDocID); ok {
			resourceID := rctxInsertDoc.ResourceID
			resourceIDToDocMap[resourceID] = resourceDocID

			// Add escape characters if '\' are present
			if strings.Contains(resourceID, `\`) {
				resourceID = common.EscapeString(resourceID)
			}

			resourceIDs = append(resourceIDs, resourceID)
		}
	}

	aggregatedEventsQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"idpType.keyword":"` + idpType + `"}},{"match":{"actorType.keyword":"User"}},{"nested":{"path":"targets","query":{"bool":{"must":[{"terms":{"targets.targetId.keyword":["` + strings.Join(resourceIDs, `","`) + `"]}}]}}}}]}},"from":0,"size":0,"aggs":{"nested_targets":{"nested":{"path":"targets"},"aggs":{"filtered_targets":{"filter":{"terms":{"targets.targetId.keyword":["` + strings.Join(resourceIDs, `","`) + `"]}},"aggs":{"byTarget":{"terms":{"field":"targets.targetId.keyword","size":1000,"order":[{"_count":"desc"},{"_key":"asc"}]},"aggs":{"to_root":{"reverse_nested":{},"aggs":{"byActor":{"terms":{"field":"actorId.keyword","size":100},"aggs":{"matchedDoc":{"top_hits":{"size":1,"sort":{"publishedTime":{"order":"desc"}},"_source":["publishedTime","eventDisplayMessage","actorType"]}}}}}}}}}}}}}}`

	idpEventsAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.IDP_EVENTS_INDEX}, aggregatedEventsQuery)
	if err != nil {
		return
	}

	idpEventsAggBytes, err := json.Marshal(idpEventsAggregation)
	if err != nil {
		logger.Print(logger.ERROR, "Got error marshalling", []string{resourceContext.TenantID}, err)
		return
	}

	var idpEventsAgg IDPEventsAgg
	if err = json.Unmarshal(idpEventsAggBytes, &idpEventsAgg); err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
		return
	}

	filteredTargets := idpEventsAgg.NestedTargets.FilteredTargets

	for _, targetBucket := range filteredTargets.ByTarget.Buckets {

		resourceID := targetBucket.Key

		if resourceDocID, ok := resourceIDToDocMap[resourceID]; ok {

			var activityUsers = make(map[string]common.ResourceContextItem)

			resourceContextInsertDoc, _ := resourceContext.GetResourceContextInsertDoc(resourceDocID)

			for _, actorBucket := range targetBucket.ToRoot.ByActor.Buckets {

				userID := actorBucket.Key

				for _, userInnerHit := range actorBucket.MatchedDoc.Hits.Hits {

					eventTime := userInnerHit.Source.PublishedTime
					eventName := userInnerHit.Source.EventDisplayMessage
					actorType := userInnerHit.Source.ActorType

					if actorType == "User" {
						// IDP Events stores the idp userId and not email
						if userEmail, _ := resourceContext.GetUserIDToEmail(userID); ok {
							activityUsers[eventTime] = context.GetContextItem(userEmail, common.ACTIVITY_OWNER_TYPE, "User has performed activities on the resource", context.WithUserEvaluate(resourceContext, userEmail, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
								Name: eventName,
								Time: eventTime,
							}))
						}
					}
				}
			}

			// Avoid activities done by a person on its own resource (for eg, AD User)
			for k, user := range activityUsers {
				if strings.EqualFold(user.Name, resourceContextInsertDoc.ResourceName) {
					delete(activityUsers, k)
				}
			}

			eventTimes := make([]string, 0, len(activityUsers))

			for k := range activityUsers {
				eventTimes = append(eventTimes, k)
			}

			sort.Sort(sort.Reverse(sort.StringSlice(eventTimes)))

			for _, eventTime := range eventTimes {
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					activityUsers[eventTime],
				)
			}

			resourceContext.SetResourceContextInsertDoc(resourceDocID, resourceContextInsertDoc)
		}
	}

	return
}
