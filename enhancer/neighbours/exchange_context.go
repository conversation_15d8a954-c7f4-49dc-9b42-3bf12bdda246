package neighbours

import (
	"slices"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/internal/property"
	"github.com/precize/logger"
)

type ExchangeOption func(*exchangeOptions)

type exchangeOptions struct {
	confidenceThreshold float32
}

func WithConfidenceThreshold(threshold float32) ExchangeOption {
	return func(o *exchangeOptions) { o.confidenceThreshold = threshold }
}

func ExchangeContext(rctxA, rctxB *common.ResourceContextInsertDoc, resourceContextMap *sync.Map, opts ...ExchangeOption) {
	if rctxA == nil || rctxB == nil {
		return
	}

	options := exchangeOptions{confidenceThreshold: 0.0}
	for _, opt := range opts {
		opt(&options)
	}

	deriveWithDesc := func(items []common.ResourceContextItem, from string, uniqueCtx *[]string) []common.ResourceContextItem {
		var derived []common.ResourceContextItem
		for _, item := range items {
			if strings.EqualFold(item.Type, common.NEIGHBOUR_OWNER_TYPE) {
				continue
			}

			if item.Confidence < options.confidenceThreshold {
				continue
			}

			if contextType, ok := common.ContextItemToContextType[item.Type]; ok {

				newItem := item

				resourceIDSplit := strings.Split(from, "/")
				from = resourceIDSplit[len(resourceIDSplit)-1]

				switch contextType {
				case common.OWNER_CONTEXT_TYPE:
					newItem.Type = common.NEIGHBOUR_OWNER_TYPE
					newItem.Desc = context.GetStaticDescriptionOfUserType(common.NEIGHBOUR_OWNER_TYPE) + ", " + from
				case common.APP_CONTEXT_TYPE:
					newItem.Type = common.NEIGHBOUR_APP_TYPE
					newItem.Desc = context.GetStaticDescriptionOfAppType(common.NEIGHBOUR_APP_TYPE) + ", " + from
				case common.TEAM_CONTEXT_TYPE:
					newItem.Type = common.NEIGHBOUR_TEAM_TYPE
					newItem.Desc = context.GetStaticDescriptionOfTeamType(common.NEIGHBOUR_TEAM_TYPE) + ", " + from
				case common.ENV_CONTEXT_TYPE:
					newItem.Type = common.NEIGHBOUR_ENV_TYPE
					newItem.Desc = context.GetStaticDescriptionOfEnvType(common.NEIGHBOUR_ENV_TYPE) + ", " + from
				case common.DEPLOYMENT_CONTEXT_TYPE:
					newItem.Type = common.NEIGHBOUR_DEPLOYMENT_TYPE
					newItem.Desc = context.GetStaticDescriptionOfDeploymentType(common.NEIGHBOUR_DEPLOYMENT_TYPE) + ", " + from
				}

				newItem.ObjectType = common.IntPtr(common.RCTX_ITEM_OBJECT_TYPE_SECONDARY)

				derived = append(derived, newItem)

				if !slices.Contains(*uniqueCtx, newItem.Name) {
					*uniqueCtx = append(*uniqueCtx, newItem.Name)
				}

			}

		}
		return derived
	}

	mergeContextItems := func(existing, newItems []common.ResourceContextItem) []common.ResourceContextItem {

		existingMap := make(map[string]bool, len(existing))
		for _, e := range existing {
			key := e.Name + "|" + e.Type
			existingMap[key] = true
		}

		for _, n := range newItems {
			key := n.Name + "|" + n.Type
			if !existingMap[key] {
				existing = append(existing, n)
				existingMap[key] = true
			}
		}
		return existing
	}

	dataEntry, ok := resourceContextMap.Load(rctxA.ID)
	if !ok {
		logger.Print(logger.INFO, "No resource data found for docID", rctxA.ID)
		return
	}
	rDataA := dataEntry.(*context.ContextUniqueList)

	dataEntry, ok = resourceContextMap.Load(rctxB.ID)
	if !ok {
		logger.Print(logger.INFO, "No resource data found for docID", rctxB.ID)
		return
	}
	rDataB := dataEntry.(*context.ContextUniqueList)

	// exchange defined and derived owners
	{

		uniqueOwnersA := rDataA.Data[property.ENHANCED_OWNER_PROPERTY_NAME]
		uniqueOwnersB := rDataB.Data[property.ENHANCED_OWNER_PROPERTY_NAME]

		derivedFromA := deriveWithDesc(append(rctxA.DefinedOwners, rctxA.DerivedOwners...), rctxA.ResourceID, &uniqueOwnersB)
		rctxB.DerivedOwners = mergeContextItems(rctxB.DerivedOwners, derivedFromA)

		derivedFromB := deriveWithDesc(append(rctxB.DefinedOwners, rctxB.DerivedOwners...), rctxB.ResourceID, &uniqueOwnersA)
		rctxA.DerivedOwners = mergeContextItems(rctxA.DerivedOwners, derivedFromB)

		rDataA.Data[property.ENHANCED_OWNER_PROPERTY_NAME] = uniqueOwnersA
		rDataB.Data[property.ENHANCED_OWNER_PROPERTY_NAME] = uniqueOwnersB
	}

	// exchange defined and derived teams
	{
		uniqueTeamsA := rDataA.Data[property.ENHANCED_TEAM_PROPERTY_NAME]
		uniqueTeamsB := rDataB.Data[property.ENHANCED_TEAM_PROPERTY_NAME]

		derivedFromA := deriveWithDesc(append(rctxA.DefinedTeam, rctxA.DerivedTeam...), rctxA.ResourceID, &uniqueTeamsB)
		rctxB.DerivedTeam = mergeContextItems(rctxB.DerivedTeam, derivedFromA)

		derivedFromB := deriveWithDesc(append(rctxB.DefinedTeam, rctxB.DerivedTeam...), rctxB.ResourceID, &uniqueTeamsA)
		rctxA.DerivedTeam = mergeContextItems(rctxA.DerivedTeam, derivedFromB)

		rDataA.Data[property.ENHANCED_TEAM_PROPERTY_NAME] = uniqueTeamsA
		rDataB.Data[property.ENHANCED_TEAM_PROPERTY_NAME] = uniqueTeamsB
	}

	// exchange defined and derived env
	uniqueEnvA := rDataA.Data[property.ENVIRONMENT_PROPERTY_NAME]
	if len(rDataA.Data[property.ENVIRONMENT_PROPERTY_NAME]) <= 0 {
		derivedFromB := deriveWithDesc(append(rctxB.DefinedEnv, rctxB.DerivedEnv...), rctxB.ResourceID, &uniqueEnvA)
		rctxA.DerivedEnv = mergeContextItems(rctxA.DerivedEnv, derivedFromB)

		rDataA.Data[property.ENVIRONMENT_PROPERTY_NAME] = uniqueEnvA
	}

	// exchange defined and derived env
	uniqueEnvB := rDataA.Data[property.ENVIRONMENT_PROPERTY_NAME]
	if len(rDataB.Data[property.ENVIRONMENT_PROPERTY_NAME]) <= 0 {
		derivedFromA := deriveWithDesc(append(rctxA.DefinedEnv, rctxA.DerivedEnv...), rctxA.ResourceID, &uniqueEnvB)
		rctxB.DerivedEnv = mergeContextItems(rctxB.DerivedEnv, derivedFromA)

		rDataB.Data[property.ENVIRONMENT_PROPERTY_NAME] = uniqueEnvB
	}

	// exchange derived and defined apps
	{
		uniqueAppsA := rDataA.Data[property.ENHANCED_APP_PROPERTY_NAME]
		uniqueAppsB := rDataB.Data[property.ENHANCED_APP_PROPERTY_NAME]

		derivedFromA := deriveWithDesc(append(rctxA.DefinedApp, rctxA.DerivedApp...), rctxA.ResourceID, &uniqueAppsB)
		rctxB.DerivedApp = mergeContextItems(rctxB.DerivedApp, derivedFromA)

		derivedFromB := deriveWithDesc(append(rctxB.DefinedApp, rctxB.DerivedApp...), rctxB.ResourceID, &uniqueAppsA)
		rctxA.DerivedApp = mergeContextItems(rctxA.DerivedApp, derivedFromB)

		rDataA.Data[property.ENHANCED_APP_PROPERTY_NAME] = uniqueAppsA
		rDataB.Data[property.ENHANCED_APP_PROPERTY_NAME] = uniqueAppsB
	}

	// exchange derived and defined deployment

	uniqueDeploymentA := rDataA.Data[property.DEPLOYMENT_PROPERTY_NAME]
	if len(rDataA.Data[property.DEPLOYMENT_PROPERTY_NAME]) <= 0 {
		derivedFromB := deriveWithDesc(append(rctxB.DefinedDeployment, rctxB.DerivedDeployment...), rctxB.ResourceID, &uniqueDeploymentA)
		rctxA.DerivedDeployment = mergeContextItems(rctxA.DerivedDeployment, derivedFromB)

		rDataA.Data[property.DEPLOYMENT_PROPERTY_NAME] = uniqueDeploymentA
	}

	// exchange derived and defined deployment

	uniqueDeploymentB := rDataA.Data[property.DEPLOYMENT_PROPERTY_NAME]
	if len(rDataB.Data[property.DEPLOYMENT_PROPERTY_NAME]) <= 0 {
		derivedFromA := deriveWithDesc(append(rctxA.DefinedDeployment, rctxA.DerivedDeployment...), rctxA.ResourceID, &uniqueDeploymentB)
		rctxB.DerivedDeployment = mergeContextItems(rctxB.DerivedDeployment, derivedFromA)

		rDataB.Data[property.DEPLOYMENT_PROPERTY_NAME] = uniqueDeploymentB
	}

	//TODO: exchange derived and defined cost center (there is no derived cost center)

	resourceContextMap.Store(rctxA.ID, rDataA)
	resourceContextMap.Store(rctxB.ID, rDataB)
}
