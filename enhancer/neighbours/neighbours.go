package neighbours

import (
	"encoding/json"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/contextor/neighbour"
	"github.com/precize/elastic"
	"github.com/precize/enhancer/rcontext"
)

func DeriveContextFromClosestNeighbours(resourceContext *rcontext.ResourceContext, resourcesHash []string, resourceContextMap *sync.Map) {

	query := `{"query":{"bool":{"must":[{"match":{"tenantId":"` + resourceContext.TenantID + `"}},{"range":{"score":{"gt":0.85}}},{"match":{"deleted":"false"}},{"nested":{"path":"resourceA","query":{"terms":{"resourceA.resourceHash":["` + strings.Join(resourcesHash, `","`) + `"]}}}}]}}}`
	contextNeighbourDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CONTEXT_NEIGHBOURS_INDEX}, query)
	if err != nil {
		return
	}

	for _, contextNeighbourDoc := range contextNeighbourDocs {

		contextNeighbourDocBytes, err := json.Marshal(contextNeighbourDoc)
		if err != nil {
			continue
		}

		var contextNeighbour neighbour.ContextNeighboursDoc
		if err = json.Unmarshal(contextNeighbourDocBytes, &contextNeighbour); err != nil {
			continue
		}

		if contextNeighbour.ResourceB.ResourceHash == contextNeighbour.ResourceA.ResourceHash {
			continue
		}

		rctxDoc := common.GenerateCombinedHashID(contextNeighbour.ResourceA.EntityID, contextNeighbour.ResourceA.EntityType, contextNeighbour.ResourceA.AccountID, resourceContext.LastCollectedAt, resourceContext.TenantID)
		rctxA, ok := resourceContext.GetResourceContextInsertDoc(rctxDoc)
		if !ok {
			continue
		}

		rctxDoc = common.GenerateCombinedHashID(contextNeighbour.ResourceB.EntityID, contextNeighbour.ResourceB.EntityType, contextNeighbour.ResourceB.AccountID, resourceContext.LastCollectedAt, resourceContext.TenantID)
		rctxB, ok := resourceContext.GetResourceContextInsertDoc(rctxDoc)
		if !ok {
			continue
		}

		// exchange context
		ExchangeContext(&rctxA, &rctxB, resourceContextMap, WithConfidenceThreshold(0.85))

		resourceContext.SetResourceContextInsertDoc(rctxA.ID, rctxA)
		resourceContext.SetResourceContextInsertDoc(rctxB.ID, rctxB)
	}
}
