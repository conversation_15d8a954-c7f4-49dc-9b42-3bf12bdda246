package enhance

import (
	"runtime/debug"
	"sync"
	"time"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/enhancer/context"
	"github.com/precize/enhancer/identity"
	emailutils "github.com/precize/enhancer/internal/email"
	"github.com/precize/enhancer/internal/exceptions"
	"github.com/precize/enhancer/internal/property"
	"github.com/precize/enhancer/internal/sddl"
	"github.com/precize/enhancer/postprocess"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/enhancer/resource"
	"github.com/precize/enhancer/source/code"
	"github.com/precize/enhancer/source/customer"
	"github.com/precize/enhancer/source/global"
	"github.com/precize/enhancer/source/issue"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/rds/scans"
)

const (
	MAX_THREADS = 10
)

func StartContextProcessing(tenantID, lastCollectedAt, serviceID string, debugMode bool) {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("enhancer")
		}

		logger.LogEmailProcessor("", true)
	}()

	if len(serviceID) <= 0 {
		serviceID = common.GetServiceID(tenantID, lastCollectedAt)
		if len(serviceID) <= 0 {
			logger.Print(logger.INFO, "Invalid collectedAt at or tenantId", tenantID, lastCollectedAt)
			return
		}
	}

	logger.Print(logger.INFO, "Processing context data for tenant", []string{tenantID}, serviceID, lastCollectedAt)

	tenantData, err := tenant.GetTenantData(tenantID, false)
	if err != nil {
		return
	}

	startContextProcessingForTenant(tenantID, lastCollectedAt, serviceID, tenantData, debugMode)
}

func startContextProcessingForTenant(tenantID, lastCollectedAt, serviceID string, tenantData tenant.TenantData, debugMode bool) {

	var (
		currentTime = time.Now().UTC()
		// All insertions for enhancer should happen here and must check for persistEnhancerData flag
		persistEnhancerData = !debugMode
	)

	previousCollectedAt, err := scans.GetPreviousCollectedAt(tenantID, lastCollectedAt, serviceID)
	if err != nil {
		return
	}

	resourceContext := rcontext.NewResourceContext(tenantID, lastCollectedAt, previousCollectedAt, serviceID, tenantData)

	resourceContext.EmailFormats, _ = emailutils.GetEmailFormatsForTenant(tenantID)
	resourceContext.SDDLStatus, _ = sddl.GetSDDLStatus(tenantID)
	resourceContext.TenantTfApproach, _ = common.FetchTenantTfApproach(tenantID)
	resourceContext.PrimaryDomains = emailutils.GetPrimaryDomains(tenantID)
	global.GetGlobalOrgContext(resourceContext)
	customer.GetCustomerDefinedContexts(resourceContext)
	exceptions.GetExceptions(resourceContext)

	identity.GetUsers(resourceContext)

	if enabled, ok := resourceContext.GetEnabledService("okta"); ok && enabled {
		resource.GetOktaContext(resourceContext)
	}

	if enabled, ok := resourceContext.GetEnabledService("jira"); ok && enabled {
		issue.GetJiraContext(resourceContext)
	}

	switch serviceID {

	case common.AWS_SERVICE_ID:
		resource.GetAWSOrgContext(resourceContext)
		resource.GetAWSUserAndRoleContext(resourceContext)
		resource.GetOrgUnitContext(resourceContext)
		resource.GetAccountContext(resourceContext)
		resource.GetSecurityGroupRules(resourceContext)

	case common.AZURE_SERVICE_ID:
		resource.GetTenantContext(resourceContext)
		resource.GetApplicationContext(resourceContext)
		resource.GetRoleAssignments(resourceContext)
		resource.GetADUserContext(resourceContext)
		resource.GetManagementGroupContext(resourceContext)
		resource.GetSubscriptionContext(resourceContext)
		resource.GetResourceGroupContext(resourceContext)
		resource.GetNetworkSecurityGroupRules(resourceContext)

	case common.GCP_SERVICE_ID:
		resource.GetRoles(resourceContext)
		resource.GetGCPGroupContext(resourceContext)
		resource.GetGCPOrgContext(resourceContext)
		resource.GetServiceAccountKeyContext(resourceContext)
		resource.GetServiceAccountContext(resourceContext)
		resource.GetSAPolicyBindingContext(resourceContext)
		resource.GetFolderContext(resourceContext)
		resource.GetProjectContext(resourceContext)
		resource.GetFirewallRules(resourceContext)

	case common.OPENAI_SERVICE_ID:
		resource.GetOpenAIOrgContext(resourceContext)
		resource.GetOpenAIProjectContext(resourceContext)
	}

	identity.GetIdentityAppsContext(resourceContext)
	resource.GetResourceContext(resourceContext)

	nameList := make(map[string][]string)

	resourceContext.RangeUserResources(func(usrRscKey string, userResource *rcontext.UserContext) bool {
		identity.InitializeHumanOrNonHumanEvaluation(usrRscKey, userResource, resourceContext, nameList)
		return true
	})

	identity.EvaluateNamesForHumanOrNonHuman(nameList, resourceContext)

	var (
		accountSensitivity sync.Map // accountID -> map[string]struct{}
		accountCompliance  sync.Map // accountID -> map[string]struct{}

		resourceContextMap sync.Map // docID -> ContextUniqueList
	)

	logger.Print(logger.INFO, "User list for post processing", []string{tenantID}, lastCollectedAt)

	resourceContext.RangeUserResources(func(usrRscKey string, userResource *rcontext.UserContext) bool {
		logger.Print(logger.INFO, usrRscKey, userResource.Print())
		return true
	})

	logger.Print(logger.INFO, "Starting post process", []string{tenantID}, serviceID, lastCollectedAt)

	var wg sync.WaitGroup
	semStage1 := make(chan struct{}, MAX_THREADS)

	logger.Print(logger.INFO, "Starting post process stage 1", []string{tenantID}, serviceID, lastCollectedAt)

	resourceContext.RangeResourceContextInsertDocs(func(docID string, resourceContextDoc common.ResourceContextInsertDoc) bool {
		semStage1 <- struct{}{}
		wg.Add(1)

		go func(docID string, rContextDoc *common.ResourceContextInsertDoc) {
			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occurred", r, docID)
					email.SendPanicEmail("enhancer")
				}
				wg.Done()
				<-semStage1
			}()

			crDataIface, _ := resourceContextMap.LoadOrStore(docID, &context.ContextUniqueList{
				Data: make(map[string][]string),
			})
			rData := crDataIface.(*context.ContextUniqueList)

			rData.Set(property.OWNER_PROPERTY_NAME, context.PostProcessOwners(rContextDoc, resourceContext))
			rData.Set(property.ENVIRONMENT_PROPERTY_NAME, context.GetUniqueEnvContext(rContextDoc, resourceContext))
			rData.Set(property.APP_PROPERTY_NAME, context.GetUniqueAppContext(rContextDoc))
			rData.Set(property.TEAM_PROPERTY_NAME, context.GetUniqueTeamContext(rContextDoc))
			rData.Set(property.SOFTWARE_PROPERTY_NAME, context.GetUniqueSoftwareContext(rContextDoc))
			rData.Set(property.DEPLOYMENT_PROPERTY_NAME, context.GetUniqueDeploymentContext(rContextDoc))
			rData.Set(property.COMPLIANCE_PROPERTY_NAME, context.GetUniqueComplianceContext(rContextDoc, &accountCompliance))
			rData.Set(property.SENSITIVITY_PROPERTY_NAME, context.GetUniqueSensitivityContext(rContextDoc, &accountSensitivity))
			rData.Set(property.COSTCENTER_PROPERTY_NAME, context.GetUniqueCostCenterContext(rContextDoc))
			rData.Set(property.TTL_PROPERTY_NAME, context.GetUniqueTTLContext(rContextDoc))
			rData.Set(property.USER_AGENT_PROPERTY_NAME, context.GetUniqueUserAgentContext(rContextDoc))

			resourceContextMap.Store(docID, rData)
			code.PostProcessCommitContext(resourceContext, rContextDoc, docID)

			resource.IncrementResourceCounts(resourceContext, rContextDoc)

			owners, _ := rData.Get(property.OWNER_PROPERTY_NAME)
			for _, owner := range owners {
				// Only increment for resources where owner is present
				context.IncrementParentChildOwnerCount(resourceContext, owner, rContextDoc.Account)
			}

			// Increment resource type owner count even if owner is absent
			context.IncrementResourceTypeOwnerCount(resourceContext, *rContextDoc, owners)

			resourceContext.SetResourceContextInsertDoc(docID, *rContextDoc)
		}(docID, &resourceContextDoc)

		return true
	})

	wg.Wait()
	close(semStage1)

	logger.Print(logger.INFO, "Completed post process stage 1", []string{tenantID}, serviceID, lastCollectedAt)

	debug.FreeOSMemory()

	// cooldown
	time.Sleep(5 * time.Second)

	if persistEnhancerData {
		emailutils.UpdateUndeliverableValidEmails(resourceContext)
	}

	logger.Print(logger.INFO, "Starting post process stage 2", []string{tenantID}, serviceID, lastCollectedAt)

	semStage2 := make(chan struct{}, MAX_THREADS)

	resourceContext.RangeResourceContextInsertDocs(func(docID string, resourceContextDoc common.ResourceContextInsertDoc) bool {

		semStage2 <- struct{}{}
		wg.Add(1)
		go func(docID string, resourceContextDoc *common.ResourceContextInsertDoc) {
			defer func() {
				if r := recover(); r != nil {
					logger.Print(logger.ERROR, "Panic occurred", r, docID)
					email.SendPanicEmail("enhancer")
				}
				wg.Done()
				<-semStage2
			}()

			crDataIface, ok := resourceContextMap.Load(docID)
			if !ok {
				logger.Print(logger.INFO, "No resource data found for docID", docID)
				return
			}
			rData := crDataIface.(*context.ContextUniqueList)

			ownerData, _ := rData.Get(property.OWNER_PROPERTY_NAME)
			appData, _ := rData.Get(property.APP_PROPERTY_NAME)
			teamData, _ := rData.Get(property.TEAM_PROPERTY_NAME)
			envData, _ := rData.Get(property.ENVIRONMENT_PROPERTY_NAME)
			softwareData, _ := rData.Get(property.SOFTWARE_PROPERTY_NAME)
			deploymentData, _ := rData.Get(property.DEPLOYMENT_PROPERTY_NAME)
			ttlData, _ := rData.Get(property.TTL_PROPERTY_NAME)
			uaData, _ := rData.Get(property.USER_AGENT_PROPERTY_NAME)
			ccData, _ := rData.Get(property.COSTCENTER_PROPERTY_NAME)

			var (
				enhancedUniqueOwners = append([]string{}, ownerData...)
				enhancedUniqueApps   = append([]string{}, appData...)
				enhancedUniqueTeams  = append([]string{}, teamData...)
			)

			context.PostProcessServiceIdentities(resourceContext, resourceContextDoc, ownerData, &enhancedUniqueOwners, &resourceContextMap)

			postprocess.PostProcessRelatedResources(
				docID, resourceContext, resourceContextDoc,
				&resourceContextMap,
				&enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams,
			)

			postprocess.PostProcessMaxRelatedOwner(docID, resourceContext, resourceContextDoc,
				&resourceContextMap, &enhancedUniqueOwners)

			postprocess.PostProcessSimilarResourceNames(
				docID, resourceContext, resourceContextDoc,
				&resourceContextMap,
				&enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams,
			)

			postprocess.PostProcessSameAppResources(
				docID, resourceContext, resourceContextDoc,
				&resourceContextMap,
				&enhancedUniqueOwners, &enhancedUniqueTeams,
			)

			postprocess.PostProcessSameTagResources(
				docID, resourceContext, resourceContextDoc,
				&resourceContextMap,
				&enhancedUniqueOwners, &enhancedUniqueApps, &enhancedUniqueTeams,
			)

			postprocess.PostProcessResourceTypeOwner(resourceContext, resourceContextDoc, &enhancedUniqueOwners)
			postprocess.PostProcessSmallAccounts(resourceContext, resourceContextDoc, &resourceContextMap, &enhancedUniqueOwners)

			sensitivityData, _ := rData.Get(property.SENSITIVITY_PROPERTY_NAME)
			complianceData, _ := rData.Get(property.COMPLIANCE_PROPERTY_NAME)

			postprocess.PostProcessParentComplianceAndSensitivity(
				docID, resourceContextDoc,
				sensitivityData,
				complianceData,
				&accountSensitivity, &accountCompliance,
			)

			postprocess.PostProcessTeamsOfOwner(resourceContext, resourceContextDoc, &enhancedUniqueOwners, &enhancedUniqueTeams)

			rData.Set(property.ENHANCED_OWNER_PROPERTY_NAME, enhancedUniqueOwners)
			rData.Set(property.ENHANCED_TEAM_PROPERTY_NAME, enhancedUniqueTeams)
			rData.Set(property.ENHANCED_APP_PROPERTY_NAME, enhancedUniqueApps)

			allData := rData.GetAll()
			customer.RemoveCustomerEntityExcludeContextOfResource(resourceContext, resourceContextDoc, allData)

			rData.SetAll(allData)

			resourceContextMap.Store(docID, rData)

			resourceContextDoc.LastCollectedAt = lastCollectedAt
			resourceContextDoc.UpdatedTime = elastic.DateTime(currentTime)
			resourceContextDoc.ID = docID

			uniqueMap := map[string][]string{
				property.OWNER_PROPERTY_NAME:       enhancedUniqueOwners,
				property.ENVIRONMENT_PROPERTY_NAME: envData,
				property.APP_PROPERTY_NAME:         enhancedUniqueApps,
				property.SOFTWARE_PROPERTY_NAME:    softwareData,
				property.TEAM_PROPERTY_NAME:        enhancedUniqueTeams,
				property.DEPLOYMENT_PROPERTY_NAME:  deploymentData,
				property.COMPLIANCE_PROPERTY_NAME:  complianceData,
				property.SENSITIVITY_PROPERTY_NAME: sensitivityData,
				property.COSTCENTER_PROPERTY_NAME:  ccData,
				property.TTL_PROPERTY_NAME:         ttlData,
				property.USER_AGENT_PROPERTY_NAME:  uaData,
			}

			postprocess.EnhanceContextItems(resourceContext, resourceContextDoc, uniqueMap)

			resourceContext.SetResourceContextInsertDoc(docID, *resourceContextDoc)
		}(docID, &resourceContextDoc)

		return true
	})

	wg.Wait()
	close(semStage2)

	logger.Print(logger.INFO, "Completed post process stage 2", []string{tenantID}, serviceID, lastCollectedAt)
	debug.FreeOSMemory()

	// cooldown
	time.Sleep(5 * time.Second)

	logger.Print(logger.INFO, "Starting post process stage 3", []string{tenantID}, serviceID, lastCollectedAt)

	var (
		identitiesMap   sync.Map
		identityCount   int32
		identityMapLock sync.RWMutex
		resourcesHash   = []string{}
		rctxDocIDs      = []string{}
	)

	resourceContext.RangeResourceContextInsertDocs(func(docID string, resourceContextDoc common.ResourceContextInsertDoc) bool {

		rscHash := common.GenerateCombinedHashID(resourceContextDoc.ResourceID, resourceContextDoc.ResourceType, resourceContextDoc.Account, resourceContextDoc.TenantID)
		resourcesHash = append(resourcesHash, rscHash)
		rctxDocIDs = append(rctxDocIDs, resourceContextDoc.ID)
		if len(resourcesHash) >= 5000 {
			postprocess.PostProcessThirdStage(resourceContext, resourcesHash, persistEnhancerData, lastCollectedAt, currentTime, &identitiesMap, &identityCount, &identityMapLock, &resourceContextMap, rctxDocIDs)

			resourcesHash = []string{}
			rctxDocIDs = []string{}
		}
		return true
	})

	if len(resourcesHash) > 0 {
		postprocess.PostProcessThirdStage(resourceContext, resourcesHash, persistEnhancerData, lastCollectedAt, currentTime, &identitiesMap, &identityCount, &identityMapLock, &resourceContextMap, rctxDocIDs)
	}

	debug.FreeOSMemory()

	// cooldown
	time.Sleep(5 * time.Second)

	if persistEnhancerData {

		logger.Print(logger.INFO, "Printing Parent Child", []string{tenantID}, lastCollectedAt)
		resourceContext.RangeChildPrimaryEmail(func(childEmail, primaryEmail string) bool {
			logger.Print(logger.INFO, childEmail, primaryEmail)
			return true
		})

		if int(identityCount) > 0 {
			err := identity.InsertIdentites(&identitiesMap, resourceContext.TenantID)
			if err != nil {
				return
			}
		}

		err := identity.ProcessDeletedResourceContextDocs(elastic.DateTime(currentTime), resourceContext.TenantID, resourceContext.ServiceID)
		if err != nil {
			return
		}

		logger.Print(logger.INFO, "Starting identity primary email updating process")

		err = identity.UpdatePrimaryEmailForNonEnhancerIdentities(resourceContext, elastic.DateTime(currentTime))
		if err != nil {
			return
		}

		logger.Print(logger.INFO, "Completed identity primary email updating process")

		logger.Print(logger.INFO, "Starting identity status updating process", []string{tenantID}, lastCollectedAt)

		err = identity.UpdateIdentityType(resourceContext)
		if err != nil {
			return
		}

		err = identity.UpdateExEmployeeReplacements(resourceContext)
		if err != nil {
			return
		}

		global.ProcessGlobalApps(resourceContext)
		global.AIRejectedGlobalContext(resourceContext)
		global.SetGlobalOrgContext(resourceContext)

		logger.Print(logger.INFO, "Completed identity status updating process", []string{tenantID}, lastCollectedAt)
	}

	logger.Print(logger.INFO, "Completed post process stage 3", lastCollectedAt)

	if config.Environment == config.PROD_ENV {
		logger.Print(logger.INFO, "Sleeping 5 mins for data sync", []string{tenantID}, lastCollectedAt)
		time.Sleep(5 * time.Minute)
	}

	logger.Print(logger.INFO, "Enhancer completed for tenant", []string{tenantID}, lastCollectedAt)
}
