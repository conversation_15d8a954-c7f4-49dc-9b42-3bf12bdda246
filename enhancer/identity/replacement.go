package identity

import (
	"encoding/json"
	"strconv"
	"strings"

	"github.com/precize/elastic"
	"github.com/precize/enhancer/rcontext"
	"github.com/precize/logger"
)

func UpdateExEmployeeReplacements(resourceContext *rcontext.ResourceContext) error {

	var (
		bulkIdentitiesReplacementRequest string
		recordsCount                     int
		identityIDs                      []string
		searchAfter                      any
	)

	resourceContext.RangeExEmployeeReplacements(func(email string, replacements []string) bool {
		identityIDs = append(identityIDs, email)
		return true
	})

	batchSize := 100

	for i := 0; i < len(identityIDs); i += batchSize {

		end := i + batchSize
		if end > len(identityIDs) {
			end = len(identityIDs)
		}

		batch := identityIDs[i:end]

		idsJson, err := json.Marshal(batch)
		if err != nil {
			logger.Print(logger.ERROR, "Failed to marshal", err)
			continue
		}

		exEmployeeIdentitiesQuery := `{
			"_source": ["identityId","type"],
			"query": {
				"bool": {
					"must": [
						{"term": {"tenantId.keyword": "` + resourceContext.TenantID + `"}},
						{"terms": {"identityId.keyword": ` + string(idsJson) + `}},
						{"term": {"deleted": "false"}}
					]
				}
			}
		}`

		for {

			identityDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.IDENTITIES_INDEX}, exEmployeeIdentitiesQuery, searchAfter)
			if err != nil {
				break
			}

			if len(identityDocs) > 0 {
				searchAfter = sortResponse
			} else {
				break
			}

			for identityDocID, identityDoc := range identityDocs {

				if identityID, ok := identityDoc["identityId"].(string); ok && len(identityID) > 0 {

					if replacements, ok := resourceContext.GetExEmployeeReplacements(identityID); ok {

						replacementUpdateMetadata := `{"update": {"_id": "` + identityDocID + `"}}`
						replacementUpdateDoc := `{"doc":{"replacement": ["` + strings.Join(replacements, `","`) + `"]}}`

						bulkIdentitiesReplacementRequest = bulkIdentitiesReplacementRequest + replacementUpdateMetadata + "\n" + replacementUpdateDoc + "\n"
						recordsCount++

						if recordsCount > MAX_RECORDS {
							if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesReplacementRequest); err != nil {
								break
							}

							logger.Print(logger.INFO, "Identities replacement bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})

							recordsCount = 0
							bulkIdentitiesReplacementRequest = ""
						}
					}
				}
			}
		}
	}

	if recordsCount > 0 {
		if err := elastic.BulkDocumentsAPI(resourceContext.TenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesReplacementRequest); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Identities replacement bulk API Successful for "+strconv.Itoa(recordsCount)+" records", []string{resourceContext.TenantID})

	}

	return nil
}
