AWSTemplateFormatVersion: 2010-09-09

Metadata:
  AWS::CloudFormation::Interface:

# =================================================================
# Parameter Groups
# =================================================================

    ParameterGroups:
      - Label:
          default: Generic
        Parameters:
          - Environment
          - BuildServerIP

      - Label:
          default: VPC and Subnets
        Parameters:
          - VpcCidr
          - PublicSubnetCidr
          - PublicSubnetZone
          - PrivateSubnet1Cidr
          - PrivateSubnet1Zone
          - PrivateSubnet2Cidr
          - PrivateSubnet2Zone
          - PrivateSubnet3Cidr
          - PrivateSubnet3Zone

      - Label:
          default: Key Pair
        Parameters:
          - KeyName

      - Label:
          default: RDS Database Parameters
        Parameters:
          - DBName
          - DBMasterUsername
          - DBInstanceClass
          - AllocatedStorage
          - MaxAllocatedStorage
          - Engine

      - Label:
          default: Bastion Host Parameters
        Parameters:
          - BastionHostAMI
          - BastionHostInstanceType

      - Label:
          default: Dashboard Instance Parameters
        Parameters:
          - DashboardAMI
          - DashboardInstanceType
          - DashboardExternalVolumeSize

      - Label:
          default: Activity Instance EC2 Parameters
        Parameters:
          - ActivityAMI
          - ActivityInstanceType
          - ActivityExternalVolumeSize

      - Label:
          default: Provider Instance EC2 Parameters
        Parameters:
          - ProviderAMI
          - ProviderInstanceType
          - ProviderExternalVolumeSize

      - Label:
          default: Scan Instance EC2 Parameters
        Parameters:
          - ScanAMI
          - ScanInstanceType
          - ScanExternalVolumeSize

      - Label:
          default: Elastic Cluster Parameters
        Parameters:
          - ElasticAMI
          - ElasticMasterNodeInstanceType
          - ElasticMasterNodeExternalVolumeSize
          - ElasticDataNodeInstanceType
          - ElasticDataNodeExternalVolumeSize

      - Label:
          default: Redis Instance EC2 Parameters
        Parameters:
          - RedisAMI
          - RedisInstanceType
          - RedisExternalVolumeSize

      - Label:
          default: Vector Instance EC2 Parameters
        Parameters:
          - VectorDBAMI
          - VectorDBInstanceType
          - VectorDBExternalVolumeSize

# =================================================================
# Parameter Labels
# =================================================================

    ParameterLabels:
      Environment:
        default: Environment
      BuildServerIP:
        default: Build Server IP
      VpcCidr:
        default: VPC CIDR Block
      PublicSubnetCidr:
        default: Public Subnet CIDR Block
      PublicSubnetZone:
        default: Public Subnet Zone
      PrivateSubnet1Cidr:
        default: Private Subnet 1 CIDR Block
      PrivateSubnet1Zone:
        default: Private Subnet 1 Zone
      PrivateSubnet2Cidr:
        default: Private Subnet 2 CIDR Block
      PrivateSubnet2Zone:
        default: Private Subnet 2 Zone
      PrivateSubnet3Cidr:
        default: Private Subnet 3 CIDR Block
      PrivateSubnet3Zone:
        default: Private Subnet 3 Zone
      KeyName:
        default: EC2 Key Pair Name
      DBName:
        default: DB Name
      DBMasterUsername:
        default: DB Master Username
      DBInstanceClass:
        default: DB Instance Type
      AllocatedStorage:
        default: Allocated DB Storage (GB)
      MaxAllocatedStorage:
        default: Max Allocated DB Storage (GB)
      Engine:
        default: Engine
      BastionHostAMI:
        default: Instance AMI
      BastionHostInstanceType:
        default: Instance Type
      DashboardAMI:
        default: Instance AMI
      DashboardInstanceType:
        default: Instance Type
      DashboardExternalVolumeSize:
        default: External Volume Size (GB)
      ActivityAMI:
        default: Instance AMI
      ActivityInstanceType:
        default: Instance Type
      ActivityExternalVolumeSize:
        default: External Volume Size (GB)
      ProviderAMI:
        default: Instance AMI
      ProviderInstanceType:
        default: Instance Type
      ProviderExternalVolumeSize:
        default: External Volume Size (GB)
      ScanAMI:
        default: Instance AMI
      ScanInstanceType:
        default: Instance Type
      ScanExternalVolumeSize:
        default: External Volume Size (GB)
      ElasticAMI:
        default: Instances AMI
      ElasticMasterNodeInstanceType:
        default: Master Node Instance Type
      ElasticMasterNodeExternalVolumeSize:
        default: Master Node External Volume Size (GB)
      ElasticDataNodeInstanceType:
        default: Data Node Instance Type
      ElasticDataNodeExternalVolumeSize:
        default: Data Node External Volume Size (GB)
      RedisAMI:
        default: Instance AMI
      RedisInstanceType:
        default: Instance Type
      RedisExternalVolumeSize:
        default: External Volume Size (GB)
      VectorDBAMI:
        default: Instance AMI
      VectorDBInstanceType:
        default: Instance Type
      VectorDBExternalVolumeSize:
        default: External Volume Size (GB)

# =================================================================
# Parameters
# =================================================================

Parameters:
  Environment:
    Type: String
    Default: qa
    Description: The Environment of deployment
    AllowedValues:
      - qa
      - preprod
      - prod

  BuildServerIP:
    Type: String
    Default: ************
    Description: IP of the build server containing artifacts

  VpcCidr:
    Type: String
    Default: 10.0.0.0/16
    Description: The CIDR block for the VPC

  PublicSubnetCidr:
    Type: String
    Default: ********/24
    Description: CIDR block for the public subnet

  PublicSubnetZone:
    Type: AWS::EC2::AvailabilityZone::Name
    Description: Zone for public subnet

  PrivateSubnet1Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for the private subnet 1

  PrivateSubnet1Zone:
    Type: AWS::EC2::AvailabilityZone::Name
    Description: Zone for private subnet 1

  PrivateSubnet2Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for the private subnet 2

  PrivateSubnet2Zone:
    Type: AWS::EC2::AvailabilityZone::Name
    Description: Zone for private subnet 2

  PrivateSubnet3Cidr:
    Type: String
    Default: ********/24
    Description: CIDR block for the private subnet 3

  PrivateSubnet3Zone:
    Type: AWS::EC2::AvailabilityZone::Name
    Description: Zone for private subnet 3. Should be different AZ from private subnet 2

  KeyName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: Key pair to access all instances

  DBName:
    Type: String
    Default: precizedb
    Description: The name of the initial database.

  DBMasterUsername:
    Type: String
    Default: dbadmin
    Description: Master username for the database. Password will be auto-generated.
    MinLength: '4'
    MaxLength: '16'
    AllowedPattern: '[a-zA-Z][a-zA-Z0-9]*'
    ConstraintDescription: Must begin with a letter and contain only alphanumeric characters.

  DBInstanceClass:
    Type: String
    Default: db.m5.large
    Description: The compute and memory capacity of the DB instance.
    AllowedValues:
      - db.t3.micro
      - db.t3.small
      - db.t3.medium
      - db.m5.large
      - db.m5.xlarge
    ConstraintDescription: Must be a valid DB instance type.

  AllocatedStorage:
    Type: Number
    Default: 100
    Description: The size of the database storage in GiB.
    MinValue: 20
    MaxValue: 1000
    ConstraintDescription: Must be between 20 and 1000 GiB.

  MaxAllocatedStorage:
    Type: Number
    Default: 400
    Description: The max size of the database storage in GiB.
    MinValue: 20
    MaxValue: 1000
    ConstraintDescription: Must be between 20 and 1000 GiB.

  Engine:
    Type: String
    Default: postgres
    Description: The database engine to be used for this instance.
    AllowedValues:
      - mysql
      - postgres
      - mariadb
    ConstraintDescription: Must be a supported DB engine.

  BastionHostAMI:
    Type: String
    Description: AMI for Bastion Host

  BastionHostInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Bastion Host

  DashboardAMI:
    Type: String
    Description: AMI for Dashboard Instance

  DashboardInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Dashboard Instance

  DashboardExternalVolumeSize:
    Type: Number
    Default: 20
    Description: Size of external volume in GB for Dashboard Instance

  ActivityAMI:
    Type: String
    Description: AMI for Activity Instance

  ActivityInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Activity Instance

  ActivityExternalVolumeSize:
    Type: Number
    Default: 20
    Description: Size of external volume in GB for Activity Instance

  ProviderAMI:
    Type: String
    Description: AMI for Provider Instance

  ProviderInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Provider Instance

  ProviderExternalVolumeSize:
    Type: Number
    Default: 20
    Description: Size of external volume in GB for Provider Instance

  ScanAMI:
    Type: String
    Description: AMI for Scan Instance

  ScanInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Scan Instance

  ScanExternalVolumeSize:
    Type: Number
    Default: 20
    Description: Size of external volume in GB for Scan Instance

  ElasticAMI:
    Type: String
    Description: AMI for Elastic Instances

  ElasticMasterNodeInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Elastic Master Node

  ElasticMasterNodeExternalVolumeSize:
    Type: Number
    Default: 20
    Description: Size of external volume in GB for Elastic Master Node

  ElasticDataNodeInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Elastic Data Node

  ElasticDataNodeExternalVolumeSize:
    Type: Number
    Default: 20
    Description: Size of external volume in GB for Elastic Data Node

  RedisAMI:
    Type: String
    Description: AMI for Redis Instance

  RedisInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Redis Instance

  RedisExternalVolumeSize:
    Type: Number
    Default: 20
    Description: Size of external volume in GB for Redis Instance

  VectorDBAMI:
    Type: String
    Description: AMI for Vector Instance

  VectorDBInstanceType:
    Type: String
    Default: t2.micro
    Description: Instance type for Vector Instance

  VectorDBExternalVolumeSize:
    Type: Number
    Default: 20
    Description: Size of external volume in GB for Vector Instance

# =================================================================
# Mappings
# =================================================================

Mappings:
  EngineToPortMap:
    mysql:
      Port: '3306'
    postgres:
      Port: '5432'
    mariadb:
      Port: '3306'

# =================================================================
# Resources - VPC and Subnets
# =================================================================

Resources:
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: !Ref VpcCidr
      EnableDnsSupport: true
      EnableDnsHostnames: true
      Tags:
        - Key: Name
          Value: Precize VPC
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  PublicSubnet:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      CidrBlock: !Ref PublicSubnetCidr
      AvailabilityZone: !Ref PublicSubnetZone
      MapPublicIpOnLaunch: true
      Tags:
        - Key: Name
          Value: Precize Public Subnet
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  InternetGateway:
    Type: AWS::EC2::InternetGateway

  AttachGateway:
    Type: AWS::EC2::VPCGatewayAttachment
    Properties:
      VpcId: !Ref VPC
      InternetGatewayId: !Ref InternetGateway

  PublicRouteTable:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC

  PublicInternetRoute:
    Type: AWS::EC2::Route
    Properties:
      RouteTableId: !Ref PublicRouteTable
      DestinationCidrBlock: 0.0.0.0/0
      GatewayId: !Ref InternetGateway

  PublicSubnetRouteTableAssociation:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      SubnetId: !Ref PublicSubnet
      RouteTableId: !Ref PublicRouteTable

  PrivateSubnet1:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      CidrBlock: !Ref PrivateSubnet1Cidr
      AvailabilityZone: !Ref PrivateSubnet1Zone
      Tags:
        - Key: Name
          Value: Precize Private Subnet 1
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  PrivateRouteTable1:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC

  PrivateSubnetRouteTableAssociation1:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      SubnetId: !Ref PrivateSubnet1
      RouteTableId: !Ref PrivateRouteTable1

  PrivateSubnet2:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      CidrBlock: !Ref PrivateSubnet2Cidr
      AvailabilityZone: !Ref PrivateSubnet2Zone
      Tags:
        - Key: Name
          Value: Precize Private Subnet 2
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  PrivateRouteTable2:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC

  PrivateSubnetRouteTableAssociation2:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      SubnetId: !Ref PrivateSubnet2
      RouteTableId: !Ref PrivateRouteTable2

  PrivateSubnet3:
    Type: AWS::EC2::Subnet
    Properties:
      VpcId: !Ref VPC
      CidrBlock: !Ref PrivateSubnet3Cidr
      AvailabilityZone: !Ref PrivateSubnet3Zone
      Tags:
        - Key: Name
          Value: Precize Private Subnet 3
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  PrivateRouteTable3:
    Type: AWS::EC2::RouteTable
    Properties:
      VpcId: !Ref VPC

  PrivateSubnetRouteTableAssociation3:
    Type: AWS::EC2::SubnetRouteTableAssociation
    Properties:
      SubnetId: !Ref PrivateSubnet3
      RouteTableId: !Ref PrivateRouteTable3

  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: !Sub "DB Subnet Group for ${Environment}"
      SubnetIds:
        - !Ref PrivateSubnet2
        - !Ref PrivateSubnet3
      Tags:
        - Key: Name
          Value: !Sub "Precize-${Environment}-dbsubnetgroup"

# =================================================================
# Resources - Security Groups
# =================================================================

  RDSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: !Sub "Security group for RDS instance in ${Environment}"
      VpcId: !Ref VPC
      SecurityGroupIngress:
        # Allow traffic only from the internal application security group
        - IpProtocol: tcp
          FromPort: !FindInMap [EngineToPortMap, !Ref Engine, Port]
          ToPort: !FindInMap [EngineToPortMap, !Ref Engine, Port]
          CidrIp: !Ref VpcCidr
      Tags:
        - Key: Name
          Value: Precize RDS SG
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  BastionSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Allow SSH access from anywhere
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Name
          Value: Precize Bastion SG
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  DashboardSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Allow HTTP/HTTPS access from anywhere
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Name
          Value: Precize Dashboard SG
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ElasticSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Allow Elastic port access from within the VPC
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 9200
          ToPort: 9200
          CidrIp: !Ref VpcCidr
        - IpProtocol: tcp
          FromPort: 9300
          ToPort: 9300
          CidrIp: !Ref VpcCidr
      Tags:
        - Key: Name
          Value: Precize Elastic Master Node SG
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  RedisSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Allow Redis port access from within the VPC
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 6379
          ToPort: 6379
          CidrIp: !Ref VpcCidr
      Tags:
        - Key: Name
          Value: Precize Redis SG
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  VectorSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Allow Vector DB port access from within the VPC
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 18001
          ToPort: 18001
          CidrIp: !Ref VpcCidr
        - IpProtocol: tcp
          FromPort: 50051
          ToPort: 50051
          CidrIp: !Ref VpcCidr
      Tags:
        - Key: Name
          Value: Precize Vector DB SG
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  InternalServiceSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Allow HTTP and other internal access from within the VPC
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: !Ref VpcCidr
        - IpProtocol: tcp
          FromPort: 19090
          ToPort: 19090
          CidrIp: !Ref VpcCidr
        - IpProtocol: tcp
          FromPort: 18090
          ToPort: 18090
          CidrIp: !Ref VpcCidr
        - IpProtocol: tcp
          FromPort: 17070
          ToPort: 17070
          CidrIp: !Ref VpcCidr
      Tags:
        - Key: Name
          Value: Precize Servers Internal SG
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  InternalSSHSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Allow SSH access from within the VPC
      VpcId: !Ref VPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: !Ref VpcCidr
      Tags:
        - Key: Name
          Value: Precize Servers Internal SSH SG
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

# =================================================================
# Resources - Secrets
# =================================================================

  DBSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: !Sub "Credentials for the ${DBName} RDS database"
      GenerateSecretString:
        SecretStringTemplate: !Sub '{"username": "${DBMasterUsername}"}'
        GenerateStringKey: "password"
        PasswordLength: 16
        ExcludeCharacters: '"@/\'
      Tags:
        - Key: Name
          Value: !Sub "Precize-${Environment}-db-secret"

# =================================================================
# Resources - RDS Instance
# =================================================================

  RDSInstance:
    Type: AWS::RDS::DBInstance
    Properties:
      DBName: !Ref DBName
      DBInstanceIdentifier: !Sub "precize-${Environment}-rds-instance"
      MasterUsername: !Ref DBMasterUsername
      MasterUserPassword: !Sub "{{resolve:secretsmanager:${DBSecret}:SecretString:password}}"
      DBInstanceClass: !Ref DBInstanceClass
      Engine: !Ref Engine
      AllocatedStorage: !Ref AllocatedStorage
      MaxAllocatedStorage: !Ref MaxAllocatedStorage
      StorageEncrypted: true
      StorageType: gp2
      PubliclyAccessible: false
      VPCSecurityGroups:
        - !Ref RDSSecurityGroup
      DBSubnetGroupName: !Ref DBSubnetGroup
      MultiAZ: false
      BackupRetentionPeriod: 7
      DeletionProtection: true
      Tags:
        - Key: Name
          Value: !Sub "Precize-${Environment}-db-instance"
        - Key: Owners
          Value: <EMAIL>
        - Key: Environment
          Value: !Ref Environment

# =================================================================
# Resources - Elastic IP's
# =================================================================

  DashboardEIP:
    Type: AWS::EC2::EIP
    Properties:
      Domain: vpc
      Tags:
        - Key: Name
          Value: Precize Dashboard Elastic IP
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  DashboardEIPAssociation:
    Type: AWS::EC2::EIPAssociation
    Properties:
      InstanceId: !Ref DashboardInstance
      EIP: !Ref DashboardEIP

  ScanEIP:
    Type: AWS::EC2::EIP
    Properties:
      Domain: vpc
      Tags:
        - Key: Name
          Value: Precize Scan Elastic IP
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ScanEIPAssociation:
    Type: AWS::EC2::EIPAssociation
    Properties:
      InstanceId: !Ref ScanInstance
      EIP: !Ref ScanEIP

# =================================================================
# Resources - EC2 Instances
# =================================================================

  BastionHost:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref BastionHostInstanceType
      ImageId: !Ref BastionHostAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref BastionSecurityGroup
      SubnetId: !Ref PublicSubnet
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              sudo chown -R ubuntu:ubuntu /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Bastion Host
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  DashboardInstance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref DashboardInstanceType
      ImageId: !Ref DashboardAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref DashboardSecurityGroup
        - !Ref InternalSSHSecurityGroup
      SubnetId: !Ref PublicSubnet
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/precize-vader /app/precize-server
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp -o StrictHostKeyChecking=no ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_dashboard.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services platform,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Dashboard Instance
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ActivityInstance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref ActivityInstanceType
      ImageId: !Ref ActivityAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref InternalSSHSecurityGroup
      SubnetId: !Ref PublicSubnet
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/precize-vader /app/precize-server
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp -o StrictHostKeyChecking=no ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_activity.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services platform,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Activity Instance
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ProviderInstance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref ProviderInstanceType
      ImageId: !Ref ProviderAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref InternalSSHSecurityGroup
        - !Ref InternalServiceSecurityGroup
      SubnetId: !Ref PublicSubnet
      IamInstanceProfile: !Ref ECRAccessInstanceProfile
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          sudo apt update
          sudo apt install -y unzip curl
          sudo curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          sudo unzip awscliv2.zip
          sudo ./aws/install
          sudo rm -rf awscliv2.zip
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/precize-vader /app/precize-provider /app/precize-pserver /app/precize-analyzer /app/fetch8k
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp -o StrictHostKeyChecking=no ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_provider.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services provider,pserver,analyzer,fetch8k,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Provider Instance
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ScanInstance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref ScanInstanceType
      ImageId: !Ref ScanAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref InternalSSHSecurityGroup
        - !Ref InternalServiceSecurityGroup
      SubnetId: !Ref PublicSubnet
      IamInstanceProfile: !Ref ECRAccessInstanceProfile
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/precize-vader /app/precize-server /app/precize-enhancer /app/precize-prioritiser /app/precize-externaldc /app/precize-contextor
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp -o StrictHostKeyChecking=no ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_scan.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services platform,enhancer,prioritiser,externaldc,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Scan Instance
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ElasticMasterNodeInstance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref ElasticMasterNodeInstanceType
      ImageId: !Ref ElasticAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref ElasticSecurityGroup
        - !Ref InternalSSHSecurityGroup
      SubnetId: !Ref PrivateSubnet1
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/elastic
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_elastic.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services elastic,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Elastic Master Instance
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ElasticDataNode1Instance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref ElasticDataNodeInstanceType
      ImageId: !Ref ElasticAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref ElasticSecurityGroup
        - !Ref InternalSSHSecurityGroup
      SubnetId: !Ref PrivateSubnet1
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/elastic
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_elastic.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services elastic,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Elastic Data Instance 1
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ElasticDataNode2Instance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref ElasticDataNodeInstanceType
      ImageId: !Ref ElasticAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref ElasticSecurityGroup
        - !Ref InternalSSHSecurityGroup
      SubnetId: !Ref PrivateSubnet2
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/elastic
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_elastic.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services elastic,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Elastic Data Instance 2
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  RedisInstance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref RedisInstanceType
      ImageId: !Ref RedisAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref RedisSecurityGroup
        - !Ref InternalSSHSecurityGroup
      SubnetId: !Ref PrivateSubnet2
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/redis
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_redis.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services redis,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Redis Instance
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  VectorDBInstance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref VectorDBInstanceType
      ImageId: !Ref VectorDBAMI
      KeyName: !Ref KeyName
      SecurityGroupIds:
        - !Ref VectorSecurityGroup
        - !Ref InternalSSHSecurityGroup
      SubnetId: !Ref PublicSubnet
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          sleep 10
          DEVICE_NAME=$(lsblk -o NAME,SIZE,MOUNTPOINT | grep -v "NAME" | tail -n 1 | awk '{print "/dev/"$1}')
          if [ -b "$DEVICE_NAME" ]; then
              sudo mkfs -t ext4 $DEVICE_NAME
              sudo mkdir -p /app
              sudo mount $DEVICE_NAME /app
              echo "$DEVICE_NAME /app ext4 defaults,nofail 0 2" | sudo tee -a /etc/fstab

              sudo mkdir /app/precize-vader /app/weaviate_data
              sudo chown -R ubuntu:ubuntu /app
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-d /app/precize-vader/vader-d
              sudo -u ubuntu scp ubuntu@${BuildServerIP}:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader/configs/${Environment}/vader_vector.json /app/precize-vader/vader.json
              sudo -u ubuntu /app/precize-vader/vader-d --bootup --config /app/precize-vader/vader.json --services vector,vader-m --user CFT &
          else
              echo "$DEVICE_NAME not found. Please check if the volume is attached properly."
          fi
      Tags:
        - Key: Name
          Value: Precize Vector DB Instance
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

# =================================================================
# Resources - EBS Backup
# =================================================================

  EBSBackupLifecyclePolicy:
    Type: AWS::DLM::LifecyclePolicy
    Properties:
      Description: "Daily Backup Policy for Elastic EBS Volumes"
      State: ENABLED
      ExecutionRoleArn: !GetAtt EBSBackupRole.Arn
      PolicyDetails:
        ResourceTypes:
          - VOLUME
        TargetTags:
          - Key: ElasticBackup
            Value: Daily
        Schedules:
          - Name: "DailyBackup"
            CreateRule:
              Interval: 24
              IntervalUnit: HOURS
              Times:
                - "07:30"
            RetainRule:
              Count: 7

  EBSBackupRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: dlm.amazonaws.com
            Action: "sts:AssumeRole"
      Policies:
        - PolicyName: DLMPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ec2:CreateSnapshot
                  - ec2:CreateSnapshots
                  - ec2:DeleteSnapshot
                  - ec2:DescribeInstances
                  - ec2:DescribeVolumes
                  - ec2:DescribeSnapshots
                  - ec2:EnableFastSnapshotRestores
                  - ec2:DescribeFastSnapshotRestores
                  - ec2:DisableFastSnapshotRestores
                  - ec2:CopySnapshot
                  - ec2:ModifySnapshotAttribute
                  - ec2:DescribeSnapshotAttribute
                  - ec2:DescribeSnapshotTierStatus
                  - ec2:ModifySnapshotTier
                  - ec2:DescribeAvailabilityZones
                Resource: "*"
              - Effect: Allow
                Action:
                  - ec2:CreateTags
                Resource: arn:aws:ec2:*::snapshot/*
              - Effect: Allow
                Action:
                  - events:PutRule
                  - events:DeleteRule
                  - events:DescribeRule
                  - events:EnableRule
                  - events:DisableRule
                  - events:ListTargetsByRule
                  - events:PutTargets
                  - events:RemoveTargets
                Resource: arn:aws:events:*:*:rule/AwsDataLifecycleRule.managed-cwe.*

# =================================================================
# Resources - EBS Volumes
# =================================================================

  DashboardVolume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt DashboardInstance.AvailabilityZone
      Size: !Ref DashboardExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Dashboard Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  DashboardVolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref DashboardInstance
      VolumeId: !Ref DashboardVolume
      Device: /dev/xvdf

  ActivityVolume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt ActivityInstance.AvailabilityZone
      Size: !Ref ActivityExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Activity Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ActivityVolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref ActivityInstance
      VolumeId: !Ref ActivityVolume
      Device: /dev/xvdf

  ProviderVolume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt ProviderInstance.AvailabilityZone
      Size: !Ref ProviderExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Provider Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ProviderVolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref ProviderInstance
      VolumeId: !Ref ProviderVolume
      Device: /dev/xvdf

  ScanVolume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt ScanInstance.AvailabilityZone
      Size: !Ref ScanExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Scan Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  ScanVolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref ScanInstance
      VolumeId: !Ref ScanVolume
      Device: /dev/xvdf

  ElasticMasterNodeVolume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt ElasticMasterNodeInstance.AvailabilityZone
      Size: !Ref ElasticMasterNodeExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Elastic Master Node Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment
        - Key: ElasticBackup
          Value: Daily

  ElasticMasterNodeVolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref ElasticMasterNodeInstance
      VolumeId: !Ref ElasticMasterNodeVolume
      Device: /dev/xvdf

  ElasticDataNode1Volume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt ElasticDataNode1Instance.AvailabilityZone
      Size: !Ref ElasticDataNodeExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Elastic Data Node 1 Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment
        - Key: ElasticBackup
          Value: Daily

  ElasticDataNode1VolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref ElasticDataNode1Instance
      VolumeId: !Ref ElasticDataNode1Volume
      Device: /dev/xvdf

  ElasticDataNode2Volume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt ElasticDataNode2Instance.AvailabilityZone
      Size: !Ref ElasticDataNodeExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Elastic Data Node 2 Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment
        - Key: ElasticBackup
          Value: Daily

  ElasticDataNode2VolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref ElasticDataNode2Instance
      VolumeId: !Ref ElasticDataNode2Volume
      Device: /dev/xvdf

  RedisVolume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt RedisInstance.AvailabilityZone
      Size: !Ref RedisExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Redis Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  RedisVolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref RedisInstance
      VolumeId: !Ref RedisVolume
      Device: /dev/xvdf

  VectorDBVolume:
    Type: AWS::EC2::Volume
    Properties:
      AvailabilityZone: !GetAtt VectorDBInstance.AvailabilityZone
      Size: !Ref VectorDBExternalVolumeSize
      VolumeType: gp2
      Tags:
        - Key: Name
          Value: Precize Vector DB Volume
        - Key: Owners
          Value: <EMAIL>, <EMAIL>
        - Key: Environment
          Value: !Ref Environment

  VectorDBVolumeAttachment:
    Type: AWS::EC2::VolumeAttachment
    Properties:
      InstanceId: !Ref VectorDBInstance
      VolumeId: !Ref VectorDBVolume
      Device: /dev/xvdf

# =================================================================
# Resources - Roles
# =================================================================

  ECRAccessRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ECRAccessRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      Path: /
      Policies:
        - PolicyName: ECRPullAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ecr:GetAuthorizationToken
                  - ecr:BatchCheckLayerAvailability
                  - ecr:GetDownloadUrlForLayer
                  - ecr:BatchGetImage
                Resource: "*"

  ECRAccessInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      InstanceProfileName: ECRAccessInstanceProfile
      Roles:
        - !Ref ECRAccessRole

# =================================================================
# Outputs
# =================================================================

Outputs:
  DBEndpointAddress:
    Description: The connection endpoint for the database.
    Value: !GetAtt RDSInstance.Endpoint.Address
    Export:
      Name: !Sub "${AWS::StackName}-DBEndpointAddress"

  DBEndpointPort:
    Description: The port for the database connection.
    Value: !GetAtt RDSInstance.Endpoint.Port
    Export:
      Name: !Sub "${AWS::StackName}-DBEndpointPort"

  DBSecretARN:
    Description: The ARN of the secret in AWS Secrets Manager containing the database credentials.
    Value: !Ref DBSecret
    Export:
      Name: !Sub "${AWS::StackName}-DBSecretARN"
