package main

import (
	"strconv"
	"strings"
	"time"

	"github.com/precize/common/azureopenai"
	"github.com/precize/contextor/engine"
	"github.com/precize/elastic"
	"github.com/precize/email"
	"github.com/precize/logger"
)

const (
	INSERTION_MAX_RECORDS    = 500
	EVALUATION_MAX_RECORDS   = 50
	DB_INSERTION_MAX_RECORDS = 3000
)

func DeriveContext(aiClient *azureopenai.Client, tenantID, lastCollectedAt, serviceID, enabledPhases string) error {

	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("contextor")
		}

		logger.LogEmailProcessor("", true)
	}()

	logger.Print(logger.INFO, "Processing context data for tenant", []string{tenantID}, serviceID, lastCollectedAt)

	ce := engine.ContextEngine{
		AI:            aiClient,
		TenantID:      tenantID,
		CollectedAt:   lastCollectedAt,
		ServiceID:     serviceID,
		EnabledPhases: enabledPhases,
	}

	err := ce.InitializeGlobalSystemPrompt()
	if err != nil {
		return err
	}

	Insertion(&ce, tenantID)
	Evaluation(&ce, tenantID)

	logger.Print(logger.INFO, "Processing complete for tenant", []string{tenantID}, serviceID, lastCollectedAt)

	return nil
}

func Insertion(ce *engine.ContextEngine, tenantID string) {

	if len(ce.EnabledPhases) > 0 && !strings.Contains(ce.EnabledPhases, "insertion") {
		logger.Print(logger.INFO, "Insertion phase is disabled for tenant", []string{tenantID})
		return
	}

	var searchAfter any

	logger.Print(logger.INFO, "Insertion started for tenant", []string{tenantID})

	for {

		resourceDocs, sortResponse, err := ce.CollectResourceData(searchAfter)
		if err != nil {
			break
		}

		if len(resourceDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		docs := []map[string]any{}

		logger.Print(logger.INFO, "Fetched data from CR", []string{tenantID}, len(resourceDocs))

		for _, resourceDoc := range resourceDocs {

			docs = append(docs, resourceDoc)

			if len(docs) >= INSERTION_MAX_RECORDS {
				ce.VectorizeResourceMetadata(docs)
				ce.ExtractAndVectorizeBreadcrumbs(docs)
				docs = []map[string]any{}
			}
		}

		if len(docs) > 0 {
			ce.VectorizeResourceMetadata(docs)
			ce.ExtractAndVectorizeBreadcrumbs(docs)
		}

		logger.Print(logger.INFO, "Processed data for tenant", []string{tenantID}, len(resourceDocs))
	}

	logger.Print(logger.INFO, "Insertion completed for tenant", []string{tenantID})
}

func Evaluation(ce *engine.ContextEngine, tenantID string) {

	if len(ce.EnabledPhases) > 0 && !strings.Contains(ce.EnabledPhases, "evaluation") {
		logger.Print(logger.INFO, "Evaluation phase is disabled for tenant", []string{tenantID})
		return
	}

	logger.Print(logger.INFO, "Evaluation started for tenant", []string{tenantID})

	var (
		bulkContextNeighbourRequest strings.Builder
		contextNeighboursCount      int
		searchAfter                 any
	)

	for {

		resourceDocs, sortResponse, err := ce.CollectResourceData(searchAfter)
		if err != nil {
			break
		}

		if len(resourceDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		docs := []map[string]any{}

		logger.Print(logger.INFO, "Fetched data from CR", []string{tenantID}, len(resourceDocs))

		for _, resourceDoc := range resourceDocs {

			docs = append(docs, resourceDoc)

			if len(docs) >= EVALUATION_MAX_RECORDS {
				ce.GetSimilarResourceUsingMetadata(docs, &bulkContextNeighbourRequest, &contextNeighboursCount)
				ce.GetSimilarResourcesUsingBreadcrumbs(docs, &bulkContextNeighbourRequest, &contextNeighboursCount)
				docs = []map[string]any{}
			}

			if contextNeighboursCount >= DB_INSERTION_MAX_RECORDS {
				if err := elastic.BulkDocumentsAPI(tenantID, elastic.CONTEXT_NEIGHBOURS_INDEX, bulkContextNeighbourRequest.String()); err != nil {
					logger.Print(logger.INFO, "Query failed", []string{tenantID}, contextNeighboursCount, bulkContextNeighbourRequest.String())
					return
				}

				logger.Print(logger.INFO, "Context neighbour bulk API Successful for "+strconv.Itoa(contextNeighboursCount)+" records", []string{tenantID})

				contextNeighboursCount = 0
				bulkContextNeighbourRequest.Reset()
				time.Sleep(150 * time.Millisecond)
			}

		}

		if len(docs) > 0 {
			ce.GetSimilarResourceUsingMetadata(docs, &bulkContextNeighbourRequest, &contextNeighboursCount)
			ce.GetSimilarResourcesUsingBreadcrumbs(docs, &bulkContextNeighbourRequest, &contextNeighboursCount)
		}

		if contextNeighboursCount > 0 {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CONTEXT_NEIGHBOURS_INDEX, bulkContextNeighbourRequest.String()); err != nil {
				logger.Print(logger.ERROR, "Query failed", []string{tenantID}, contextNeighboursCount, bulkContextNeighbourRequest.String())
				return
			}

			logger.Print(logger.INFO, "Context neighbour bulk API Successful for "+strconv.Itoa(contextNeighboursCount)+" records", []string{tenantID})

			contextNeighboursCount = 0
			bulkContextNeighbourRequest.Reset()
			time.Sleep(150 * time.Millisecond)
		}

		logger.Print(logger.INFO, "Processed data for tenant", []string{tenantID}, len(resourceDocs))
	}

	logger.Print(logger.INFO, "Evaluation completed for tenant", []string{tenantID})
}
