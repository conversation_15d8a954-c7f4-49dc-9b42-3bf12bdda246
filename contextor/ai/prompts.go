package ai

const (
	INTRO_PROMPT = `You are a cloud context derivation engine.
We already collect and process all raw cloud data programmatically, extracting any well-known or directly derivable context (such as resource IDs, resource types, quotas, or provider metadata).
Your responsibility is to go beyond that: analyze resources and identities at a human level to extract contextual signals that are not usually derivable via code.
Focus on uncovering ownership, applications, teams, deployments and other customer-relevant context that requires reasoning, interpretation, or implicit inference.
Your goal is to produce context breadcrumbs that help humans link resources together and understand their purpose, while avoiding low-value or purely technical noise.`

	CUSTOMER_PROMPT = `Return ONLY JSON with this structure:
{
  "company_name": string,
  "domains": [string],          // primary industries/domains the company works in
  "applications": [string],     // key products/services/applications
  "teams": [string],        		// notable customers or target segments or teams
  "technologies": [string],     // technologies or stacks the company is known for
  "business_model": string,     // short description of how the company operates
  "notes": string               // any additional context that could help understand their cloud usage
}

Rules:
- You are retrieving contextual background about the given company to support cloud context derivation.
- Be concise but specific.
- Use public knowledge about the company.
- Do not fabricate details; if something is unknown, leave the field empty.
- Ensure all strings are human-readable.

Company to analyze:
%s`

	EXTRACTION_PROMPT = `
		Analyze this cloud resource metadata and extract ALL meaningful insights you can discover.

		**Resource Metadata:**
			- Resource Name: {resource_name}
			- Tags: {tags_json}
			- Description: "{description}"

		**Discovery Guidelines:**
			- Be comprehensive but precise - extract insights that are actually valuable
			- Look for ownership (identity owning the resource), purpose, environment, security, compliance, cost, operations
			- Infer meaning from context when clear
			- Standardize values (e.g., "Terraform" instead of "tf")
			- No duplicates - keep output lean
			- Create meaningful "type" categories that describe the insight

		**Strict Exclusion:**
			- Strictly DO NOT include the resource name and region as a breadcrumb
			- Focus only on insights extracted from tags, description, and name analysis
			- If no clear insights found beyond resource name, return empty array []

		**Output Requirements:**
			- Output MUST follow this exact JSON structure:
			{
			"resourceId": {
				"breadcrumbs": [
				{"value": "string", "type": "string", "confidence": 0.9}
				]
			}
			}

		**JSON Requirements:**
			- Use double quotes for all strings and keys
			- Confidence as number (0.6-1.0), not string
			- Empty array [] if no breadcrumbs (never null)
			- No trailing commas, properly close all braces

		**Confidence Scoring:**
			- 1.0 = Explicitly stated in metadata
			- 0.7-0.9 = Strong pattern match or clear inference
			- 0.5-0.7 = Reasonable inference from context
			- Omit any breadcrumbs with confidence < 0.5

		**Example:**
			For the input:
				- Resource Name: test-bucket-aiml-logs
				- Tags: [{"owner": "john from provider team", "deployment": "tf"}]
				- Description: "this resource was used for dev purposes only is us-east-1 under the guidance of emile doe"

		Expected output:
			{
				"test-bucket-aiml-logs": {
					"breadcrumbs": [
					{"value": "john", "type": "owner", "confidence": 1.0},
					{"value": "emile doe", "type": "owner", "confidence": 0.8},
					{"value": "provider team", "type": "team", "confidence": 0.9},
					{"value": "Terraform", "type": "deployment_tool", "confidence": 0.9},
					{"value": "development", "type": "environment", "confidence": 1.0},
					{"value": "storing AI/ML logs", "type": "purpose", "confidence": 0.8}
					]
				}
			}
	`

	VALIDATION_PROMPT = `Return ONLY JSON with this structure:
{
  "contextual_matches": [
    {"resourceId": string, "reason": string, "score": number}
  ]
}
Rules:
- A candidate resource is considered matching ONLY if multiple strong breadcrumbs align in the SAME CONTEXT (e.g. both as application, both as owner). Strong does not mean they need to be exactly equal in value.
- DO NOT consider values that appear under different context types as a match (e.g. "Alexa" as an application vs "Alexa" as an owner is NOT a match).
- Guard against spurious hits.
- Use the resourceId provided for each candidate resource.
- score must be in [0,1], representing how strongly aligned the contexts are.
- Exclude all non-matching and low score (< 3) resources from the output (only return strong matches).

Primary resource breadcrumb record:
%s

Candidate resources breadcrumb records (map keyed by resourceId with breadcrumb record as value):
%s`
)
