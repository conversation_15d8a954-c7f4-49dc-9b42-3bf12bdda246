package metadata

import (
	"github.com/precize/common"
	cloudutils "github.com/precize/common/cloud"
	resourceutils "github.com/precize/common/resource"
)

func FetchResourceNameFromCRDoc(crDoc resourceutils.CloudResource) string {

	resourceName := ""
	var resourceNameTagValue string
	for _, tag := range crDoc.Tags {
		if tag.Key == "Name" {
			resourceNameTagValue = tag.Value
			break
		}
	}

	switch crDoc.ServiceID {
	case common.AWS_SERVICE_ID_INT:
		resourceName = cloudutils.GetNameForAWSResource(crDoc.EntityID, crDoc.EntityType, resourceNameTagValue, crDoc.EntityJson)
	case common.AZURE_SERVICE_ID_INT:
		resourceName = cloudutils.GetNameForAzureResource(crDoc.EntityID, crDoc.EntityType, crDoc.EntityJson)
	case common.GCP_SERVICE_ID_INT:
		resourceName = cloudutils.GetNameForGCPResource(crDoc.EntityID, crDoc.EntityType, crDoc.EntityJson)
	case common.OPENAI_SERVICE_ID_INT:
		resourceName = cloudutils.GetNameForOpenAIResource(crDoc.EntityID, crDoc.EntityJson)
	default:
		if name := cloudutils.GetResourceNameFromEntityJSON(crDoc.EntityJson); len(name) > 0 {
			resourceName = name
		}
	}

	if len(resourceName) <= 0 && len(resourceNameTagValue) > 0 {
		resourceName = resourceNameTagValue
	}

	return resourceName
}
