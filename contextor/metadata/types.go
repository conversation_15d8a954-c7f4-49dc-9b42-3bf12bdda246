package metadata

type ResourceMetadata struct {
	ID                 string `json:"id"`
	EntityID           string `json:"entityId"`
	EntityType         string `json:"entityType"`
	EntityName         string `json:"entityName"`
	TenantID           string `json:"tenantId"`
	ServiceID          int    `json:"serviceId"`
	Description        string `json:"description"`
	InsertTime         string `json:"insertTime"`
	AccountID          string `json:"accountId"`
	VectorPropertyHash string `json:"vectorPropertyHash"`
}

type ResourceMetadataSearch struct {
	ID                 string         `json:"id"`
	EntityID           string         `json:"entityId"`
	EntityType         string         `json:"entityType"`
	EntityName         string         `json:"entityName"`
	TenantID           string         `json:"tenantId"`
	ServiceID          int            `json:"serviceId"`
	Description        string         `json:"description"`
	AccountID          string         `json:"accountId"`
	AdditionalInfo     AdditionalInfo `json:"_additional"`
	VectorPropertyHash string         `json:"vectorPropertyHash"`
}

type AdditionalInfo struct {
	Score        string  `json:"score"`
	ExplainScore string  `json:"explainScore"`
	Distance     float64 `json:"distance"`
	ID           string  `json:"id"`
}

type MetadataSearchResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Data map[string]struct {
			MetadataResp []ResourceMetadataSearch `json:"ResourceMetadata"`
		} `json:"data"`
	} `json:"data"`
}

type MetadataSimilarSearchResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Data struct {
			Get struct {
				ResourceMetadata []ResourceMetadataSearch `json:"ResourceMetadata"`
			} `json:"Get"`
		} `json:"data"`
	} `json:"data"`
}

type MetadataNestedFilter struct {
	Operator  string                 `json:"operator,omitempty"`
	Path      []string               `json:"path,omitempty"`
	ValueText string                 `json:"valueText,omitempty"`
	ValueInt  int                    `json:"valueInt,omitempty"`
	Operands  []MetadataNestedFilter `json:"operands,omitempty"`
}
