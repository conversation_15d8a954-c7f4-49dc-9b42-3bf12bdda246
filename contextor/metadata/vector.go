package metadata

import (
	"encoding/json"
	"fmt"

	"github.com/precize/common/weaviate"
	"github.com/precize/logger"
)

const (
	RESOURCE_METADATA_CLASS = "ResourceMetadata"
)

func InsertResourceMetadata(records []ResourceMetadata) error {
	return weaviate.InsertWithID(RESOURCE_METADATA_CLASS, records)
}

func SearchResourceMetadata(filters string, fields []string, limit int) ([]byte, error) {
	return weaviate.SearchByQuery(RESOURCE_METADATA_CLASS, filters, fields, limit)
}

func FetchSimilarResourcesUsingMetadataBulk(similaritySearchReq []weaviate.SearchSimilarRequest, tenantID, serviceID string) (map[string][]ResourceMetadataSearch, error) {

	resp, err := weaviate.SearchSimilarBulk(similaritySearchReq)
	if err != nil {
		return nil, err
	}

	var metaDataSearchResp MetadataSearchResponse
	if err := json.Unmarshal(resp, &metaDataSearchResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal Metadata search response", err)
		return nil, err
	}

	if !metaDataSearchResp.Success {
		logger.Print(logger.INFO, "Similar Resources Not Successful")
		return nil, fmt.Errorf("similar resources not successful")
	}

	result := make(map[string][]ResourceMetadataSearch)
	for key, v := range metaDataSearchResp.Data.Data {
		result[key] = v.MetadataResp
	}

	return result, nil
}

func FetchSimilarResourceUsingMetadata(searchReq weaviate.SearchSimilarRequest, tenantID, serviceID string) ([]ResourceMetadataSearch, error) {

	resp, err := weaviate.SearchSimilar(searchReq.ClassName, searchReq.Query, searchReq.SearchType, searchReq.FusionType, searchReq.Filters, searchReq.Fields, *searchReq.Regulator, *searchReq.MaxVectorDistance, searchReq.Limit)
	if err != nil {
		return nil, err
	}

	var metaDataSearchResp MetadataSimilarSearchResponse
	if err := json.Unmarshal(resp, &metaDataSearchResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal Metadata search response", err)
		return nil, err
	}

	if !metaDataSearchResp.Success {
		logger.Print(logger.INFO, "Similar Resources Not Successful")
		return nil, fmt.Errorf("similar resources not successful")
	}

	return metaDataSearchResp.Data.Data.Get.ResourceMetadata, nil
}
