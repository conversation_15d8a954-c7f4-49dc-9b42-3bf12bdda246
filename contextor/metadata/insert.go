package metadata

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/common/basic/uuid"
	resourceutils "github.com/precize/common/resource"
	stringutils "github.com/precize/common/string_utils"
	"github.com/precize/contextor/utils"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func VectorizeResourceMetadata(resourceDocs []map[string]any, tenantID string) error {

	logger.Print(logger.INFO, "VectorizeResourceMetadata started for tenant", []string{tenantID}, len(resourceDocs))

	var (
		records []ResourceMetadata
	)

	for _, resourceDoc := range resourceDocs {

		rscDocBytes, err := json.Marshal(resourceDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
			continue
		}

		var crDoc resourceutils.CloudResource
		if err = json.Unmarshal(rscDocBytes, &crDoc); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
			continue
		}

		resourceName := crDoc.ResourceName

		if len(resourceName) <= 0 {
			resourceName = FetchResourceNameFromCRDoc(crDoc)
		}

		var tagParts []string
		for _, t := range crDoc.Tags {
			tagParts = append(tagParts, fmt.Sprintf("%s:%s", t.Key, t.Value))
		}
		tagString := strings.Join(tagParts, ", ")
		sortedTagString := stringutils.SortString(tagString)

		// vectorPropertyHash is a unique hash generated from entityID, entityType, accountID, tenantID,
		// and the sorted tags. It helps identify existing vectorized records and prevents
		// unnecessary re-vectoring of the same entity data.

		id := uuid.GenerateUUIDFromString(crDoc.EntityID + crDoc.EntityType + crDoc.AccountID + crDoc.TenantID)
		vectorPropertyHash := common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.AccountID, crDoc.TenantID, resourceName, sortedTagString)
		description := fmt.Sprintf("name: %s | tags: %s", resourceName, tagString)

		vectorInsertDoc := ResourceMetadata{
			ID:                 id,
			EntityID:           crDoc.EntityID,
			EntityName:         resourceName,
			EntityType:         crDoc.EntityType,
			TenantID:           tenantID,
			ServiceID:          crDoc.ServiceID,
			Description:        description,
			InsertTime:         elastic.DateTime(time.Now().UTC()),
			AccountID:          crDoc.AccountID,
			VectorPropertyHash: vectorPropertyHash,
		}

		records = append(records, vectorInsertDoc)
	}

	if len(records) == 0 {
		return nil
	}

	batchSize := 50
	totalInserted := 0

	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}

		batch := records[i:end]

		filteredBatch := utils.FilterInsertRecords(tenantID, batch, SearchResourceMetadata, func(r ResourceMetadata) string { return r.VectorPropertyHash })

		if len(filteredBatch) == 0 {
			continue
		}

		if err := InsertResourceMetadata(filteredBatch); err != nil {
			logger.Print(logger.ERROR, "Error inserting resource metadata batch", []string{tenantID}, err)
			return err
		}

		totalInserted += len(filteredBatch)
	}

	logger.Print(logger.INFO, "VectorizeResourceMetadata completed for tenant", []string{tenantID}, totalInserted)

	return nil
}
