# ───────────────────────────────
# Stage 1: Build
# ───────────────────────────────

FROM golang:1.23-alpine AS builder

# Install git (required by go mod for private repos)
RUN apk add --no-cache git

WORKDIR /app

# Copy go modules first (for caching)
COPY go.mod go.sum ./
RUN go mod download

# Copy the rest of the source code
COPY . .

RUN cd contextor && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o /app/precize-contextor

# ───────────────────────────────
# Stage 2: Runtime Image
# ───────────────────────────────

FROM alpine:latest

WORKDIR /app

COPY --from=builder /app/precize-contextor /usr/bin/precize-contextor

RUN chmod +x /usr/bin/precize-contextor

ENTRYPOINT ["/usr/bin/precize-contextor"]