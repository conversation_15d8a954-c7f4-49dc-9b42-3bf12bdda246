{"className": "Breadcrumbs", "description": "Class contains vectorized breadcrumbs generated from resource name, tags and description", "properties": [{"name": "breadcrumbs", "dataType": ["text"]}], "vectorConfig": {"metadata_vector": {"vectorizer": {"text2vec-openai": {"sourceProperties": ["breadcrumbs"], "dimensions": 1536, "resourceName": "precize", "deploymentId": "textEmbeddingSmallDeployment", "baseURL": "https://precize.openai.azure.com"}}, "vectorIndexType": "hnsw", "vectorIndexConfig": {"distance": "cosine"}}}}