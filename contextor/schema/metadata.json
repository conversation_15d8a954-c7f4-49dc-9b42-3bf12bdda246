{"className": "ResourceMetadata", "description": "Class contains vectorized resource name and tags", "properties": [{"name": "description", "dataType": ["text"]}], "vectorConfig": {"metadata_vector": {"vectorizer": {"text2vec-openai": {"sourceProperties": ["description"], "dimensions": 1536, "resourceName": "precize", "deploymentId": "textEmbeddingSmallDeployment", "baseURL": "https://precize.openai.azure.com"}}, "vectorIndexType": "hnsw", "vectorIndexConfig": {"distance": "cosine"}}}}