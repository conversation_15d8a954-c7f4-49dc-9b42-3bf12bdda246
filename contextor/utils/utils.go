package utils

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"
)

type QuerySearchFunc func(filters string, fields []string, limit int) ([]byte, error)

func FilterInsertRecords[T any](tenantID string, records []T, searchFn QuerySearchFunc, hashExtractor func(T) string) []T {
	if len(records) == 0 {
		return records
	}

	idSet := make(map[string]struct{}, len(records))
	for _, record := range records {
		id := hashExtractor(record)
		if id == "" {
			continue
		}
		idSet[id] = struct{}{}
	}

	if len(idSet) == 0 {
		return records
	}

	ids := make([]string, 0, len(idSet))
	for id := range idSet {
		ids = append(ids, id)
	}
	sort.Strings(ids)

	filterStr := fmt.Sprintf(`{ path: ["vectorPropertyHash"], operator: ContainsAny, valueText: [%s] }`,
		`"`+strings.Join(ids, `", "`)+`"`)

	resp, err := searchFn(filterStr, []string{`_additional { id } vectorPropertyHash`}, len(ids))
	if err != nil {
		return records
	}

	existingIDs, err := extractIDsFromSearchResponse(resp)
	if err != nil {
		return records
	}

	filtered := make([]T, 0, len(records))
	for _, record := range records {
		id := hashExtractor(record)
		if id == "" {
			filtered = append(filtered, record)
			continue
		}
		if _, exists := existingIDs[id]; exists {
			continue
		}
		filtered = append(filtered, record)
	}

	return filtered
}

func extractIDsFromSearchResponse(resp []byte) (map[string]struct{}, error) {
	var queryResp struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
		Data    struct {
			Data struct {
				Get map[string][]struct {
					Additional struct {
						ID string `json:"id"`
					} `json:"_additional"`
					VectorPropertyHash string `json:"vectorPropertyHash"`
				} `json:"Get"`
			} `json:"data"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp, &queryResp); err != nil {
		return nil, err
	}

	if !queryResp.Success {
		return nil, fmt.Errorf("search unsuccessful: %s", queryResp.Message)
	}

	ids := make(map[string]struct{})
	for _, records := range queryResp.Data.Data.Get {
		for _, record := range records {
			id := record.VectorPropertyHash
			if id == "" {
				continue
			}
			ids[id] = struct{}{}
		}
	}

	return ids, nil
}
