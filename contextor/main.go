package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/precize/common"
	"github.com/precize/common/azureopenai"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/rds"
	"github.com/precize/transport"
)

// To run contextor, use the following command
// sudo docker run -d   --name precize-contextor   -v /app/precize-server/application.yml:/app/application.yml   -v /app/precize-contextor:/app/logs   124474729313.dkr.ecr.ap-south-1.amazonaws.com/precize-contextor:qa   --config=/app/application.yml   --tenant=0R8Da4gBoELr5xpoQ6Y3   --collected=1762330204074   --serviceId=1000   --enabledPhases="evaluation"

func main() {

	var (
		appConfigPath   = flag.String("config", "application.yml", "Path to application.yml")
		tenantID        = flag.String("tenant", "", "TenantId to run enhancer for")
		lastCollectedAt = flag.String("collected", "", "Last scan time for tenantId")
		serviceID       = flag.String("serviceId", "", "ServiceId of the service")
		enabledPhases   = flag.String("enabledPhases", "", "Comma separated list of enabled phases")
	)

	flag.Parse()

	logger.InitializeLogs("contextor", false)

	config.Localhost = "host.docker.internal"

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err := elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	var aiClient = azureopenai.NewClient()

	if err = rds.ConnectToPostgres(); err != nil {
		return
	}

	gracefullyShutDown()

	DeriveContext(aiClient, *tenantID, *lastCollectedAt, *serviceID, *enabledPhases)

	os.Exit(0)
}

func gracefullyShutDown() {

	sigs := make(chan os.Signal, 1)

	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	go func() {

		sig := <-sigs
		logger.Print(logger.INFO, "Signal received", sig)
		os.Exit(1)
	}()
}
