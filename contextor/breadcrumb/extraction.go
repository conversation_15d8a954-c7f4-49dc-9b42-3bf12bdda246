package breadcrumb

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common/basic/uuid"
	stringutils "github.com/precize/common/string_utils"

	"github.com/precize/common"
	"github.com/precize/common/azureopenai"
	cloudutils "github.com/precize/common/cloud"
	resourceutils "github.com/precize/common/resource"
	"github.com/precize/contextor/ai"
	"github.com/precize/contextor/utils"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type Breadcrumb struct {
	Value      string  `json:"value"`
	Type       string  `json:"type,omitempty"`
	Confidence float64 `json:"confidence,omitempty"`
}

type extractionRequest struct {
	ResourceName string                `json:"resourceName"`
	Description  string                `json:"description"`
	Tags         []resourceutils.CRTag `json:"tags"`

	crDoc resourceutils.CloudResource `json:"-"`
}

func ExtractAndVectorizeBreadcrumbs(resourceDocs []map[string]any, aiClient *azureopenai.Client, systemPrompt, tenantID string) error {

	logger.Print(logger.INFO, "ExtractAndVectorizeBreadcrumbs started for tenant", []string{tenantID}, len(resourceDocs))
	var (
		bcRecords                                    []BreadcrumbRecord
		extractionBulkRequest                        = make(map[string]extractionRequest)
		extractionRecordCount, textLookupRecordCount int
		bulkTextLookupRequest                        strings.Builder
		textLookUpDocIDs                             []string
	)

	type lookupItem struct {
		entityID string
		docID    string
		req      extractionRequest
		cr       resourceutils.CloudResource
	}
	var items []lookupItem

	for _, resourceDoc := range resourceDocs {

		rscDocBytes, err := json.Marshal(resourceDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
			continue
		}

		var crDoc resourceutils.CloudResource
		if err = json.Unmarshal(rscDocBytes, &crDoc); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
			continue
		}

		extractionReq, err := createExtractionRequest(crDoc)
		if err != nil {
			continue
		}

		// skip resource with empty names and tags
		if extractionReq.ResourceName == "" && len(extractionReq.Tags) == 0 {
			continue
		}

		extractionReqJSON, err := json.Marshal(*extractionReq)
		if err != nil {
			logger.Print(logger.ERROR, "Marshal error", err)
			continue
		}

		textHash := common.HashSha256(extractionReqJSON)
		textLookupDocID := common.GenerateCombinedHashID(textHash)

		items = append(items, lookupItem{
			entityID: crDoc.EntityID,
			docID:    textLookupDocID,
			req:      *extractionReq,
			cr:       crDoc,
		})

		textLookUpDocIDs = append(textLookUpDocIDs, textLookupDocID)
	}

	if len(items) > 0 {

		txtLkUpQuery := `{"query":{"bool":{"must":[{"terms":{"_id":["` + strings.Join(textLookUpDocIDs, `","`) + `"]}}]}},"size": ` + strconv.Itoa(len(textLookUpDocIDs)) + `}`
		txtLkUpDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.TEXT_LOOKUP_INDEX}, txtLkUpQuery)
		if err != nil {
			logger.Print(logger.ERROR, "Query failed", []string{tenantID}, err, txtLkUpQuery)
			return err
		}

		for _, it := range items {
			if doc, ok := txtLkUpDocs[it.docID]; ok && len(doc) > 0 {
				if dataString, ok := doc["data"].(string); ok {
					var breadcrumbs []Breadcrumb
					if err := json.Unmarshal([]byte(dataString), &breadcrumbs); err != nil {
						logger.Print(logger.ERROR, "Unmarshal error", err)
						continue
					}

					if err = stageBreadcrumbRecord(it.cr, breadcrumbs, &bcRecords); err != nil {
						continue
					}
				}
			} else {

				extractionRecordCount++
				extractionBulkRequest[it.entityID] = it.req

				if extractionRecordCount >= 20 {
					extractBreadcrumbs(extractionBulkRequest, aiClient, systemPrompt, tenantID, &bulkTextLookupRequest, &textLookupRecordCount, &bcRecords)

					extractionRecordCount = 0
					extractionBulkRequest = make(map[string]extractionRequest)
				}
			}
		}
	}

	if extractionRecordCount > 0 {
		extractBreadcrumbs(extractionBulkRequest, aiClient, systemPrompt, tenantID, &bulkTextLookupRequest, &textLookupRecordCount, &bcRecords)
	}

	if textLookupRecordCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.TEXT_LOOKUP_INDEX, bulkTextLookupRequest.String()); err != nil {
			logger.Print(logger.ERROR, "Query failed", []string{tenantID}, textLookupRecordCount, bulkTextLookupRequest.String())
			return err
		}

		logger.Print(logger.INFO, "Text lookup bulk API Successful for "+strconv.Itoa(textLookupRecordCount)+" records", []string{tenantID})

		bulkTextLookupRequest.Reset()
	}

	if len(bcRecords) > 0 {
		batchSize := 50
		totalInserted := 0

		for i := 0; i < len(bcRecords); i += batchSize {
			end := i + batchSize
			if end > len(bcRecords) {
				end = len(bcRecords)
			}

			batch := bcRecords[i:end]

			filteredBatch := utils.FilterInsertRecords(tenantID, batch, SearchBreadcrumbs, func(r BreadcrumbRecord) string { return r.VectorPropertyHash })

			if len(filteredBatch) == 0 {
				continue
			}

			if err := InsertBreadcrumbs(filteredBatch); err != nil {
				logger.Print(logger.ERROR, "Error inserting breadcrumbs batch", []string{tenantID}, err)
				return err
			}

			totalInserted += len(filteredBatch)
		}

		logger.Print(logger.INFO, "Inserted breadcrumbs for tenant", []string{tenantID}, totalInserted)
	}

	logger.Print(logger.INFO, "ExtractAndVectorizeBreadcrumbs completed for tenant", []string{tenantID})
	time.Sleep(1 * time.Second)
	return nil
}

func createExtractionRequest(crDoc resourceutils.CloudResource) (*extractionRequest, error) {

	var (
		extractionReq        = &extractionRequest{}
		entityJSONMap        = make(map[string]any)
		resourceNameTagValue string
	)

	err := json.Unmarshal([]byte(crDoc.EntityJson), &entityJSONMap)
	if err != nil {
		logger.Print(logger.ERROR, "Unmarshal error", err)
		return nil, err
	}

	extractionReq.Description, _ = entityJSONMap["description"].(string)

	for _, tag := range crDoc.Tags {
		if tag.Key == "Name" {
			resourceNameTagValue = tag.Value
		}

		extractionReq.Tags = append(extractionReq.Tags, resourceutils.CRTag{Key: tag.Key, Value: tag.Value})
	}

	switch crDoc.ServiceID {
	case common.AWS_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForAWSResource(crDoc.EntityID, crDoc.EntityType, resourceNameTagValue, crDoc.EntityJson)
	case common.AZURE_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForAzureResource(crDoc.EntityID, crDoc.EntityType, crDoc.EntityJson)
	case common.GCP_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForGCPResource(crDoc.EntityID, crDoc.EntityType, crDoc.EntityJson)
	case common.OPENAI_SERVICE_ID_INT:
		extractionReq.ResourceName = cloudutils.GetNameForOpenAIResource(crDoc.EntityID, crDoc.EntityJson)
	default:
		if name := cloudutils.GetResourceNameFromEntityJSON(crDoc.EntityJson); len(name) > 0 {
			extractionReq.ResourceName = name
		}
	}

	extractionReq.crDoc = crDoc

	return extractionReq, err
}

func extractBreadcrumbs(extractionBulkRequest map[string]extractionRequest, aiClient *azureopenai.Client, systemPrompt, tenantID string, bulkTextLookupRequest *strings.Builder, textLookupRecordCount *int, bcRecords *[]BreadcrumbRecord) error {

	extractionBulkRequestJSON, err := json.Marshal(extractionBulkRequest)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal error", err)
		return err
	}

	session := aiClient.NewSession()
	session.AddSystemMessage(`You are an AI analyst specialized in cloud infrastructure. Your task is to discover meaningful insights from cloud resource metadata and output them as structured JSON with confidence scores. Be thorough in identifying valuable information for resource management, cost optimization, security, and operations.`)
	session.AddSystemMessage("Few insights about the company which the resources belongs to : " + systemPrompt)
	session.AddUserMessage(ai.EXTRACTION_PROMPT)
	session.AddUserMessage(string(extractionBulkRequestJSON))

	out, err := session.Send()
	if err != nil {
		return err
	}

	azureopenai.RemoveJSONQuoteFromResponse(&out)

	// EntityId -> Breadcrumbs object
	var bcResps = make(map[string]struct {
		Breadcrumbs []Breadcrumb `json:"breadcrumbs"`
	})

	if err := json.Unmarshal([]byte(out), &bcResps); err != nil {

		// openAI gives an extra '}' at the end, remove it and try again
		out = out[:len(out)-1]

		if err := json.Unmarshal([]byte(out), &bcResps); err != nil {
			logger.Print(logger.ERROR, "Unmarshal error", err, out)
			return err
		}
	}

	for entityID, bcResp := range bcResps {

		// if resource name is part of breadcrumb value then remove from bcResp.Breadcrumbs
		filtered := make([]Breadcrumb, 0, len(bcResp.Breadcrumbs))
		for _, breadcrumb := range bcResp.Breadcrumbs {
			if extractionReq, ok := extractionBulkRequest[entityID]; ok {
				if strings.Contains(breadcrumb.Value, extractionReq.ResourceName) {
					continue
				}
			}
			filtered = append(filtered, breadcrumb)
		}
		bcResp.Breadcrumbs = filtered

		err = stageBreadcrumbRecord(extractionBulkRequest[entityID].crDoc, bcResp.Breadcrumbs, bcRecords)
		if err != nil {
			continue
		}

		breadcrumbsJSON, err := json.Marshal(bcResp.Breadcrumbs)
		if err != nil {
			logger.Print(logger.ERROR, "Marshal error", err)
			continue
		}

		extractionReqJSON, err := json.Marshal(extractionBulkRequest[entityID])
		if err != nil {
			logger.Print(logger.ERROR, "Marshal error", err)
			continue
		}

		textHash := common.HashSha256(extractionReqJSON)
		textLookupDocID := common.GenerateCombinedHashID(textHash)

		textLookupDoc := common.TextLookupInsertDoc{
			Text:       textHash,
			Data:       string(breadcrumbsJSON),
			TenantID:   tenantID,
			InsertTime: elastic.DateTime(time.Now().UTC()),
			Category:   "Breadcrumb Extraction",
		}

		textLookupInsertMetadata := `{"index": {"_id": "` + textLookupDocID + `"}}`
		textLookupInsertDoc, err := json.Marshal(textLookupDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling document", err)
			continue
		}

		bulkTextLookupRequest.WriteString(textLookupInsertMetadata)
		bulkTextLookupRequest.WriteString("\n")
		bulkTextLookupRequest.Write(textLookupInsertDoc)
		bulkTextLookupRequest.WriteString("\n")

		*textLookupRecordCount++
	}

	return nil
}

func stageBreadcrumbRecord(crDoc resourceutils.CloudResource, breadcrumbs []Breadcrumb, bcRecords *[]BreadcrumbRecord) error {

	var breadcrumbMap = make(map[string][]string)

	for _, breadcrumb := range breadcrumbs {

		if s, err := strconv.ParseFloat(breadcrumb.Value, 64); err == nil {
			// skip breadcrumbs with low confidence score
			if s <= 0.4 {
				continue
			}
		}

		breadcrumbMap[breadcrumb.Type] = append(breadcrumbMap[breadcrumb.Type], breadcrumb.Value)
	}

	if len(breadcrumbMap) <= 0 {
		return nil
	}

	bcBytes, err := json.Marshal(breadcrumbMap)
	if err != nil {
		logger.Print(logger.ERROR, "Marshal error", err)
		return err
	}

	// vectorPropertyHash is a unique hash generated from entityID, entityType, accountID, tenantID,
	// and the sorted breadcrumbs. It helps identify existing vectorized records and prevents
	// unnecessary re-vectoring of the same entity data.

	sortBreadcrumbs := stringutils.SortString(string(bcBytes))
	id := uuid.GenerateUUIDFromString(crDoc.EntityID + crDoc.EntityType + crDoc.AccountID + crDoc.TenantID)
	vectorPropertyHash := common.GenerateCombinedHashID(crDoc.EntityID, crDoc.EntityType, crDoc.AccountID, crDoc.TenantID, sortBreadcrumbs)

	bcRecord := BreadcrumbRecord{
		EntityID:           crDoc.EntityID,
		EntityType:         crDoc.EntityType,
		Breadcrumbs:        string(bcBytes),
		TenantID:           crDoc.TenantID,
		ServiceID:          crDoc.ServiceID,
		Source:             "resource_properties",
		ID:                 id,
		InsertTime:         elastic.DateTime(time.Now().UTC()),
		AccountID:          crDoc.AccountID,
		VectorPropertyHash: vectorPropertyHash,
	}

	*bcRecords = append(*bcRecords, bcRecord)
	return nil
}
