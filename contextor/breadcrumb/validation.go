package breadcrumb

import (
	"encoding/json"
	"fmt"

	"github.com/precize/common/azureopenai"
	"github.com/precize/contextor/ai"
	"github.com/precize/logger"
)

type ValidateResp struct {
	ContextualMatches []struct {
		ResourceID string  `json:"resourceId"`
		Reason     string  `json:"reason"`
		Score      float64 `json:"score"`
	} `json:"contextual_matches"`
}

func ValidateContext(primaryRecord, candidateRecords string, aiClient *azureopenai.Client) (*ValidateResp, error) {

	session := aiClient.NewSession()
	// session.AddSystemMessage(systemPrompt)
	session.AddUserMessage(fmt.Sprintf(ai.VALIDATION_PROMPT, primaryRecord, candidateRecords))

	out, err := session.Send()
	if err != nil {
		return nil, err
	}

	azureopenai.RemoveJSONQuoteFromResponse(&out)

	var validateResp ValidateResp

	if err := json.Unmarshal([]byte(out), &validateResp); err != nil {
		logger.Print(logger.ERROR, "Unmarshal error", err, out)
		return nil, err
	}

	return &validateResp, nil
}
