package breadcrumb

import (
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/common/azureopenai"
	"github.com/precize/common/basic/uuid"
	resourceutils "github.com/precize/common/resource"
	"github.com/precize/contextor/neighbour"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func GetSimilarResourcesUsingBreadcrumbs(resourceDocs []map[string]any, aiClient *azureopenai.Client, tenantID string, bulkContextNeighbourRequest *strings.Builder, contextNeighboursCount *int) error {

	type lookupItem struct {
		cr    resourceutils.CloudResource
		docID string
	}
	var items []lookupItem
	var textLookupIDs []string

	for _, resourceDoc := range resourceDocs {

		rscDocBytes, err := json.Marshal(resourceDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Error marshalling alert data to JSON", []string{tenantID}, err)
			continue
		}

		var crDoc resourceutils.CloudResource
		if err = json.Unmarshal(rscDocBytes, &crDoc); err != nil {
			logger.Print(logger.ERROR, "Error unmarshalling JSON", []string{tenantID}, err)
			continue
		}

		extractionReq, err := createExtractionRequest(crDoc)
		if err != nil {
			continue
		}

		extractionReqJSON, err := json.Marshal(*extractionReq)
		if err != nil {
			logger.Print(logger.ERROR, "Marshal error", err)
			continue
		}

		textHash := common.HashSha256(extractionReqJSON)
		textLookupDocID := common.GenerateCombinedHashID(textHash)

		items = append(items, lookupItem{cr: crDoc, docID: textLookupDocID})
		textLookupIDs = append(textLookupIDs, textLookupDocID)
	}

	if len(items) == 0 {
		return nil
	}

	txtLkQuery := `{"query":{"bool":{"must":[{"terms":{"_id":["` + strings.Join(textLookupIDs, `","`) + `"]}}]}},"size": ` + strconv.Itoa(len(textLookupIDs)) + `}`
	docsMap, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.TEXT_LOOKUP_INDEX}, txtLkQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Query failed", []string{tenantID}, txtLkQuery, err)
		return err
	}

	for _, it := range items {
		doc, ok := docsMap[it.docID]
		if !ok || len(doc) == 0 {
			continue
		}

		dataString, ok := doc["data"].(string)
		if !ok {
			continue
		}

		var breadcrumbs []Breadcrumb
		if err := json.Unmarshal([]byte(dataString), &breadcrumbs); err != nil {
			logger.Print(logger.ERROR, "Unmarshal error", err)
			continue
		}

		breadcrumbMap := make(map[string][]string)
		for _, breadcrumb := range breadcrumbs {
			if s, err := strconv.ParseFloat(breadcrumb.Value, 64); err == nil {
				// skip breadcrumbs with low confidence score
				if s <= 0.4 {
					continue
				}
			}
			breadcrumbMap[breadcrumb.Type] = append(breadcrumbMap[breadcrumb.Type], breadcrumb.Value)
		}

		if len(breadcrumbMap) <= 0 {
			continue
		}

		primaryRecordID := uuid.GenerateUUIDFromString(it.cr.EntityID + it.cr.EntityType + it.cr.AccountID + it.cr.TenantID)

		primaryRecord, err := GetBreadcrumbByID(primaryRecordID)
		if err != nil || primaryRecord == nil {
			continue
		}

		// search by id does not return the id in the response
		primaryRecord.ID = primaryRecordID

		if len(primaryRecord.EntityID) == 0 {
			continue
		}

		candidateRecords, err := GetSimilarBreadcrumbs(primaryRecord.Breadcrumbs, tenantID, strconv.Itoa(it.cr.ServiceID), primaryRecordID)
		if err != nil {
			continue
		}

		for _, candidateRecord := range candidateRecords {

			breadcrumbNeighbourType := "breadcrumbs"

			// generate sorted hash with entityID, entityType, accountID, tenantID of A and B
			// so that a -> b and b -> a have one record

			a := fmt.Sprintf("%s|%s|%s|%s", primaryRecord.EntityID, primaryRecord.EntityType, primaryRecord.AccountID, tenantID)
			b := fmt.Sprintf("%s|%s|%s|%s", candidateRecord.EntityID, candidateRecord.EntityType, candidateRecord.AccountID, tenantID)

			pair := []string{a, b}
			sort.Strings(pair)

			docID := common.GenerateCombinedHashID(pair[0], pair[1], breadcrumbNeighbourType, tenantID)

			rscAHash := common.GenerateCombinedHashID(primaryRecord.EntityID, primaryRecord.EntityType, primaryRecord.AccountID, tenantID)
			rscBHash := common.GenerateCombinedHashID(candidateRecord.EntityID, candidateRecord.EntityType, candidateRecord.AccountID, tenantID)

			score := NormalizeVectorDistanceToScore(candidateRecord.AdditionalInfo.Distance)

			contextNeighbourDoc := neighbour.ContextNeighboursDoc{
				Type: breadcrumbNeighbourType,
				ResourceA: neighbour.NeighbourResource{
					EntityID:     primaryRecord.EntityID,
					EntityType:   primaryRecord.EntityType,
					Value:        primaryRecord.Breadcrumbs,
					AccountID:    primaryRecord.AccountID,
					ResourceHash: rscAHash,
					DocID:        primaryRecord.ID,
				},
				ResourceB: neighbour.NeighbourResource{
					EntityID:     candidateRecord.EntityID,
					EntityType:   candidateRecord.EntityType,
					Value:        candidateRecord.Breadcrumbs,
					AccountID:    candidateRecord.AccountID,
					ResourceHash: rscBHash,
					DocID:        candidateRecord.ID,
				},
				Score:      float32(score),
				TenantID:   tenantID,
				ServiceID:  it.cr.ServiceID,
				ID:         docID,
				Deleted:    false,
				InsertTime: elastic.DateTime(time.Now()),
			}

			contextNeighbourInsertMetadata := `{"index": {"_id": "` + docID + `"}}`
			contextNeighbourInsertDoc, err := json.Marshal(contextNeighbourDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Got error marshalling document", err)
				continue
			}

			bulkContextNeighbourRequest.WriteString(contextNeighbourInsertMetadata)
			bulkContextNeighbourRequest.WriteString("\n")
			bulkContextNeighbourRequest.Write(contextNeighbourInsertDoc)
			bulkContextNeighbourRequest.WriteString("\n")

			*contextNeighboursCount++
		}
	}

	return nil
}
