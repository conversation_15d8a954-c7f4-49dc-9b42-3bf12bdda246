package main

import (
	"encoding/base64"
	"flag"
	"os"

	"github.com/precize/common"
	"github.com/precize/logger"
)

func main() {

	var (
		action = flag.String("action", "", "action name")
	)

	flag.Parse()

	logger.InitializeLogs("script", false)

	args := flag.Args()

	switch *action {

	case "encrypt-encode":
		if len(args) < 1 {
			logger.Print(logger.ERROR, "Not enough arguments")
			os.Exit(1)
		}

		input := args[0]

		output, err := common.EncryptTextAES([]byte(input))
		if err != nil {
			os.Exit(1)
		}

		encodedOutput := base64.StdEncoding.EncodeToString(output)

		logger.Print(logger.INFO, "Output", encodedOutput)

	case "decode-decrypt":
		if len(args) < 1 {
			logger.Print(logger.ERROR, "Not enough arguments")
			os.Exit(1)
		}

		input := args[0]

		decodedInput, err := base64.StdEncoding.DecodeString(input)
		if err != nil {
			logger.Print(logger.ERROR, "Decode failed", err)
		}

		output, err := common.DecryptTextAES([]byte(decodedInput))
		if err != nil {
			os.Exit(1)
		}

		logger.Print(logger.INFO, "Output", string(output))

	default:
		logger.Print(logger.ERROR, "Action not found")
		os.Exit(1)
	}
}
