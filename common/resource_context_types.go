package common

import (
	"sync"
	"time"
)

const (
	AWS_SERVICE_ID             = "1000"
	AZURE_SERVICE_ID           = "2000"
	GCP_SERVICE_ID             = "3000"
	OKTA_SERVICE_ID            = "5000"
	OPENAI_SERVICE_ID          = "9600"
	BITBUCKET_ID               = "7000"
	GITHUB_ID                  = "8000"
	GITLAB_ID                  = "9000"
	AI_VIEW_ID                 = "10000"
	AWS_SERVICE_ID_INT         = 1000
	AZURE_SERVICE_ID_INT       = 2000
	GCP_SERVICE_ID_INT         = 3000
	OKTA_SERVICE_ID_INT        = 5000
	OPENAI_SERVICE_ID_INT      = 9600
	AZUREDEVOPS_SERVICE_ID_INT = 15000
	AI_VIEW_ID_INT             = 10000
	BITBUCKET_ID_INT           = 7000
	GITHUB_ID_INT              = 8000
	GITLAB_ID_INT              = 9000
	AWS_SERVICE_CODE           = "aws"
	AZURE_SERVICE_CODE         = "azure"
	GCP_SERVICE_CODE           = "gcp"
	GITHUB_SERVICE_CODE        = "github"
	GITLAB_SERVICE_CODE        = "gitlab"
	BITBUCKET_SERVICE_CODE     = "bitbucket"
	JIRA_SERVICE_CODE          = "jira"
	OKTA_SERVICE_CODE          = "okta"
	ORCA_SERVICE_CODE          = "Orca"
	OPENAI_SERVICE_CODE        = "openai"
	PRISMA_SERVICE_CODE        = "prisma"
	WIZ_SERVICE_CODE           = "wiz"

	AZURE_RG_RESOURCE_TYPE                        = "ResourceGroup"
	AZURE_MGMTGRP_RESOURCE_TYPE                   = "MgtGroup"
	AZURE_SUBSCRIPTION_RESOURCE_TYPE              = "Subscription"
	AZURE_APP_RESOURCE_TYPE                       = "Application"
	AZURE_GRAPHAPP_RESOURCE_TYPE                  = "GraphApplication"
	AZURE_ADUSER_RESOURCE_TYPE                    = "ADUser"
	AZURE_NSG_RESOURCE_TYPE                       = "NetworkSecurityGroup"
	AZURE_VM_RESOURCE_TYPE                        = "VirtualMachine"
	AZURE_LOCATION_RESOURCE_TYPE                  = "Location"
	AZURE_SUBNET_RESOURCE_TYPE                    = "VirtualNetworkSubnet"
	AZURE_PUBLICIP_RESOURCE_TYPE                  = "PublicIPAddress"
	AZURE_VMDISK_RESOURCE_TYPE                    = "Disk"
	AZURE_SNAPSHOT_RESOURCE_TYPE                  = "snapshots"
	AZURE_STORAGEACCOUNT_RESOURCE_TYPE            = "StorageAccount"
	AZURE_MYSQL_RESOURCE_TYPE                     = "MySQL"
	AZURE_SQLDB_RESOURCE_TYPE                     = "SQLDB"
	AZURE_SQLSERVER_RESOURCE_TYPE                 = "SQLServer"
	AZURE_COSMOSDB_RESOURCE_TYPE                  = "CosmosDB"
	AZURE_DATABRICKWORKSPACE_RESOURCE_TYPE        = "Microsoft.Databricks/workspaces"
	AZURE_TENANT_RESOURCE_TYPE                    = "Tenant"
	AZURE_VIRTUALNETWORK_RESOURCE_TYPE            = "VirtualNetwork"
	AZURE_SECURITYCENTER_RESOURCE_TYPE            = "SecurityCenter"
	AZURE_KEYVAULT_RESOURCE_TYPE                  = "KeyVault"
	AZURE_MARIADB_RESOURCE_TYPE                   = "MariaDB"
	AZURE_POSTGRES_RESOURCE_TYPE                  = "Postgres"
	AZURE_REDISCACHE_RESOURCE_TYPE                = "RedisCache"
	AZURE_DEPLOYMENT_RESOURCE_TYPE                = "Deployment"
	AZURE_ROUTETABLES_RESOURCE_TYPE               = "routeTables"
	AZURE_NETWORKWATCHER_RESOURCE_TYPE            = "NetworkWatcher"
	AZURE_GROUPS_RESOURCE_TYPE                    = "groups"
	AZURE_POLICY_RESOURCE_TYPE                    = "policy"
	AZURE_DEVICE_RESOURCE_TYPE                    = "device"
	AZURE_SSHPUBLICKEYS_RESOURCE_TYPE             = "sshPublicKeys"
	AZURE_CONTAINERREGISTRY_RESOURCE_TYPE         = "ContainerRegistry"
	AZURE_AKSCLUSTER_RESOURCE_TYPE                = "AKSCluster"
	AZURE_AKSCLUSTERROLE_RESOURCE_TYPE            = "AKSClusterRole"
	AZURE_AKSNAMESPACEROLE_RESOURCE_TYPE          = "AKSNamespaceRole"
	AZURE_OPENAI_RESOURCE_TYPE                    = "OpenAI"
	AZURE_OPENAIMODEL_RESOURCE_TYPE               = "OpenAIModel"
	AZURE_OPENAIDEPLOYMENT_RESOURCE_TYPE          = "OpenAIDeployment"
	AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE       = "OpenAICF"
	AZURE_OPENAICONTENTFILTERBL_RESOURCE_TYPE     = "OpenAICFBL"
	AZURE_AIMULTISERVICEACCOUNT_RESOURCE_TYPE     = "AzureAiMultiServiceAccount"
	AZURE_MLWORKSPACE_RESOURCE_TYPE               = "MlWorkspace"
	AZURE_VIDEOINDEXER_RESOURCE_TYPE              = "VideoIndexer"
	AZURE_SPEECHSERVICE_RESOURCE_TYPE             = "SpeechService"
	AZURE_ASSIGNEDROLE_RESOURCE_TYPE              = "AssignedRole"
	AZURE_ROLEASSIGNMENT_RESOURCE_TYPE            = "AzureRoleAssignment"
	AZURE_POLICYSTATE_RESOURCE_TYPE               = "PolicyState"
	AZURE_POLICYDEFINITION_RESOURCE_TYPE          = "PolicyDefinition"
	AZURE_MLWORKSPACEENDPOINTS_RESOURCE_TYPE      = "MLWorkSpaceEndPoints"
	AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE = "MLWorkSpaceCompute"
	AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE    = "MLWorkSpaceDeployments"
	AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE         = "MLWorkspaceModels"
	AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE           = "MLWorkspaceJobs"
	AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE      = "MLWorkspaceDataStore"
	AZURE_MLFEATURESTORE_RESOURCE_TYPE            = "MLFeatureStore"
	AZURE_MLPROJECTINDEX_RESOURCE_TYPE            = "MLProjectIndex"
	AZURE_MLHUBCONNECTION_RESOURCE_TYPE           = "MLHubConnection"
	AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE    = "MLWorkspaceExperiments"
	AZURE_MLWORKSPACEDATAASSET_RESOURCE_TYPE      = "MLWorkspaceDataAsset"
	AZURE_ARCDATASQLINSTANCE_RESOURCE_TYPE        = "ArcDataSqlServerInstance"
	AZURE_ARCDATASQLDB_RESOURCE_TYPE              = "ArcDataSQLDatabase"
	AZURE_NATGATEWAY_RESOURCE_TYPE                = "NATGateway"
	AZURE_USEROWNER_RESOURCE_TYPE                 = "UserOwnedResource"
	AZURE_IPDETAILS_RESOURCE_TYPE                 = "ipDetails"
	AZURE_METRICALERT_RESOURCE_TYPE               = "MetricAlert"
	AZURE_ACTIONGROUP_RESOURCE_TYPE               = "ActionGroup"
	AZURE_SCHEDULEDQUERYRULES_RESOURCE_TYPE       = "ScheduledQueryRules"
	AZURE_VMSCALESET_RESOURCE_TYPE                = "VirtualMachineScaleSet"
	AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE      = "UserAssignedIdentity"
	AZURE_K8NODE_RESOURCE_TYPE                    = "K8sNode"
	AZURE_COMPUTEIMAGE_RESOURCE_TYPE              = "ComputeImage"
	AZURE_BUDGET_RESOURCE_TYPE                    = "Budget"
	AZURE_APPREGISTRATION_RESOURCE_TYPE           = "AppRegistration"

	AWS_ORG_RESOURCE_TYPE                            = "organizations"
	AWS_ORGUNIT_RESOURCE_TYPE                        = "OrganisationUnit"
	AWS_ACCOUNT_RESOURCE_TYPE                        = "account"
	AWS_IAM_USER_RESOURCE_TYPE                       = "User"
	AWS_IAM_ROLE_RESOURCE_TYPE                       = "IAMRole"
	AWS_SG_RESOURCE_TYPE                             = "SecurityGroup"
	AWS_EC2_RESOURCE_TYPE                            = "EC2"
	AWS_SUBNET_RESOURCE_TYPE                         = "Subnet"
	AWS_VPC_RESOURCE_TYPE                            = "Vpc"
	AWS_EBSVOLUME_RESOURCE_TYPE                      = "EBSVolume"
	AWS_EBSSNAPSHOT_RESOURCE_TYPE                    = "EBSSnapshot"
	AWS_REGION_RESOURCE_TYPE                         = "region"
	AWS_S3_RESOURCE_TYPE                             = "S3"
	AWS_AMI_RESOURCE_TYPE                            = "AMI"
	AWS_RDS_RESOURCE_TYPE                            = "RDS"
	AWS_AURORA_RESOURCE_TYPE                         = "aurora"
	AWS_RDSSNAPSHOT_RESOURCE_TYPE                    = "RDSSnapshot"
	AWS_RDSCLUSTERSNAPSHOT_RESOURCE_TYPE             = "RDSClusterSnapshot"
	AWS_RDSCLUSTER_RESOURCE_TYPE                     = "RDSCluster"
	AWS_DYNAMODB_RESOURCE_TYPE                       = "DynamoDB"
	AWS_ELASTICACHE_RESOURCE_TYPE                    = "ElastiCache"
	AWS_ROUTETABLE_RESOURCE_TYPE                     = "RouteTable"
	AWS_ROOTUSER_RESOURCE_TYPE                       = "rootUser"
	AWS_SSOUSER_RESOURCE_TYPE                        = "ssoUser"
	AWS_ELASTICIP_RESOURCE_TYPE                      = "ElasticIp"
	AWS_ELASTICSEARCH_RESOURCE_TYPE                  = "ElasticSearch"
	AWS_SQS_RESOURCE_TYPE                            = "SQS"
	AWS_SNS_RESOURCE_TYPE                            = "SNS"
	AWS_IAM_RESOURCE_TYPE                            = "IAM"
	AWS_KMS_RESOURCE_TYPE                            = "KMS"
	AWS_LAMBDA_RESOURCE_TYPE                         = "Lambda"
	AWS_CLOUDFRONT_RESOURCE_TYPE                     = "CloudFront"
	AWS_ELB_RESOURCE_TYPE                            = "ELB"
	AWS_ROUTE53_RESOURCE_TYPE                        = "Route53"
	AWS_ROUTE53DOMAIN_RESOURCE_TYPE                  = "Route53Domain"
	AWS_CLOUDFORMATION_RESOURCE_TYPE                 = "cloudformation"
	AWS_TRUSTEDADVISORY_RESOURCE_TYPE                = "TrustedAdvisory"
	AWS_IAMPOLICY_RESOURCE_TYPE                      = "IAMPolicy"
	AWS_REDISMEMORYDBCLUSTER_RESOURCE_TYPE           = "RedisMemoryDBCluster"
	AWS_IAMSERVERCERT_RESOURCE_TYPE                  = "IAMServerCert"
	AWS_NEPTUNECLUSTER_RESOURCE_TYPE                 = "NeptuneCluster"
	AWS_NEPTUNEINSTANCE_RESOURCE_TYPE                = "NeptuneInstance"
	AWS_OPENSEARCHSERVERLESSCOLLECTION_RESOURCE_TYPE = "OpenSearchServerlessCollection"
	AWS_REDSHIFT_RESOURCE_TYPE                       = "RedShift"
	AWS_TIMESTREAMDB_RESOURCE_TYPE                   = "TimeStreamDB"
	AWS_TIMESTREAMTABLE_RESOURCE_TYPE                = "TimeStreamTable"
	AWS_CONTAINERREGISTRY_RESOURCE_TYPE              = "ContainerRegistry"
	AWS_CONTAINERREPOSITORY_RESOURCE_TYPE            = "ContainerRepository"
	AWS_CONTAINERIMAGE_RESOURCE_TYPE                 = "ContainerImage"
	AWS_ECSFARGATE_RESOURCE_TYPE                     = "ECSFargate"
	AWS_EKSCLUSTER_RESOURCE_TYPE                     = "EKSCluster"
	AWS_EKSCLUSTERROLE_RESOURCE_TYPE                 = "EKSClusterRole"
	AWS_EKSNAMESPACEROLE_RESOURCE_TYPE               = "EKSNamespaceRole"
	AWS_SAGEMAKERARTIFACT_RESOURCE_TYPE              = "AWSAISageMakerArtifact"
	AWS_SAGEMAKERTRIALCOMPONENT_RESOURCE_TYPE        = "AWSAISageMakerTrialComponent"
	AWS_APIGATEWAY_RESOURCE_TYPE                     = "APIGateway"
	AWS_SAGEMAKERENDPOINTCONFIG_RESOURCE_TYPE        = "AWSAISageMakerEndpointConfig"
	AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE              = "AWSAISageMakerEndpoint"
	AWS_SAGEMAKERMODEL_RESOURCE_TYPE                 = "AWSAISageMakerModel"
	AWS_SAGEMAKERTRAININGJOB_RESOURCE_TYPE           = "AWSAISageMakerTrainingJob"
	AWS_BEDROCKMODEL_RESOURCE_TYPE                   = "BedRockModel"
	AWS_BEDROCKPROMPT_RESOURCE_TYPE                  = "BedrockPrompt"
	AWS_BEDROCKAGENT_RESOURCE_TYPE                   = "BedrockAgent"
	AWS_BEDROCKPROVMODELTHROUGHPUT_RESOURCE_TYPE     = "BedrockProvisionedModelThroughput"
	AWS_BEDROCKEVALJOB_RESOURCE_TYPE                 = "BedrockEvaluationJob"
	AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE           = "BedrockKnowledgeBase"
	AWS_BEDROCKAGENTALIASES_RESOURCE_TYPE            = "BedrockAgentAliases"
	AWS_BEDROCKGUARDRAIL_RESOURCE_TYPE               = "BedrockGuardrail"
	AWS_KNOWLEDGEBASEDATASOURCE_RESOURCE_TYPE        = "KnowledgeBaseDataSource"
	AWS_PERSONALIZERECIPE_RESOURCE_TYPE              = "PersonalizeRecipe"
	AWS_ORGANIZATIONPOLICY_RESOURCE_TYPE             = "OrganizationPolicy"
	AWS_EC2KEYPAIR_RESOURCE_TYPE                     = "EC2KeyPair"
	AWS_CFTSTACK_RESOURCE_TYPE                       = "CloudFormationStack"
	AWS_NETWORKACL_RESOURCE_TYPE                     = "NetworkACL"
	AWS_NETWORKINSIGHTSPATH_RESOURCE_TYPE            = "NetworkInsightsPath"
	AWS_INTERNETGATEWAY_RESOURCE_TYPE                = "InternetGateway"
	AWS_AICOMPREHENDDOCCLASSIFIER_RESOURCE_TYPE      = "AWSAIComprehendDocClassifier"
	AWS_BEDROCKAGENTGROUP_RESOURCE_TYPE              = "BedrockAgentGroup"
	AWS_SAGEMAKERLABELINGJOBS_RESOURCE_TYPE          = "SageMakerLabelingJobs"
	AWS_ECSCLUSTER_RESOURCE_TYPE                     = "ECSCluster"
	AWS_SSMINSTANCE_RESOURCE_TYPE                    = "SSMInstance"
	AWS_GLUECATALOGTABLE_RESOURCE_TYPE               = "GlueCatalogTable"
	AWS_GLUECATALOGDATABASE_RESOURCE_TYPE            = "GlueCatalogDatabase"
	AWS_AISAGEMAKERMODELTYPE_RESOURCE_TYPE           = "AWSAISageMakerModelTypes"
	AWS_GUARDDUTYFINDING_RESOURCE_TYPE               = "GuardDutyFinding"
	AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE              = "AWSAISageMakerDomain"
	AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE    = "AISageMakerNotebookInstance"
	AWS_ATHENA_WORKGROUP_RESOURCE_TYPE               = "AthenaWorkGroup"
	AWS_ACM_CERTIFICATE_RESOURCE_TYPE                = "acm:certificate"
	AWS_CLOUDTRAIL_TRAIL_RESOURCE_TYPE               = "cloudtrail:trail"
	AWS_LAUNCHTEMPLATE_RESOURCE_TYPE                 = "EC2LaunchTemplate"
	AWS_ECSSERVICE_RESOURCE_TYPE                     = "ECSService"
	AWS_ECSTASKDEFINITION_RESOURCE_TYPE              = "ECSTaskDefinition"
	AWS_ELBAPP_RESOURCE_TYPE                         = "elasticloadbalancing:loadbalancer/app"
	AWS_SECRETSMANAGER_RESOURCE_TYPE                 = "SecretManagerSecret"
	AWS_COGNITOUSERPOOL_RESOURCE_TYPE                = "cognito-idp:userpool"
	AWS_ELBTARGETGROUP_RESOURCE_TYPE                 = "elasticloadbalancing:targetgroup"
	AWS_ECSCONTAINERDEFINITION_RESOURCE_TYPE         = "ECSContainerDefinition"
	AWS_ECSTASK_RESOURCE_TYPE                        = "ECSTask"
	AWS_ECSCONTAINERINSTANCE_RESOURCE_TYPE           = "ECSContainerInstance"

	GCP_ORG_RESOURCE_TYPE                        = "Organization"
	GCP_FOLDER_RESOURCE_TYPE                     = "Folder"
	GCP_PROJECT_RESOURCE_TYPE                    = "Project"
	GCP_IAM_RESOURCE_TYPE                        = "GCPUser"
	GCP_GROUP_RESOURCE_TYPE                      = "GCPGroup"
	GCP_SERVICEACCOUNT_RESOURCE_TYPE             = "ServiceAccount"
	GCP_SAPOLICYBINDING_RESOURCE_TYPE            = "SAPolicyBinding"
	GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE          = "ServiceAccountKey"
	GCP_FIREWALL_RESOURCE_TYPE                   = "Firewall"
	GCP_INSTANCE_RESOURCE_TYPE                   = "VMInstance"
	GCP_GKECLUSTER_RESOURCE_TYPE                 = "GKECluster"
	GCP_GKECLUSTERROLE_RESOURCE_TYPE             = "GkeClusterRole"
	GCP_GKENAMESPACEROLE_RESOURCE_TYPE           = "GkeNamespaceRole"
	GCP_GKENAMESPACE_RESOURCE_TYPE               = "GkeNamespace"
	GCP_GKESERVICEACCOUNT_RESOURCE_TYPE          = "GkeServiceAccount"
	GCP_GKEDAEMONSET_RESOURCE_TYPE               = "GkeDaemonSet"
	GCP_GKEREPLICASET_RESOURCE_TYPE              = "GkeReplicaSet"
	GCP_GKESERVICE_RESOURCE_TYPE                 = "GkeService"
	GCP_GKENODE_RESOURCE_TYPE                    = "k8s.io/Node"
	GCP_GKEDEPLOYMENT_RESOURCE_TYPE              = "apps.k8s.io/Deployment"
	GCP_VMDISK_RESOURCE_TYPE                     = "VMDisks"
	GCP_NETWORK_RESOURCE_TYPE                    = "Network"
	GCP_SUBNETWORK_RESOURCE_TYPE                 = "SubNetwork"
	GCP_DISK_SNAPSHOT_RESOURCE_TYPE              = "DiskSnapshot"
	GCP_REGION_RESOURCE_TYPE                     = "Region"
	GCP_CONSTRAINT_RESOURCE_TYPE                 = "Constraint"
	GCP_CLOUDSTORAGE_RESOURCE_TYPE               = "CloudStorage"
	GCP_SQLDB_RESOURCE_TYPE                      = "SQLDatabase"
	GCP_BIGQUERYTABLE_RESOURCE_TYPE              = "BigQueryTable"
	GCP_BIGQUERYDATASET_RESOURCE_TYPE            = "BigQueryDataset"
	GCP_BIGTABLE_RESOURCE_TYPE                   = "bigtableadmin.googleapis.com/Table"
	GCP_BIGTABLECLUSTER_RESOURCE_TYPE            = "bigtableadmin.googleapis.com/Cluster"
	GCP_SPANNERDB_RESOURCE_TYPE                  = "spanner.googleapis.com/Database"
	GCP_SPANNERDBINSTANCE_RESOURCE_TYPE          = "spanner.googleapis.com/Instance"
	GCP_DATAPROCCLUSTER_RESOURCE_TYPE            = "dataproc.googleapis.com/Cluster"
	GCP_DATAFLOWJOB_RESOURCE_TYPE                = "dataflow.googleapis.com/Job"
	GCP_AUTOSCALER_RESOURCE_TYPE                 = "Autoscaler"
	GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE       = "InstanceGroupManager"
	GCP_INSTANCEGROUP_RESOURCE_TYPE              = "InstanceGroup"
	GCP_BACKENDSERVICE_RESOURCE_TYPE             = "BackendService"
	GCP_DNSMANAGEDZONE_RESOURCE_TYPE             = "DnsManagedZone"
	GCP_FUNCTION_RESOURCE_TYPE                   = "Function"
	GCP_GCPIAM_RESOURCE_TYPE                     = "GcpIAM"
	GCP_IMAGE_RESOURCE_TYPE                      = "Image"
	GCP_IAMPOLICY_RESOURCE_TYPE                  = "IamPolicy"
	GCP_SSLPOLICY_RESOURCE_TYPE                  = "SSLPolicy"
	GCP_LBTARGETHTTPSPROXY_RESOURCE_TYPE         = "LBTargetHttpsProxy"
	GCP_PUBSUBSNAPSHOT_RESOURCE_TYPE             = "PubsubSnapshot"
	GCP_PUBSUBSUBSCRIPTION_RESOURCE_TYPE         = "PubsubSubscription"
	GCP_PUBSUBTOPIC_RESOURCE_TYPE                = "PubsubTopic"
	GCP_KMS_RESOURCE_TYPE                        = "KMS"
	GCP_ARTIFACTREPOSITORY_RESOURCE_TYPE         = "ArtifactRepository"
	GCP_DOCKERIMAGE_RESOURCE_TYPE                = "DockerImage"
	GCP_CONTAINERREPOSITORY_RESOURCE_TYPE        = "ContainerRepository"
	GCP_CONTAINERIMAGE_RESOURCE_TYPE             = "ContainerImage"
	GCP_VERTEXAIENDPOINT_RESOURCE_TYPE           = "vertexAIEndpoint"
	GCP_VERTEXAIMODEL_RESOURCE_TYPE              = "vertexAIModel"
	GCP_VERTEXTRAINING_PIPELINE_RESOURCE_TYPE    = "vertexAITrainingPipeline"
	GCP_VERTEXFEATUREVIEW_RESOURCE_TYPE          = "vertexAIFeatureView"
	GCP_VERTEXFEATURESTORE_RESOURCE_TYPE         = "vertexAIFeatureOnlineStore"
	GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE   = "DiscoveryEngineDataStore"
	GCP_DIALOGFLOWAGENT_RESOURCE_TYPE            = "DialogFlowAgent"
	GCP_DIALOGFLOWAGENTWEBHOOK_RESOURCE_TYPE     = "DialogFlowAgentWebhook"
	GCP_DIALOGFLOWAGENTTOOL_RESOURCE_TYPE        = "DialogFlowAgentTool"
	GCP_DIALOGFLOWAGENTPLAYBOOK_RESOURCE_TYPE    = "DialogFlowAgentPlaybook"
	GCP_DIALOGFLOWAGENTSECSETTINGS_RESOURCE_TYPE = "DialogFlowAgentSecuritySettings"
	GCP_VECTORSEARCHINDEXENDPOINT_RESOURCE_TYPE  = "VectorSearchIndexEndpoint"
	GCP_VECTORSEARCHINDEXES_RESOURCE_TYPE        = "VectorSearchIndexes"
	GCP_NOTEBOOKRUNTIME_RESOURCE_TYPE            = "NotebookRunTime"
	GCP_NOTEBOOKRUNTIMETEMPLATE_RESOURCE_TYPE    = "NotebookRunTimeTemplate"
	GCP_VERTEXAINOTEBOOKINSTANCE_RESOURCE_TYPE   = "vertexAINotebookInstance"
	GCP_DOCUMENTAIPROCESSOR_RESOURCE_TYPE        = "documentAIProcessor"
	GCP_DOCUMENTAIPROCESSORVERSION_RESOURCE_TYPE = "documentAIProcessorVersion"
	GCP_CLOUDRUNSERVICE_RESOURCE_TYPE            = "CloudRunService"
	GCP_VERTEXAITENSORBOARD_RESOURCE_TYPE        = "vertexAITensorBoard"
	GCP_IPDETAILS_RESOURCE_TYPE                  = "ipDetails"
	GCP_ROLE_RESOURCE_TYPE                       = "Role"
	GCP_USER_RESOURCE_TYPE                       = "GCPUser"
	GCP_APPENGINESERVICEVERSION_RESOURCE_TYPE    = "AppEngineServiceVersion"
	GCP_APPENGINEAPPLICATION_RESOURCE_TYPE       = "AppEngineApplication"
	GCP_APPENGINESERVICE_RESOURCE_TYPE           = "AppEngineService"
	GCP_CONTAINERINSTANCETEMPLATE_RESOURCE_TYPE  = "ContainerInstanceTemplate"
	GCP_NETWORKENDPOINTGROUP_RESOURCE_TYPE       = "NetworkEndpointGroup"
	GCP_COMPUTEHEALTHCHECK_RESOURCE_TYPE         = "ComputeHealthCheck"
	GCP_DATAFORMREPOSITORY_RESOURCE_TYPE         = "DataformRepository"
	GCP_VERTEXAIPROMPT_RESOURCE_TYPE             = "vertexAIPrompt"

	GCP_VERTEXAIFEATUREONLINESTORE_RESOURCE_TYPE = "vertexAIFeatureOnlineStore"
	GCP_VERTEXAIFEATUREVIEW_RESOURCE_TYPE        = "vertexAIFeatureView"
	GCP_VERTEXAIMETADATASTORE_RESOURCE_TYPE      = "vertexAIMetadataStore"
	GCP_VERTEXAITRAININGPIPELINE_RESOURCE_TYPE   = "vertexAITrainingPipeline"
	GCP_BIGQUERYTRANSFERCONFIG_RESOURCE_TYPE     = "BigQueryTransferConfig"
	GCP_RESOURCEPOLICY_RESOURCE_TYPE             = "ResourcePolicy"

	NETWORKINTERFACE_RESOURCE_TYPE = "NetworkInterface"
	LOADBALANCER_RESOURCE_TYPE     = "LoadBalancer"
	IPDETAILS_RESOURCE_TYPE        = "ipDetails"
	APP_RESOURCE_TYPE              = "App"

	OPENAI_ORG_RESOURCE_TYPE                 = "openAIOrg"
	OPENAI_PROJECT_RESOURCE_TYPE             = "openAIProject"
	OPENAI_USER_RESOURCE_TYPE                = "openAIUser"
	OPENAI_SERVICEACCOUNT_RESOURCE_TYPE      = "openAIServiceAccount"
	OPENAI_APIKEY_RESOURCE_TYPE              = "openAIApiKey"
	OPENAI_ADMINAPIKEY_RESOURCE_TYPE         = "openAIAdminAPIKey"
	OPENAI_MODEL_RESOURCE_TYPE               = "openAI_Model"
	OPENAI_FINETUNEDMODEL_RESOURCE_TYPE      = "openAIFineTunedModel"
	OPENAI_BATCH_RESOURCE_TYPE               = "openAIBatch"
	OPENAI_ASSISTANT_RESOURCE_TYPE           = "openAIAssistant"
	OPENAI_VECTORSTORE_RESOURCE_TYPE         = "openAIVectorStore"
	OPENAI_VECTORSTOREFILE_RESOURCE_TYPE     = "openAIVectorStoreFile"
	OPENAI_TUNINGJOB_RESOURCE_TYPE           = "openAIFineTuningJob"
	OPENAI_TUNINGJOBEVENT_RESOURCE_TYPE      = "openAIFineTuningJobEvent"
	OPENAI_TUNINGJOBCHECKPOINT_RESOURCE_TYPE = "openAIFineTuningJobCheckpoint"
	OPENAI_FILE_RESOURCE_TYPE                = "openAI_File"
	OPENAI_RATELIMIT_RESOURCE_TYPE           = "openAIRateLimit"
	OPENAI_PROJECTCOST_RESOURCE_TYPE         = "openAIProjectCost"
	OPENAI_ORGCOST_RESOURCE_TYPE             = "openAIOrgCost"
	OPENAI_COMPLETIONUSAGE_RESOURCE_TYPE     = "openAICompletionUsage"

	OKTA_USER_RESOURCE_TYPE = "OktaUser"

	SNOWFLAKE_USER_RESOURCE_TYPE = "sfUser"

	PRECIZEINTERNAL_COMMITFILE_RESOURCE_TYPE = "PrecizeInternalCommitFile"

	AWS_USER_IDENTITY_TYPE      = "USER"
	AWS_SSOUSER_IDENTITY_TYPE   = "SSO_USER"
	AZURE_AD_USER_IDENTITY_TYPE = "AD_USER"
	GCP_USER_IDENTITY_TYPE      = "GCP_USER"
	OKTA_USER_IDENTITY_TYPE     = "OKTA_USER"
	AWS_ROOT_USER_IDENTITY_TYPE = "ROOT_USER"
	OPENAI_USER_IDENTITY_TYPE   = "OPEN_AI_USER"
	JIRA_USER_IDENTITY_TYPE     = "JIRA_USER"

	AWS_IAM_ROLE_IDENTITY_TYPE            = "AWS_IAM_ROLE"
	TRUSTED_ENTITY_IDENTITY_TYPE          = "TRUSTED_ENTITY"
	GRAPH_APPLICATION_IDENTITY_TYPE       = "GRAPH_APPLICATION"
	APP_REGISTRATION_IDENTITY_TYPE        = "APP_REGISTRATION"
	SERVICE_ACCOUNT_IDENTITY_TYPE         = "SERVICE_ACCOUNT"
	SA_POLICY_BINDING_IDENTITY_TYPE       = "SA_POLICY_BINDING"
	OPEN_AI_SERVICE_ACCOUNT_IDENTITY_TYPE = "OPEN_AI_SERVICE_ACCOUNT"
	GCP_SERVICEACCOUNT_IDENTITY_TYPE      = "SERVICE_ACCOUNT"
	AWS_IAMUSER_IDENTITY_TYPE             = "USER"

	ACCOUNT_OWNER_TYPE                 = "Account Owner"
	ACCOUNT_CONTACT_OWNER_TYPE         = "Account Contact Owner"
	PROJECT_OWNER_TYPE                 = "Project Owner"
	PROJECT_GROUPOWNER_OWNER_TYPE      = "Project Group Owner"
	ORG_OWNER_TYPE                     = "Organization Owner"
	ORG_GROUPOWNER_OWNER_TYPE          = "Org Group Owner"
	FOLDER_OWNER_TYPE                  = "Folder Owner"
	FOLDER_GROUPOWNER_OWNER_TYPE       = "Folder Group Owner"
	PARENTFOLDER_OWNER_TYPE            = "Parent Folder Owner"
	SUBSCRIPTION_OWNER_TYPE            = "Subscription Owner"
	SUBSCRIPTION_CONTACT_OWNER_TYPE    = "Subscription Contact Owner"
	ACTIVITY_OWNER_TYPE                = "Activity Owner"
	POLICYBINDING_OWNER_TYPE           = "Policy Binding Owner"
	REASSIGNED_OWNER_TYPE              = "Reassigned Owner"
	NO_CHILD_OWNERS_PARENT_OWNER_TYPE  = "Account Sole Owner"
	FEW_CHILD_OWNERS_PARENT_OWNER_TYPE = "Few User Account Owner"
	RELATED_RESOURCE_OWNER_TYPE        = "Related Resource Owner"
	PARENT_RESOURCE_OWNER_TYPE         = "Parent Related Resource Owner"
	SIMILAR_RESOURCENAME_OWNER_TYPE    = "Similar Resource Name Owner"
	SAME_APP_OWNER_TYPE                = "Same App Owner"
	SAME_TAG_OWNER_TYPE                = "Same Tag Owner"
	RESOURCE_TYPE_OWNER_TYPE           = "Service Owner"
	RESOURCE_NAME_OWNER_TYPE           = "Resource Name Owner"
	JIRA_OWNER_TYPE                    = "JIRA User Owner"
	RESOURCE_OWNER_TYPE                = "Resource Owner"
	RESOURCE_OWNERAPP_OWNER_TYPE       = "Resource App Owner"
	RESOURCE_OWNERIAMROLE_OWNER_TYPE   = "Resource IAM Role Owner"
	ORCA_APP_OWNER_TYPE                = "Orca Context Owner"
	OPENAIPROJECT_OWNER_TYPE           = "OpenAI Project Owner"
	OPENAIORG_OWNER_TYPE               = "OpenAI Organization Owner"
	ATTACHEDRESOURCE_OWNER_TYPE        = "Attached Resource Owner"
	CUSTOMER_DEFINED_OWNER_TYPE        = "Customer Defined Owner"
	PRECIZE_DEFINED_OWNER_TYPE         = "Precize Defined Owner"
	GITHUB_OWNER_TYPE                  = "Github Owner"
	GITLAB_OWNER_TYPE                  = "Gitlab Owner"
	BITBUCKET_OWNER_TYPE               = "Bitbucket Owner"
	ORG_ACCOUNT_OWNER_TYPE             = "Organization Account Owner"
	RESOURCE_OPS_CONTACT_OWNER_TYPE    = "Resource Ops Contact Owner"
	PRECIZE_DETECTED_OWNER_TYPE        = "Precize Detected Owner"
	DESC_OWNER_TYPE                    = "Description Owner"
	MANAGER_OWNER_TYPE                 = "Manager Owner"
	CREATOR_OWNER_TYPE                 = "Creator Owner"
	NEIGHBOUR_OWNER_TYPE               = "Neighbour Owner"

	ACCOUNT_NAME_ENV_TYPE       = "Account Name Env"
	RG_NAME_ENV_TYPE            = "ResourceGroup Name Env"
	SUBSCRIPTION_NAME_ENV_TYPE  = "Subscription Name Env"
	ORG_NAME_ENV_TYPE           = "Organization Name Env"
	FOLDER_NAME_ENV_TYPE        = "Folder Name Env"
	PROJECT_NAME_ENV_TYPE       = "Project Name Env"
	RESOURCE_NAME_ENV_TYPE      = "Resource Name Env"
	MAJORITY_ENV_TYPE           = "Majority Env"
	OPENAIPROJECT_NAME_ENV_TYPE = "OpenAI Project Name Env"
	CUSTOMER_DEFINED_ENV_TYPE   = "Customer Defined Env"
	PRECIZE_DEFINED_ENV_TYPE    = "Precize Defined Env"
	TENANT_NAME_ENV_TYPE        = "Tenant Name Env"
	MGMTGRP_NAME_ENV_TYPE       = "ManagementGroup Name Env"
	ORGUNIT_NAME_ENV_TYPE       = "OrganizationUnit Name Env"
	JIRA_ENV_TYPE               = "Jira Context Env"
	INHERITED_ENV_TYPE          = "Inherited Env"
	NEIGHBOUR_ENV_TYPE          = "Neighbour Env"

	TAGGED_IDENTITY_TYPE    = "TAGGED_IDENTITY"
	DEFINED_IDENTITY_TYPE   = "DEFINED_IDENTITY"
	ACTIVITY_IDENTITY_TYPE  = "ACTIVITY_IDENTITY"
	DERIVED_IDENTITY_TYPE   = "DERIVED_IDENTITY"
	GITHUB_IDENTITY_TYPE    = "GITHUB_IDENTITY"
	GITLAB_IDENTITY_TYPE    = "GITLAB_IDENTITY"
	BITBUCKET_IDENTITY_TYPE = "BITBUCKET_IDENTITY"

	TAGGED_OWNER_TYPE_DEPRECATED    = "TAGGED_USER"
	DEFINED_OWNER_TYPE_DEPRECATED   = "DEFINED_USER"
	ACTIVITY_OWNER_TYPE_DEPRECATED  = "ACTIVITY_USER"
	DERIVED_OWNER_TYPE_DEPRECATED   = "DERIVED_USER"
	GITHUB_OWNER_TYPE_DEPRECATED    = "GITHUB_USER"
	GITLAB_OWNER_TYPE_DEPRECATED    = "GITLAB_USER"
	BITBUCKET_OWNER_TYPE_DEPRECATED = "BITBUCKET_USER"

	RESOURCE_NAME_APP_TYPE        = "Resource Name App"
	IACTEMPLATE_APP_TYPE          = "IAC Template Property App"
	ORCACONTEXT_APP_TYPE          = "Orca Context App"
	CUSTOMER_DEFINED_APP_TYPE     = "Customer Defined App"
	PRECIZE_DEFINED_APP_TYPE      = "Precize Defined App"
	DEFENDERCONTEXT_APP_TYPE      = "Defender Context App"
	JIRA_APP_TYPE                 = "Jira Context App"
	RELATED_RESOURCE_APP_TYPE     = "Related Resource App"
	DESC_APP_TYPE                 = "Description App"
	SIMILAR_RESOURCENAME_APP_TYPE = "Similar Resource Name App"
	SAME_TAG_APP_TYPE             = "Same Tag App"
	ACTIVITY_APP_TYPE             = "Activity App"
	NEIGHBOUR_APP_TYPE            = "Neighbour App"

	RESOURCE_NAME_SOFTWARE_TYPE    = "Resource Name Software"
	DEFAULT_PORT_SOFTWARE_TYPE     = "Default App Port Software"
	IACTEMPLATE_SOFTWARE_TYPE      = "IAC Template Property Software"
	ORCACONTEXT_SOFTWARE_TYPE      = "Orca Context Software"
	CUSTOMER_DEFINED_SOFTWARE_TYPE = "Customer Defined Software"
	DEFENDERCONTEXT_SOFTWARE_TYPE  = "Defender Context Software"
	JIRA_SOFTWARE_TYPE             = "Jira Context Software"
	PRECIZE_DEFINED_SOFTWARE_TYPE  = "Precize Defined Software"
	DESC_SOFTWARE_TYPE             = "Description Software"

	RESOURCE_NAME_DEPLOYMENT_TYPE    = "Resource Name Deployment"
	ACTIVITY_DEPLOYMENT_TYPE         = "Activity Deployment"
	RESOURCETYPE_DEPLOYMENT_TYPE     = "Resource Type Deployment"
	CUSTOMER_DEFINED_DEPLOYMENT_TYPE = "Customer Defined Deployment"
	PRECIZE_DEFINED_DEPLOYMENT_TYPE  = "Precize Defined Deployment"
	JIRA_DEPLOYMENT_TYPE             = "Jira Context Deployment"
	RESOURCEPROPERTY_DEPLOYMENT_TYPE = "Resource Property Deployment"
	DESC_DEPLOYMENT_TYPE             = "Description Deployment"
	DEFAULT_RESOURCE_DEPLOYMENT_TYPE = "Default Resource Deployment"
	NEIGHBOUR_DEPLOYMENT_TYPE        = "Neighbour Deployment"

	RESOURCE_NAME_COMPLIANCE_TYPE    = "Resource Name Compliance"
	CUSTOMER_DEFINED_COMPLIANCE_TYPE = "Customer Defined Compliance"
	PRECIZE_DEFINED_COMPLIANCE_TYPE  = "Precize Defined Compliance"
	DESC_COMPLIANCE_TYPE             = "Description Compliance"
	CHILD_COMPLIANCE_TYPE            = "Child Compliance"
	INHERITED_COMPLIANCE_TYPE        = "Inherited Compliance"
	NEIGHBOUR_COMPLIANCE_TYPE        = "Neighbour Compliance"

	RESOURCE_NAME_SENSITIVITY_TYPE    = "Resource Name Sensitivity"
	CUSTOMER_DEFINED_SENSITIVITY_TYPE = "Customer Defined Sensitivity"
	PRECIZE_DEFINED_SENSITIVITY_TYPE  = "Precize Defined Sensitivity"
	RESOURCE_DATA_SENSITIVITY_TYPE    = "Resource Data Sensitivity"
	ORCACONTEXT_SENSITIVITY_TYPE      = "Orca Context Sensitivity"
	JIRACONTEXT_SENSITIVITY_TYPE      = "Jira Context Sensitivity"
	DESC_SENSITIVITY_TYPE             = "Description Sensitivity"
	CHILD_SENSITIVITY_TYPE            = "Child Sensitivity"
	INHERITED_SENSITIVITY_TYPE        = "Inherited Sensitivity"
	NEIGHBOUR_SENSITIVITY_TYPE        = "Neighbour Sensitivity"

	PARENT_PATH_COSTCENTER_TYPE      = "Parent Path Cost Center"
	CUSTOMER_DEFINED_COSTCENTER_TYPE = "Customer Defined Cost Center"
	PRECIZE_DEFINED_COSTCENTER_TYPE  = "Precize Defined Cost Center"
	INHERITED_COSTCENTER_TYPE        = "Inherited Cost Center"
	NEIGHBOUR_COSTCENTER_TYPE        = "Neighbour Cost Center"

	ORGUNIT_NAME_TEAM_TYPE         = "OrganizationUnit Name Team"
	ACCOUNT_NAME_TEAM_TYPE         = "Account Name Team"
	MGMTGRP_NAME_TEAM_TYPE         = "ManagementGroup Name Team"
	SUBSCRIPTION_NAME_TEAM_TYPE    = "Subscription Name Team"
	RG_NAME_TEAM_TYPE              = "ResourceGroup Name Team"
	FOLDER_NAME_TEAM_TYPE          = "Folder Name Team"
	PROJECT_NAME_TEAM_TYPE         = "Project Name Team"
	RESOURCE_NAME_TEAM_TYPE        = "Resource Name Team"
	RESOURCE_OWNER_TEAM_TYPE       = "Resource Owner Team"
	CUSTOMER_DEFINED_TEAM_TYPE     = "Customer Defined Team"
	RELATED_RESOURCE_TEAM_TYPE     = "Related Resource Team"
	PRECIZE_DEFINED_TEAM_TYPE      = "Precize Defined Team"
	DESC_TEAM_TYPE                 = "Description Team"
	SIMILAR_RESOURCENAME_TEAM_TYPE = "Similar Resource Name Team"
	SAME_APP_TEAM_TYPE             = "Same App Team"
	SAME_TAG_TEAM_TYPE             = "Same Tag Team"
	INHERITED_TEAM_TYPE            = "Inherited Team"
	NEIGHBOUR_TEAM_TYPE            = "Neighbour Team"

	RESOURCE_NAME_TTL_TYPE    = "Resource Name TTL"
	CUSTOMER_DEFINED_TTL_TYPE = "Customer Defined TTL"
	PRECIZE_DEFINED_TTL_TYPE  = "Precize Defined TTL"
	INHERITED_TTL_TYPE        = "Inherited TTL"

	CURRENT_EMPLOYEE_IDENTITY_STATUS = 0
	EX_EMPLOYEE_IDENTITY_STATUS      = 1
	VALID_IDENTITY_STATUS            = 2
	AMBIGUOUS_IDENTITY_STATUS        = 3
	UNKNOWN_IDENTITY_STATUS          = -1

	DERIVEDFROM_DEPLOYMENT_TYPE = "deploymentLog"

	CREATOR_RCTXITEM_OWNERTYPE = "CREATOR"

	RCTX_ITEM_OBJECT_TYPE_PRIMARY   = 0
	RCTX_ITEM_OBJECT_TYPE_SECONDARY = 1

	OWNER_CONTEXT_TYPE       = "owner"
	ENV_CONTEXT_TYPE         = "env"
	APP_CONTEXT_TYPE         = "app"
	SOFTWARE_CONTEXT_TYPE    = "software"
	DEPLOYMENT_CONTEXT_TYPE  = "deployment"
	COMPLIANCE_CONTEXT_TYPE  = "compliance"
	SENSITIVITY_CONTEXT_TYPE = "sensitivity"
	TEAM_CONTEXT_TYPE        = "team"
	COSTCENTER_CONTEXT_TYPE  = "costcenter"
	TTL_CONTEXT_TYPE         = "ttl"
)

var ContextItemToContextType = map[string]string{
	ACCOUNT_OWNER_TYPE:                 OWNER_CONTEXT_TYPE,
	ACCOUNT_CONTACT_OWNER_TYPE:         OWNER_CONTEXT_TYPE,
	PROJECT_OWNER_TYPE:                 OWNER_CONTEXT_TYPE,
	PROJECT_GROUPOWNER_OWNER_TYPE:      OWNER_CONTEXT_TYPE,
	ORG_OWNER_TYPE:                     OWNER_CONTEXT_TYPE,
	ORG_GROUPOWNER_OWNER_TYPE:          OWNER_CONTEXT_TYPE,
	FOLDER_OWNER_TYPE:                  OWNER_CONTEXT_TYPE,
	FOLDER_GROUPOWNER_OWNER_TYPE:       OWNER_CONTEXT_TYPE,
	PARENTFOLDER_OWNER_TYPE:            OWNER_CONTEXT_TYPE,
	SUBSCRIPTION_OWNER_TYPE:            OWNER_CONTEXT_TYPE,
	SUBSCRIPTION_CONTACT_OWNER_TYPE:    OWNER_CONTEXT_TYPE,
	ACTIVITY_OWNER_TYPE:                OWNER_CONTEXT_TYPE,
	POLICYBINDING_OWNER_TYPE:           OWNER_CONTEXT_TYPE,
	REASSIGNED_OWNER_TYPE:              OWNER_CONTEXT_TYPE,
	NO_CHILD_OWNERS_PARENT_OWNER_TYPE:  OWNER_CONTEXT_TYPE,
	FEW_CHILD_OWNERS_PARENT_OWNER_TYPE: OWNER_CONTEXT_TYPE,
	RELATED_RESOURCE_OWNER_TYPE:        OWNER_CONTEXT_TYPE,
	PARENT_RESOURCE_OWNER_TYPE:         OWNER_CONTEXT_TYPE,
	SIMILAR_RESOURCENAME_OWNER_TYPE:    OWNER_CONTEXT_TYPE,
	SAME_APP_OWNER_TYPE:                OWNER_CONTEXT_TYPE,
	SAME_TAG_OWNER_TYPE:                OWNER_CONTEXT_TYPE,
	RESOURCE_TYPE_OWNER_TYPE:           OWNER_CONTEXT_TYPE,
	RESOURCE_NAME_OWNER_TYPE:           OWNER_CONTEXT_TYPE,
	JIRA_OWNER_TYPE:                    OWNER_CONTEXT_TYPE,
	RESOURCE_OWNER_TYPE:                OWNER_CONTEXT_TYPE,
	RESOURCE_OWNERAPP_OWNER_TYPE:       OWNER_CONTEXT_TYPE,
	RESOURCE_OWNERIAMROLE_OWNER_TYPE:   OWNER_CONTEXT_TYPE,
	ORCA_APP_OWNER_TYPE:                OWNER_CONTEXT_TYPE,
	OPENAIPROJECT_OWNER_TYPE:           OWNER_CONTEXT_TYPE,
	OPENAIORG_OWNER_TYPE:               OWNER_CONTEXT_TYPE,
	ATTACHEDRESOURCE_OWNER_TYPE:        OWNER_CONTEXT_TYPE,
	CUSTOMER_DEFINED_OWNER_TYPE:        OWNER_CONTEXT_TYPE,
	PRECIZE_DEFINED_OWNER_TYPE:         OWNER_CONTEXT_TYPE,
	GITHUB_OWNER_TYPE:                  OWNER_CONTEXT_TYPE,
	GITLAB_OWNER_TYPE:                  OWNER_CONTEXT_TYPE,
	BITBUCKET_OWNER_TYPE:               OWNER_CONTEXT_TYPE,
	ORG_ACCOUNT_OWNER_TYPE:             OWNER_CONTEXT_TYPE,
	RESOURCE_OPS_CONTACT_OWNER_TYPE:    OWNER_CONTEXT_TYPE,
	PRECIZE_DETECTED_OWNER_TYPE:        OWNER_CONTEXT_TYPE,
	DESC_OWNER_TYPE:                    OWNER_CONTEXT_TYPE,
	MANAGER_OWNER_TYPE:                 OWNER_CONTEXT_TYPE,
	CREATOR_OWNER_TYPE:                 OWNER_CONTEXT_TYPE,
	NEIGHBOUR_OWNER_TYPE:               OWNER_CONTEXT_TYPE,
	ACCOUNT_NAME_ENV_TYPE:              ENV_CONTEXT_TYPE,
	RG_NAME_ENV_TYPE:                   ENV_CONTEXT_TYPE,
	SUBSCRIPTION_NAME_ENV_TYPE:         ENV_CONTEXT_TYPE,
	ORG_NAME_ENV_TYPE:                  ENV_CONTEXT_TYPE,
	FOLDER_NAME_ENV_TYPE:               ENV_CONTEXT_TYPE,
	PROJECT_NAME_ENV_TYPE:              ENV_CONTEXT_TYPE,
	RESOURCE_NAME_ENV_TYPE:             ENV_CONTEXT_TYPE,
	MAJORITY_ENV_TYPE:                  ENV_CONTEXT_TYPE,
	OPENAIPROJECT_NAME_ENV_TYPE:        ENV_CONTEXT_TYPE,
	CUSTOMER_DEFINED_ENV_TYPE:          ENV_CONTEXT_TYPE,
	PRECIZE_DEFINED_ENV_TYPE:           ENV_CONTEXT_TYPE,
	TENANT_NAME_ENV_TYPE:               ENV_CONTEXT_TYPE,
	MGMTGRP_NAME_ENV_TYPE:              ENV_CONTEXT_TYPE,
	ORGUNIT_NAME_ENV_TYPE:              ENV_CONTEXT_TYPE,
	JIRA_ENV_TYPE:                      ENV_CONTEXT_TYPE,
	INHERITED_ENV_TYPE:                 ENV_CONTEXT_TYPE,
	NEIGHBOUR_ENV_TYPE:                 ENV_CONTEXT_TYPE,
	RESOURCE_NAME_APP_TYPE:             APP_CONTEXT_TYPE,
	IACTEMPLATE_APP_TYPE:               APP_CONTEXT_TYPE,
	ORCACONTEXT_APP_TYPE:               APP_CONTEXT_TYPE,
	CUSTOMER_DEFINED_APP_TYPE:          APP_CONTEXT_TYPE,
	PRECIZE_DEFINED_APP_TYPE:           APP_CONTEXT_TYPE,
	DEFENDERCONTEXT_APP_TYPE:           APP_CONTEXT_TYPE,
	JIRA_APP_TYPE:                      APP_CONTEXT_TYPE,
	RELATED_RESOURCE_APP_TYPE:          APP_CONTEXT_TYPE,
	DESC_APP_TYPE:                      APP_CONTEXT_TYPE,
	SIMILAR_RESOURCENAME_APP_TYPE:      APP_CONTEXT_TYPE,
	SAME_TAG_APP_TYPE:                  APP_CONTEXT_TYPE,
	ACTIVITY_APP_TYPE:                  APP_CONTEXT_TYPE,
	NEIGHBOUR_APP_TYPE:                 APP_CONTEXT_TYPE,
	RESOURCE_NAME_SOFTWARE_TYPE:        SOFTWARE_CONTEXT_TYPE,
	DEFAULT_PORT_SOFTWARE_TYPE:         SOFTWARE_CONTEXT_TYPE,
	IACTEMPLATE_SOFTWARE_TYPE:          SOFTWARE_CONTEXT_TYPE,
	ORCACONTEXT_SOFTWARE_TYPE:          SOFTWARE_CONTEXT_TYPE,
	CUSTOMER_DEFINED_SOFTWARE_TYPE:     SOFTWARE_CONTEXT_TYPE,
	DEFENDERCONTEXT_SOFTWARE_TYPE:      SOFTWARE_CONTEXT_TYPE,
	JIRA_SOFTWARE_TYPE:                 SOFTWARE_CONTEXT_TYPE,
	PRECIZE_DEFINED_SOFTWARE_TYPE:      SOFTWARE_CONTEXT_TYPE,
	DESC_SOFTWARE_TYPE:                 SOFTWARE_CONTEXT_TYPE,
	RESOURCE_NAME_DEPLOYMENT_TYPE:      DEPLOYMENT_CONTEXT_TYPE,
	ACTIVITY_DEPLOYMENT_TYPE:           DEPLOYMENT_CONTEXT_TYPE,
	RESOURCETYPE_DEPLOYMENT_TYPE:       DEPLOYMENT_CONTEXT_TYPE,
	CUSTOMER_DEFINED_DEPLOYMENT_TYPE:   DEPLOYMENT_CONTEXT_TYPE,
	PRECIZE_DEFINED_DEPLOYMENT_TYPE:    DEPLOYMENT_CONTEXT_TYPE,
	JIRA_DEPLOYMENT_TYPE:               DEPLOYMENT_CONTEXT_TYPE,
	RESOURCEPROPERTY_DEPLOYMENT_TYPE:   DEPLOYMENT_CONTEXT_TYPE,
	DESC_DEPLOYMENT_TYPE:               DEPLOYMENT_CONTEXT_TYPE,
	DEFAULT_RESOURCE_DEPLOYMENT_TYPE:   DEPLOYMENT_CONTEXT_TYPE,
	RESOURCE_NAME_COMPLIANCE_TYPE:      COMPLIANCE_CONTEXT_TYPE,
	CUSTOMER_DEFINED_COMPLIANCE_TYPE:   COMPLIANCE_CONTEXT_TYPE,
	PRECIZE_DEFINED_COMPLIANCE_TYPE:    COMPLIANCE_CONTEXT_TYPE,
	DESC_COMPLIANCE_TYPE:               COMPLIANCE_CONTEXT_TYPE,
	CHILD_COMPLIANCE_TYPE:              COMPLIANCE_CONTEXT_TYPE,
	INHERITED_COMPLIANCE_TYPE:          COMPLIANCE_CONTEXT_TYPE,
	NEIGHBOUR_COMPLIANCE_TYPE:          COMPLIANCE_CONTEXT_TYPE,
	RESOURCE_NAME_SENSITIVITY_TYPE:     SENSITIVITY_CONTEXT_TYPE,
	CUSTOMER_DEFINED_SENSITIVITY_TYPE:  SENSITIVITY_CONTEXT_TYPE,
	PRECIZE_DEFINED_SENSITIVITY_TYPE:   SENSITIVITY_CONTEXT_TYPE,
	RESOURCE_DATA_SENSITIVITY_TYPE:     SENSITIVITY_CONTEXT_TYPE,
	ORCACONTEXT_SENSITIVITY_TYPE:       SENSITIVITY_CONTEXT_TYPE,
	JIRACONTEXT_SENSITIVITY_TYPE:       SENSITIVITY_CONTEXT_TYPE,
	DESC_SENSITIVITY_TYPE:              SENSITIVITY_CONTEXT_TYPE,
	CHILD_SENSITIVITY_TYPE:             SENSITIVITY_CONTEXT_TYPE,
	INHERITED_SENSITIVITY_TYPE:         SENSITIVITY_CONTEXT_TYPE,
	NEIGHBOUR_SENSITIVITY_TYPE:         SENSITIVITY_CONTEXT_TYPE,
	PARENT_PATH_COSTCENTER_TYPE:        COSTCENTER_CONTEXT_TYPE,
	CUSTOMER_DEFINED_COSTCENTER_TYPE:   COSTCENTER_CONTEXT_TYPE,
	PRECIZE_DEFINED_COSTCENTER_TYPE:    COSTCENTER_CONTEXT_TYPE,
	INHERITED_COSTCENTER_TYPE:          COSTCENTER_CONTEXT_TYPE,
	NEIGHBOUR_COSTCENTER_TYPE:          COSTCENTER_CONTEXT_TYPE,
	ORGUNIT_NAME_TEAM_TYPE:             TEAM_CONTEXT_TYPE,
	ACCOUNT_NAME_TEAM_TYPE:             TEAM_CONTEXT_TYPE,
	MGMTGRP_NAME_TEAM_TYPE:             TEAM_CONTEXT_TYPE,
	SUBSCRIPTION_NAME_TEAM_TYPE:        TEAM_CONTEXT_TYPE,
	RG_NAME_TEAM_TYPE:                  TEAM_CONTEXT_TYPE,
	FOLDER_NAME_TEAM_TYPE:              TEAM_CONTEXT_TYPE,
	PROJECT_NAME_TEAM_TYPE:             TEAM_CONTEXT_TYPE,
	RESOURCE_NAME_TEAM_TYPE:            TEAM_CONTEXT_TYPE,
	RESOURCE_OWNER_TEAM_TYPE:           TEAM_CONTEXT_TYPE,
	CUSTOMER_DEFINED_TEAM_TYPE:         TEAM_CONTEXT_TYPE,
	RELATED_RESOURCE_TEAM_TYPE:         TEAM_CONTEXT_TYPE,
	PRECIZE_DEFINED_TEAM_TYPE:          TEAM_CONTEXT_TYPE,
	DESC_TEAM_TYPE:                     TEAM_CONTEXT_TYPE,
	SIMILAR_RESOURCENAME_TEAM_TYPE:     TEAM_CONTEXT_TYPE,
	SAME_APP_TEAM_TYPE:                 TEAM_CONTEXT_TYPE,
	SAME_TAG_TEAM_TYPE:                 TEAM_CONTEXT_TYPE,
	INHERITED_TEAM_TYPE:                TEAM_CONTEXT_TYPE,
	NEIGHBOUR_TEAM_TYPE:                TEAM_CONTEXT_TYPE,
	RESOURCE_NAME_TTL_TYPE:             TTL_CONTEXT_TYPE,
	CUSTOMER_DEFINED_TTL_TYPE:          TTL_CONTEXT_TYPE,
	PRECIZE_DEFINED_TTL_TYPE:           TTL_CONTEXT_TYPE,
	INHERITED_TTL_TYPE:                 TTL_CONTEXT_TYPE,
}

var AWSImpactResource = []string{
	AWS_EC2_RESOURCE_TYPE,
	AWS_EBSVOLUME_RESOURCE_TYPE,
	AWS_RDS_RESOURCE_TYPE,
	AWS_DYNAMODB_RESOURCE_TYPE,
	AWS_TIMESTREAMDB_RESOURCE_TYPE,
	AWS_TIMESTREAMTABLE_RESOURCE_TYPE,
	AWS_EKSCLUSTER_RESOURCE_TYPE,
	AWS_RDSSNAPSHOT_RESOURCE_TYPE,
	AWS_RDSCLUSTERSNAPSHOT_RESOURCE_TYPE,
	AWS_S3_RESOURCE_TYPE,
	AWS_ACCOUNT_RESOURCE_TYPE,
	AWS_ORGUNIT_RESOURCE_TYPE,
	AWS_ORG_RESOURCE_TYPE,
	AWS_AMI_RESOURCE_TYPE,
	AWS_CONTAINERIMAGE_RESOURCE_TYPE,
	AWS_IAM_ROLE_RESOURCE_TYPE,
	AWS_IAM_USER_RESOURCE_TYPE,
	AWS_SSOUSER_IDENTITY_TYPE,
	AWS_LAMBDA_RESOURCE_TYPE,
	AWS_BEDROCKMODEL_RESOURCE_TYPE,
	AWS_KMS_RESOURCE_TYPE,
}

var GCPImpactResource = []string{
	GCP_INSTANCE_RESOURCE_TYPE,
	GCP_VMDISK_RESOURCE_TYPE,
	GCP_DISK_SNAPSHOT_RESOURCE_TYPE,
	GCP_SQLDB_RESOURCE_TYPE,
	GCP_BIGQUERYTABLE_RESOURCE_TYPE,
	GCP_BIGQUERYDATASET_RESOURCE_TYPE,
	GCP_BIGTABLE_RESOURCE_TYPE,
	GCP_BIGTABLECLUSTER_RESOURCE_TYPE,
	GCP_SPANNERDB_RESOURCE_TYPE,
	GCP_SPANNERDBINSTANCE_RESOURCE_TYPE,
	GCP_CLOUDSTORAGE_RESOURCE_TYPE,
	GCP_FOLDER_RESOURCE_TYPE,
	GCP_PROJECT_RESOURCE_TYPE,
	GCP_ORG_RESOURCE_TYPE,
	GCP_SERVICEACCOUNT_RESOURCE_TYPE,
}

var AZUREImpactResource = []string{
	AZURE_VM_RESOURCE_TYPE,
	AZURE_VMDISK_RESOURCE_TYPE,
	AZURE_SNAPSHOT_RESOURCE_TYPE,
	AZURE_MYSQL_RESOURCE_TYPE,
	AZURE_SQLDB_RESOURCE_TYPE,
	AZURE_SQLSERVER_RESOURCE_TYPE,
	AZURE_COSMOSDB_RESOURCE_TYPE,
	AZURE_MARIADB_RESOURCE_TYPE,
	AZURE_POSTGRES_RESOURCE_TYPE,
	AZURE_STORAGEACCOUNT_RESOURCE_TYPE,
	AZURE_RG_RESOURCE_TYPE,
	AZURE_SUBSCRIPTION_RESOURCE_TYPE,
	AZURE_MGMTGRP_RESOURCE_TYPE,
	AZURE_TENANT_RESOURCE_TYPE,
	AZURE_GRAPHAPP_RESOURCE_TYPE,
	AZURE_OPENAI_RESOURCE_TYPE,
}

var (
	ServiceIDs       = []string{AWS_SERVICE_ID, AZURE_SERVICE_ID, GCP_SERVICE_ID, OPENAI_SERVICE_ID}
	CspStrToIdIntMap = map[string]int{
		"aws":    AWS_SERVICE_ID_INT,
		"gcp":    GCP_SERVICE_ID_INT,
		"azure":  AZURE_SERVICE_ID_INT,
		"openai": OPENAI_SERVICE_ID_INT,
		"okta":   OKTA_SERVICE_ID_INT,
	}
	CspStrToIdStrMap = map[string]string{
		"aws":    AWS_SERVICE_ID,
		"gcp":    GCP_SERVICE_ID,
		"azure":  AZURE_SERVICE_ID,
		"openai": OPENAI_SERVICE_ID,
		"okta":   OKTA_SERVICE_ID,
	}
	IdStrToCspStrMap = map[string]string{
		AWS_SERVICE_ID:    "aws",
		GCP_SERVICE_ID:    "gcp",
		AZURE_SERVICE_ID:  "azure",
		OPENAI_SERVICE_ID: "openai",
		OKTA_SERVICE_ID:   "okta",
	}
)

// Adding any new field to this struct has to be informed to platform team (to be added in java object)
type ResourceContextInsertDoc struct {
	ID                string      `json:"id"`
	ResourceID        string      `json:"resourceId"`
	ResourceType      string      `json:"resourceType"`
	Account           string      `json:"account"`
	TenantID          string      `json:"tenantId"`
	Region            string      `json:"region"`
	ServiceID         int         `json:"serviceId"`
	UpdatedTime       string      `json:"updatedTime"`
	LastCollectedAt   string      `json:"lastCollectedAt"`
	UserAgents        []UserAgent `json:"userAgents"`
	ContextLabels     []string    `json:"contextLabels,omitempty"`
	IsDefaultResource bool        `json:"isDefaultResource"`

	ResourceOwnerTypes
	ResourceEnvTypes
	ResourceAppTypes
	ResourceSoftwareTypes
	ResourceDeploymentTypes
	ResourceSensitivityTypes
	ResourceComplianceTypes
	ResourceCostCenterTypes
	ResourceTeamTypes
	ResourceTTLTypes

	ResourceName       string         `json:"-"`
	ResourceGroup      string         `json:"-"`
	OSType             string         `json:"-"`
	HasConsoleLogin    bool           `json:"-"`
	SkipContext        bool           `json:"-"`
	CommitInfo         CommitInfo     `json:"-"`
	CreatedDate        time.Time      `json:"-"`
	CloudResourceDocID string         `json:"-"`
	AdditionalInfo     map[string]any `json:"-"`
}

type ResourceOwnerTypes struct {
	DefinedOwners   []ResourceContextItem `json:"definedOwners,omitempty"`
	DerivedOwners   []ResourceContextItem `json:"derivedOwners,omitempty"`
	InheritedOwners []ResourceContextItem `json:"inheritedOwners,omitempty"`
	CodeOwners      []ResourceContextItem `json:"codeOwners,omitempty"`
	CostOwners      []ResourceContextItem `json:"costOwners,omitempty"`
	SecurityOwners  []ResourceContextItem `json:"securityOwners,omitempty"`
	OpsOwners       []ResourceContextItem `json:"opsOwners,omitempty"`
}

type ResourceEnvTypes struct {
	DefinedEnv   []ResourceContextItem `json:"definedEnv,omitempty"`
	DerivedEnv   []ResourceContextItem `json:"derivedEnv,omitempty"`
	InheritedEnv []ResourceContextItem `json:"inheritedEnv,omitempty"`
}

type ResourceAppTypes struct {
	DefinedApp []ResourceContextItem `json:"definedApp,omitempty"`
	DerivedApp []ResourceContextItem `json:"derivedApp,omitempty"`
}

type ResourceSoftwareTypes struct {
	DefinedSoftware []ResourceContextItem `json:"definedSoftware,omitempty"`
	DerivedSoftware []ResourceContextItem `json:"derivedSoftware,omitempty"`
}

type ResourceDeploymentTypes struct {
	DefinedDeployment []ResourceContextItem `json:"definedDeployment,omitempty"`
	DerivedDeployment []ResourceContextItem `json:"derivedDeployment,omitempty"`
}

type ResourceSensitivityTypes struct {
	DefinedSensitivity   []ResourceContextItem `json:"definedSensitivity,omitempty"`
	DerivedSensitivity   []ResourceContextItem `json:"derivedSensitivity,omitempty"`
	InheritedSensitivity []ResourceContextItem `json:"inheritedSensitivity,omitempty"`
}

type ResourceComplianceTypes struct {
	DefinedCompliance   []ResourceContextItem `json:"definedCompliance,omitempty"`
	DerivedCompliance   []ResourceContextItem `json:"derivedCompliance,omitempty"`
	InheritedCompliance []ResourceContextItem `json:"inheritedCompliance,omitempty"`
}

type ResourceCostCenterTypes struct {
	DefinedCostCenter   []ResourceContextItem `json:"definedCostCenter,omitempty"`
	InheritedCostCenter []ResourceContextItem `json:"inheritedCostCenter,omitempty"`
}

type ResourceTeamTypes struct {
	DefinedTeam   []ResourceContextItem `json:"definedTeam,omitempty"`
	DerivedTeam   []ResourceContextItem `json:"derivedTeam,omitempty"`
	InheritedTeam []ResourceContextItem `json:"inheritedTeam,omitempty"`
}

type ResourceTTLTypes struct {
	DefinedTTL   []ResourceContextItem `json:"definedTTL,omitempty"`
	DerivedTTL   []ResourceContextItem `json:"derivedTTL,omitempty"`
	InheritedTTL []ResourceContextItem `json:"inheritedTTL,omitempty"`
}

type ResourceContextItem struct {
	Name            string            `json:"name"`
	Type            string            `json:"type,omitempty"`
	Desc            string            `json:"desc,omitempty"`
	Confidence      float32           `json:"confidence"`
	IdentityId      string            `json:"identityId,omitempty"`
	ChildIdentityID string            `json:"childIdentityId,omitempty"`
	Event           *ResourceCtxEvent `json:"event,omitempty"`
	// If collected from previous scan
	CollectedFrom string `json:"collectedFrom,omitempty"`
	ObjectType    *int   `json:"objectType,omitempty"`
	OwnerResourceCtxItem

	Additional map[string]any `json:"-"`
}

type OwnerResourceCtxItem struct {
	OwnerSubType   string `json:"ownerSubType,omitempty"` // Denotes specific owner types like Creator, Technical Owner, etc
	IdentityStatus *int   `json:"identityStatus,omitempty"`
}

type CloudResourceUpdateDoc struct {
	Owner       []string
	Environment []string
}

type TagObject struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// Deprecated - Using CustomerEntityContextDoc now
type CustomerDefinedContext struct {
	EntityID      string   `json:"entityId"`
	Owners        []string `json:"owners,omitempty"`
	Environments  []string `json:"envs,omitempty"`
	Applications  []string `json:"apps,omitempty"`
	Sensitivities []string `json:"sensitivities,omitempty"`
	Compliances   []string `json:"compliances,omitempty"`
	// EntityType string `json:"entityType"`
	// AccountID string `json:"accountId"`
	// ServiceID string `json:"serviceId"`
	// TenantID string `json:"tenantId"`
	// DefinitionType string `json:"definitionType"`
}

type CustomerEntityContextDoc struct {
	EntityID   string `json:"entityId"`
	EntityType string `json:"entityType"`
	// AccountID string `json:"accountId"`
	ServiceID         int                       `json:"serviceId"`
	InsertTime        string                    `json:"insertTime"`
	UpdatedBy         string                    `json:"updatedBy"`
	EntityDocID       string                    `json:"entityDocId"`
	TenantID          string                    `json:"tenantId"`
	UpdateTime        string                    `json:"updateTime"`
	ID                string                    `json:"id"`
	Type              string                    `json:"type"`
	ContextProperties []EntityContextProperties `json:"entityContextProperties"`
}

type EntityContextProperties struct {
	DisplayText  string   `json:"displayText"`
	PropertyName string   `json:"propertyName"` // owner, app , sensitivity, compliance, env
	Include      []string `json:"include"`
	Exclude      []string `json:"exclude"`
}

type CommitInfo struct {
	RepoName     string   `json:"repoName"`
	FileName     string   `json:"fileName"`
	CommitDocIDs []string `json:"commitDocIds"`
}

type ResourceCtxEvent struct {
	Name            string `json:"name"`
	Region          string `json:"region"`
	Time            string `json:"time"`
	IndirectEvent   bool   `json:"indirectEvent"`
	IdentityAccount string `json:"identityAccount,omitempty"`
}

type UserAgent struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

var ResourceCountMutex sync.Mutex

type ResourceCounts struct {
	AccountCount       int
	ResourceTypeCount  map[string]int
	ResourceGroupCount map[string]int
}
