package contextutils

const (
	PROD_ENV    = "Production"
	STAGING_ENV = "QA/Staging"
	DEV_ENV     = "Development"
	SANDBOX_ENV = "Sandbox"

	SERVER_SOFTWARE    = "HTTP Server"
	MONGO_SOFTWARE     = "MongoDB"
	ELASTIC_SOFTWARE   = "ElasticSearch"
	POSTGRES_SOFTWARE  = "PostgreSQL"
	SQL_SOFTWARE       = "SQL"
	ORACLE_SOFTWARE    = "Oracle"
	REDIS_SOFTWARE     = "Redis"
	CASSANDRA_SOFTWARE = "Cassandra"
	NEO4J_SOFTWARE     = "Neo4j"
	VPN_SOFTWARE       = "VPN"
	NAT_SOFTWARE       = "NAT"
	NGINX_SOFTWARE     = "NGINX"
	NODEJS_SOFTWARE    = "NodeJs"

	TERRAFORM_DEPLOYMENT            = "Terraform"
	HELM_DEPLOYMENT                 = "Helm"
	KUBERNETES_DEPLOYMENT           = "Kubernetes"
	CFT_DEPLOYMENT                  = "AWS CloudFormation"
	AZDO_TF_DEPLOYMENT              = "Azure DevOps (Terraform)"
	AZDO_DEPLOYMENT                 = "Azure DevOps"
	ARGO_DEPLOYMENT                 = "Argo CD"
	ARGO_KUBERNETES_DEPLOYMENT      = "Argo CD (Kubernetes)"
	CLI_DEPLOYMENT                  = "CLI"
	CONSOLE_DEPLOYMENT              = "Console"
	JENKINS_DEPLOYMENT              = "Jenkins"
	GITHUB_DEPLOYMENT               = "GitHub"
	GITLAB_DEPLOYMENT               = "GitLab"
	BITBUCKET_DEPLOYMENT            = "Bitbucket"
	DOCKER_DEPLOYMENT               = "Docker"
	AWS_DEPLOYMENT                  = "AWS Service"
	GCP_DEPLOYMENT                  = "GCP Service"
	APPLICATION_DEPLOYMENT          = "Application"
	INFRASTRUCTURE_TOOLS_DEPLOYMENT = "Infrastructure Tools"

	JENKINS_APP   = "Jenkins"
	ARGO_APP      = "Argo CD"
	TERRAFORM_APP = "Terraform"
	CICD_APP      = "CI/CD"
	GITHUB_APP    = "GitHub"
	GITLAB_APP    = "GitLab"
	BITBUCKET_APP = "Bitbucket"

	HIPAA_COMPLIANCE  = "HIPAA"
	GDPR_COMPLIANCE   = "GDPR"
	PCIDSS_COMPLIANCE = "PCI DSS"
	NIST_COMPLIANCE   = "NIST"
	SOX_COMPLIANCE    = "SOX"
	FISMA_COMPLIANCE  = "FISMA"
	ISO_COMPLIANCE    = "ISO"
	FERPA_COMPLIANCE  = "FERPA"
	GLBA_COMPLIANCE   = "GLBA"
	CJIS_COMPLIANCE   = "CJIS"

	CONFIDENTIAL_SENSITIVITY = "Confidential"
	RESTRICTED_SENSITIVITY   = "Restricted"
	PUBLIC_SENSITIVITY       = "Public"
	INTERNAL_SENSITIVITY     = "Internal"
	PII_SENSITIVITY          = "PII"
	PCI_SENSITIVITY          = "PCI"
	PHI_SENSITIVITY          = "PHI"

	AWS_SDK_JAVA_USER_AGENT           = "AWS SDK Java"
	AWS_SDK_GO_USER_AGENT             = "AWS SDK Go"
	AWS_CLI_USER_AGENT                = "AWS CLI"
	AWS_BOTO3_USER_AGENT              = "AWS Boto3"
	AWS_SERVICE_USER_AGENT            = "AWS Service"
	CLOUDFORMATION_USER_AGENT         = "AWS CloudFormation"
	KUBERNETES_DEPLOYMENT_USER_AGENT  = "Kubernetes"
	TERRAFORM_USER_AGENT              = "Terraform"
	GOLANG_APP_USER_AGENT             = "Golang App"
	JAVA_SDK_USER_AGENT               = "Java SDK"
	MOZILLA_USER_AGENT                = "Mozilla"
	CLOUDWATCH_AGENT_USER_AGENT       = "CloudWatch Agent"
	EVENT_BRIDGE_SCHEDULER_USER_AGENT = "Amazon EventBridge Scheduler"
	S3_CONSOLE_USER_AGENT             = "S3 Console"
	AWS_CONSOLE_USER_AGENT            = "AWS Console"

	IAC_USERAGENT_TYPE     = "IaC"
	NON_IAC_USERAGENT_TYPE = "Non-IaC"

	ENGINEERING_TEAM        = "Engineering"
	DEVOPS_TEAM             = "Devops"
	DEVELOPMENT_TEAM        = "Development Team"
	OPERATIONS_TEAM         = "Operations"
	PRODUCT_MANAGEMENT_TEAM = "Product Management"
	DESIGN_UXUI_TEAM        = "Design (UX/UI)"
	QA_TEAM                 = "QA"
	SECURITY_TEAM           = "Security"
	SALES_TEAM              = "Sales"
	MARKETING_TEAM          = "Marketing"
	CUSTOMER_SUPPORT_TEAM   = "Customer Support/Success"
	HR_TEAM                 = "HR"
	FINANCE_TEAM            = "Finance/Billing"
	COST_TEAM               = "Cost Team"
	DATA_TEAM               = "Data Team"
	SRE_TEAM                = "SRE"
	PLATFORM_TEAM           = "Platform Team"
	INFRA_TEAM              = "Infra Team"
	NETWORKING_TEAM         = "Networking"
	SOLUTIONS_TEAM          = "Solutions Team"
	COMPLIANCE_TEAM         = "Compliance"
	GOVERNANCE_TEAM         = "Governance"
)
