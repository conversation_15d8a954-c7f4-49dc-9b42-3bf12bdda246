package azureopenai

import (
	"bytes"
	"encoding/json"
	"fmt"

	"github.com/precize/logger"
	"github.com/precize/transport"
)

type Session struct {
	client   *Client
	Messages []ChatMessage
}

func (c *Client) NewSession() *Session {
	return &Session{
		client: c,
	}
}

func (s *Session) AddUserMessage(content string) {
	s.Messages = append(s.Messages, ChatMessage{
		Role: "user", Content: content,
	})
}

func (s *Session) AddSystemMessage(content string) {
	s.Messages = append(s.Messages, ChatMessage{
		Role: "system", Content: content,
	})
}

func (s *Session) AddAssistantMessage(content string) {
	s.Messages = append(s.Messages, ChatMessage{
		Role: "assistant", Content: content,
	})
}

func (s *Session) Send() (string, error) {
	url := fmt.Sprintf("%s/openai/deployments/%s/chat/completions?api-version=%s",
		s.client.Endpoint, s.client.Deployment, s.client.APIVersion)

	req := ChatCompletionRequest{
		Messages: s.Messages,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
		"api-key":      s.client.APIKey,
	}

	var buf bytes.Buffer
	if err := json.NewEncoder(&buf).Encode(req); err != nil {
		logger.Print(logger.ERROR, "Failed to encode chat request", err)
		return "", err
	}

	resp, err := transport.SendRequest("POST", url, nil, headers, &buf)
	if err != nil {
		return "", err
	}

	var chatResp ChatCompletionResponse
	if err := json.Unmarshal(resp, &chatResp); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal chat response", err)
		return "", err
	}

	if chatResp.Error != nil {
		err := fmt.Errorf("azure openai error: %s (%s)", chatResp.Error.Message, chatResp.Error.Type)
		logger.Print(logger.ERROR, "Chat response error", err)
		return "", err
	}

	if len(chatResp.Choices) == 0 {
		err := fmt.Errorf("no choices returned")
		logger.Print(logger.ERROR, "Chat response error", err)
		return "", err
	}

	answer := chatResp.Choices[0].Message.Content

	s.AddAssistantMessage(answer)

	return answer, nil
}
