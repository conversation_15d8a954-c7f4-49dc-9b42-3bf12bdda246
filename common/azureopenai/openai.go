package azureopenai

import (
	"encoding/base64"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/logger"
)

const (
	API_VERSION = "2025-01-01-preview"
)

type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type ChatCompletionRequest struct {
	Messages    []ChatMessage `json:"messages"`
	MaxTokens   int           `json:"max_tokens,omitempty"`
	Temperature float64       `json:"temperature,omitempty"`
	TopP        float64       `json:"top_p,omitempty"`
	Stop        []string      `json:"stop,omitempty"`
}

type ChatCompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index        int         `json:"index"`
		Message      ChatMessage `json:"message"`
		FinishReason string      `json:"finish_reason"`
	} `json:"choices"`
	Usage map[string]any `json:"usage"`
	Error *struct {
		Message string `json:"message"`
		Type    string `json:"type"`
	} `json:"error,omitempty"`
}

type Client struct {
	Endpoint   string
	Deployment string
	APIVersion string
	APIKey     string
}

func NewClient() *Client {

	if len(config.AppConfig.AzureOpenAI.GPT4oMini.APIKey) <= 0 {
		return nil
	}

	apiKey := ""
	decodedAPIKey, err := base64.StdEncoding.DecodeString(config.AppConfig.AzureOpenAI.GPT4oMini.APIKey)
	if err == nil {
		decryptedKey, err := common.DecryptTextAES(decodedAPIKey)
		if err == nil {
			apiKey = string(decryptedKey)
		} else {
			apiKey = config.AppConfig.OpenAI.APIKey
		}
	} else {
		logger.Print(logger.ERROR, "Failed to decode base64", err)
		apiKey = config.AppConfig.OpenAI.APIKey
	}

	return &Client{
		Endpoint:   config.AppConfig.AzureOpenAI.GPT4oMini.BaseUrl,
		Deployment: config.AppConfig.AzureOpenAI.GPT4oMini.DeploymentId,
		APIVersion: API_VERSION,
		APIKey:     apiKey,
	}
}
