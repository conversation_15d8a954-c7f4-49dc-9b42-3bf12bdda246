package openai

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"

	"github.com/precize/logger"
	"github.com/precize/transport"
)

type fileUploadResponse struct {
	ID        string `json:"id"`
	Object    string `json:"object"`
	Bytes     int64  `json:"bytes"`
	Purpose   string `json:"purpose"`
	Filename  string `json:"filename"`
	CreatedAt int64  `json:"created_at"`
	Status    string `json:"status"`
}

func uploadFile(apiKey, filePath string) (string, error) {

	url := baseURL + "/files"

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	if err := writer.WriteField("purpose", "assistants"); err != nil {
		logger.Print(logger.ERROR, "Got error writing purpose field", err)
		return "", err
	}

	part, err := writer.CreateFormFile("file", filepath.Base(filePath))
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating form file", err)
		return "", err
	}

	f, err := os.Open(filePath)
	if err != nil {
		logger.Print(logger.ERROR, "Got error opening file", err)
		return "", err
	}
	defer f.Close()

	if _, err := io.Copy(part, f); err != nil {
		logger.Print(logger.ERROR, "Got error copying file contents", err)
		return "", err
	}

	if err := writer.Close(); err != nil {
		logger.Print(logger.ERROR, "Got error closing multipart writer", err)
		return "", err
	}

	headers := map[string]string{
		"Authorization": "Bearer " + apiKey,
		"Content-Type":  writer.FormDataContentType(),
	}

	resp, err := transport.SendRequest("POST", url, nil, headers, &buf)
	if err != nil {
		return "", err
	}

	var fr fileUploadResponse
	if err := json.Unmarshal(resp, &fr); err != nil {
		return "", err
	}

	if fr.ID == "" {
		return "", errors.New("file upload failed: no id returned")
	}

	return fr.ID, nil
}
