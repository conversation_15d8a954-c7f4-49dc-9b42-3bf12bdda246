package secrets

import (
	"context"
	"encoding/json"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"

	"github.com/precize/logger"
)

type RDSSecret struct {
	Username string `json:"username"`
	Password string `json:"password"`
	// Host     string `json:"host"`
	// Port     int    `json:"port"`
	// DBName   string `json:"dbname"`
}

func RetrieveRDSSecret(secretName, region, accessKey, secretKey string) (*RDSSecret, error) {

	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, secretKey, "")),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Got error loading aws config", err)
		return nil, err
	}

	svc := secretsmanager.NewFromConfig(cfg)

	input := &secretsmanager.GetSecretValueInput{
		SecretId:     aws.String(secretName),
		VersionStage: aws.String("AWSCURRENT"),
	}

	result, err := svc.GetSecretValue(context.TODO(), input)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting secret", err)
		return nil, err
	}

	var rdsSecret RDSSecret

	err = json.Unmarshal([]byte(*result.SecretString), &rdsSecret)
	if err != nil {
		logger.Print(logger.ERROR, "Unmarshal failed", err)
		return nil, err
	}

	return &rdsSecret, nil
}
