package resourceutils

type CloudResource struct {
	ID                string            `json:"id"`
	TenantID          string            `json:"tenantId"`
	ServiceID         int               `json:"serviceId,omitempty"`
	AccountID         string            `json:"accountId"`
	CollectedAt       int64             `json:"collectedAt,omitempty"`
	EntityID          string            `json:"entityId"`
	EntityType        string            `json:"entityType"`
	ResourceName      string            `json:"resourceName"`
	EntityJson        string            `json:"entityJson"`
	Tags              []CRTag           `json:"tags"`
	CreatedDate       string            `json:"createdDate,omitempty"`
	Location          GeoPoint          `json:"location,omitempty"`
	TagsCount         int               `json:"tagsCount,omitempty"`
	IsTagValueEmail   bool              `json:"isTagValueEmail,omitempty"`
	GovernanceStatus  int               `json:"governanceStatus"`
	RiskScore         float64           `json:"riskScore"`
	RiskStatus        int               `json:"riskStatus"`
	Region            string            `json:"region"`
	Owner             []string          `json:"owner"`
	OwnerExistence    bool              `json:"ownerExistence,omitempty"`
	Environment       []string          `json:"environment"`
	Deployment        []string          `json:"deployment"`
	Team              []string          `json:"team"`
	Project           []string          `json:"project"`
	Purpose           []string          `json:"purpose"`
	Client            []string          `json:"client"`
	TTL               []string          `json:"ttl"`
	Software          []string          `json:"software"`
	Deleted           bool              `json:"deleted,omitempty"`
	HeroStatsCount    int64             `json:"heroStatsCount"`
	ResourceGroup     string            `json:"resourceGroup"`
	HeroStat          []string          `json:"heroStat"`
	StageCompleted    []string          `json:"stageCompleted"`
	App               []string          `json:"app"`
	MonthlyCost       float64           `json:"monthlyCost"`
	HeroStatData      []string          `json:"heroStatData"`
	Compliance        []string          `json:"compliance"`
	CostCenter        []string          `json:"costCenter"`
	Sensitivity       []string          `json:"sensitivity"`
	RelatedResources  []RelatedResource `json:"relatedResources"`
	Migrated          bool              `json:"migrated"`
	RandomID          int64             `json:"randomID,omitempty"`
	ExtContext        map[string]any    `json:"extContext"`
	OSType            string            `json:"osType"`
	Users             []string          `json:"users"`
	UserAgent         []string          `json:"userAgent"`
	DataEnrichmentIds []int             `json:"dataEnrichmentIds"`
	Source            string            `json:"source"`
}

type CRTag struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type RelatedResource struct {
	ResourceDocID string `json:"resourceDocId"`
	ResourceID    string `json:"resourceId"`
	ResourceType  string `json:"resourceType"`
}

type GeoPoint struct {
	Lat float64 `json:"lat"`
	Lon float64 `json:"lon"`
}
