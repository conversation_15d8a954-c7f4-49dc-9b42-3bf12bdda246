package common

const (
	OKTA_IDP_TYPE = "okta"
)

type IDPEventsDoc struct {
	EventID             string           `json:"eventId"`
	EventType           string           `json:"eventType"`
	EventDisplayMessage string           `json:"eventDisplayMessage"`
	ActorID             string           `json:"actorId"`
	ActorType           string           `json:"actorType"`
	PublishedTime       string           `json:"publishedTime"`
	ClientID            string           `json:"clientId"`
	Targets             []IDPEventTarget `json:"targets"`
	SourceIP            string           `json:"sourceIp"`
	IDPType             string           `json:"idpType"`
	TenantID            string           `json:"tenantId"`
	Domain              string           `json:"domain"`
}

type IDPEventTarget struct {
	TargetID   string `json:"targetId"`
	TargetType string `json:"targetType"`
}

type IDPUsersDoc struct {
	UserID          string         `json:"userId"`
	Email           string         `json:"email"`
	SecondaryEmail  string         `json:"secondaryEmail"`
	Name            string         `json:"name"`
	Title           string         `json:"title"`
	ManagerID       string         `json:"managerId"`
	ManagerName     string         `json:"managerName"`
	CreatedTime     string         `json:"createdTime"`
	LastUpdatedTime string         `json:"lastUpdatedTime"`
	IDPType         string         `json:"idpType"`
	UserType        string         `json:"userType"`
	Organization    string         `json:"organization"`
	Department      string         `json:"department"`
	Division        string         `json:"division"`
	Status          string         `json:"status"`
	Deleted         bool           `json:"deleted"`
	Apps            []IDPUserApp   `json:"userApps"`
	Groups          []IDPUserGroup `json:"userGroups"`
	Tenantid        string         `json:"tenantId"`
	Domain          string         `json:"domain"`
	InsertTime      string         `json:"insertTime"`
	LastLoginTime   string         `json:"lastLoginTime,omitempty"`
}

type IDPUserApp struct {
	AppID             string `json:"appId"`
	AppName           string `json:"appName"`
	AppLabel          string `json:"appLabel"`
	AppUsername       string `json:"appUsername"`
	AppUserAdditional string `json:"appUserAdditional"`
	LastUpdated       string `json:"lastUpdated"`

	Active bool `json:"active"`
}

type IDPUserGroup struct {
	GroupID     string `json:"groupId"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description"`
	LastUpdated string `json:"lastUpdated"`

	Active bool `json:"active"`
}

type IDPAppsDoc struct {
	AppID           string `json:"appId"`
	Label           string `json:"label"`
	Name            string `json:"name"`
	CreatedTime     string `json:"createdTime"`
	LastUpdatedTime string `json:"lastUpdatedTime"`
	IDPType         string `json:"idpType"`
	SignOnMode      string `json:"signOnMode"`
	Status          string `json:"status"`
	Settings        string `json:"settings"`
	Deleted         bool   `json:"deleted"`
	TenantID        string `json:"tenantId"`
	Domain          string `json:"domain"`
	InsertTime      string `json:"insertTime"`
}

type IDPGroupsDoc struct {
	GroupID               string `json:"groupId"`
	Name                  string `json:"name"`
	Description           string `json:"description"`
	Type                  string `json:"type"`
	CreatedTime           string `json:"createdTime"`
	LastUpdatedTime       string `json:"lastUpdatedTime"`
	LastMembershipUpdated string `json:"lastMembershipUpdated"`
	IDPType               string `json:"idpType"`
	Deleted               bool   `json:"deleted"`
	TenantID              string `json:"tenantId"`
	Domain                string `json:"domain"`
	InsertTime            string `json:"insertTime"`
}
