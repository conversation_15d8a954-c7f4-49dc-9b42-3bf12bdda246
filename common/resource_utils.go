package common

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type CloudResourceStoreDoc struct {
	AccountName      string            `json:"accountName"`
	IsDeleted        bool              `json:"isDeleted"`
	ID               string            `json:"id"`
	ServiceID        []int             `json:"serviceId"`
	Owner            []string          `json:"owner"`
	EntityType       string            `json:"entityType"`
	EntityID         string            `json:"entityId"`
	ResourceName     string            `json:"resourceName"`
	AccountID        string            `json:"accountId"`
	Environment      []string          `json:"environment"`
	Herostat         []string          `json:"heroStat"`
	Compliance       []string          `json:"compliance"`
	Sensitivity      []string          `json:"sensitivity"`
	TenantID         string            `json:"tenantId"`
	ResourceGroup    string            `json:"resourceGroup"`
	PriorityMapAsStr string            `json:"priorityMapAsStr"`
	RelatedResources []RelatedResource `json:"relatedResources"`
	//Purpose          []string `json:"purpose"`
	//IsTagValueEmail  bool     `json:"isTagValueEmail"`
	//Project          []string `json:"project"`
	//GovernanceStatus int      `json:"governanceStatus"`
	//Client           []string `json:"client"`
	//RiskScore        float64  `json:"riskScore"`
	//ResourceIDs      []string `json:"resourceIds"`
	//Deployment     []string `json:"deployment"`
	//App            []string `json:"app"`
	//TagsCount      int      `json:"tagsCount"`
	//CollectedAt    string   `json:"collectedAt"`
	//HerostatsCount int      `json:"heroStatsCount"`
	//OwnerHash      []string `json:"ownerHash"`
	//CostCenter     []string `json:"costCenter"`
	//FirstOwner     string   `json:"firstOwner"`
	//Team           []string `json:"team"`
	//Ttl            []string `json:"ttl"`
	//Tags           []struct {
	// 	Key   string `json:"key"`
	// 	Value string `json:"value"`
	// } `json:"tags"`
	//CreatedDate string   `json:"createdDate"`
	//Deleted     string   `json:"deleted"`
	//RiskStatus  int      `json:"riskStatus"`
	// Sensitivity []string `json:"sensitivity"`
	// Region      string   `json:"region"`
	// MonthlyCost float64  `json:"monthlyCost"`
}

type RelatedResource struct {
	ResourceDocID string `json:"resourceDocId"`
	ResourceID    string `json:"resourceId"`
	ResourceType  string `json:"resourceType"`
}

func GetCRSDocFromNames(resourceName, resourceType, accountName, tenantID string) (crsDoc CloudResourceStoreDoc, err error) {

	resourceName = strings.ToLower(resourceName)

	query := `
	{
		"query": {
		  "bool": {
			"must": [
			  {
				"term": {
				  "entityType.keyword": "` + resourceType + `"
				}
			  },
			  {
				"term": {
				  "tenantId.keyword": "` + tenantID + `"
				}
			  },
			  {
				"wildcard": {
				  "accountName.keyword": "*` + accountName + `"
				}
			  },
			  {
				"wildcard": {
				  "entityId.keyword": "*` + resourceName + `"
				}
			  }
			],
			"must_not": [],
			"should": []
		  }
		},
		"size": 1
	  }
	  `

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource store", []string{tenantID}, err)
		return
	}

	for _, doc := range docs {

		b, err := json.Marshal(doc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		if err = json.Unmarshal(b, &crsDoc); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			continue
		}
	}

	return
}

func GetCRSDocFromResourceID(resourceID, accountID, tenantID string) (crsDoc CloudResourceStoreDoc, err error) {

	resourceID = strings.ToLower(resourceID)

	query := `
	{
		"query": {
		  "bool": {
			"must": [
			  {
				"term": {
				  "tenantId.keyword": "` + tenantID + `"
				}
			  },
			  {
				"term": {
				  "accountId.keyword": "` + accountID + `"
				}
			  },
			  {
				"term": {
				  "entityId.keyword": "` + resourceID + `"
				}
			  }
			],
			"must_not": [],
			"should": []
		  }
		},
		"size": 1
	  }
	  `

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource store", []string{tenantID}, err)
		return
	}

	for _, doc := range docs {

		b, err := json.Marshal(doc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		if err = json.Unmarshal(b, &crsDoc); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			continue
		}
	}

	return
}

// Function not used today
// func fetchAccountIDFromAccountName(accountName, csp, tenantID string) (string, error) {

// 	entityType := ""
// 	switch csp {
// 	case "aws":
// 		entityType = AWS_ACCOUNT_RESOURCE_TYPE
// 	case "gcp":
// 		entityType = GCP_PROJECT_RESOURCE_TYPE
// 	case "azure":
// 		entityType = AZURE_SUBSCRIPTION_RESOURCE_TYPE
// 	}

// 	query := `
// 	{
// 		"_source": "entityId",
// 		"query": {
// 		  "bool": {
// 			"must": [
// 			  {
// 				"term": {
// 				  "resourceName.keyword": "` + accountName + `"
// 				}
// 			  },
// 			  {
// 				"match": {
// 				  "entityType.keyword": "` + entityType + `"
// 				}
// 			  },
// 			  {
// 				"match": {
// 				  "tenantId.keyword": "` + tenantID + `"
// 				}
// 			  }
// 			],
// 			"must_not": [],
// 			"should": []
// 		  }
// 		},
// 		"size": 1
// 	  }
// 	`

// 	resourceDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, query)
// 	if err != nil {
// 		logger.Print(logger.ERROR, "Error while fetching from cloud resource store", []string{tenantID}, err)
// 		return "", err
// 	}

// 	for _, resourceDoc := range resourceDocs {

// 		if accountID, ok := resourceDoc["entityId"].(string); ok {
// 			return accountID, nil
// 		}
// 	}

// 	return "", nil
// }

func GetCloudResourceDocumentForEntityIDAndType(entityID, entityType, tenantID, collectedAt string) (map[string]any, error) {

	query := `
	{
		"query": {
		  "bool": {
			"must": [
			  {
				"term": {
				  "tenantId.keyword": "` + tenantID + `"
				}
			  },
			  {
				"term": {
				  "entityId.keyword": "` + entityID + `"
				}
			  },
			  {
				"term": {
				  "entityType.keyword": "` + entityType + `"
				}
			  },
			  {
				"term": {
				  "collectedAt": ` + collectedAt + `
				}
			  }
			],
			"must_not": [],
			"should": []
		  }
		},
		"size": 1
	  }
	  `

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCES_INDEX}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource", []string{tenantID}, err)
		return nil, err
	}

	for _, doc := range docs {
		return doc, nil
	}

	return nil, nil
}

func GetCloudResourceDocumentFromEntityAndJSON(baseEntityID, entityType, entityJSONMatch, tenantID, collectedAt string) ([]map[string]any, error) {

	query := `
	{
		"query": {
		  "bool": {
			"must": [
			  {
				"term": {
				  "tenantId.keyword": "` + tenantID + `"
				}
			  },
			  {
				"wildcard": {
				  "entityId.keyword": "` + baseEntityID + `*"
				}
			  },
			  {
				"term": {
				  "entityType.keyword": "` + entityType + `"
				}
			  },
			  {
				"match": {
				  "entityJson": "` + entityJSONMatch + `"
				}
			  },
			  {
				"term": {
				  "collectedAt": ` + collectedAt + `
				}
			  }
			],
			"must_not": [],
			"should": []
		  }
		}
	  }
	  `

	docs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource", []string{tenantID}, err)
		return nil, err
	}

	return docs, nil
}

func IsVirtualResource(resourceType string) bool {

	switch resourceType {
	case AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE, AZURE_OPENAICONTENTFILTERBL_RESOURCE_TYPE,
		AZURE_LOCATION_RESOURCE_TYPE, AZURE_ASSIGNEDROLE_RESOURCE_TYPE,
		AZURE_ROLEASSIGNMENT_RESOURCE_TYPE, AZURE_GROUPS_RESOURCE_TYPE,
		AZURE_POLICYSTATE_RESOURCE_TYPE, AZURE_POLICYDEFINITION_RESOURCE_TYPE,
		AWS_REGION_RESOURCE_TYPE, GCP_REGION_RESOURCE_TYPE,
		GCP_CONSTRAINT_RESOURCE_TYPE:
		return true
	}

	return false
}

func GetCRSDocFromResourceIDAndName(resourceIDFullPath, resourceID, resourceName, tenantID, serviceID string) (crsDoc CloudResourceStoreDoc, err error) {

	resourceID = strings.ToLower(resourceID)
	resourceIDFullPath = strings.ToLower(resourceIDFullPath)
	if resourceName == "default" {

		parts := strings.Split(resourceIDFullPath, "/")
		parts[len(parts)-1] = resourceName

		resourceIDFullPath = strings.Join(parts, "/")
		resourceName = ""
	}

	query := `
	{
		"query":
		{
			"bool":
			{
				"must":
				[
					{
						"match":
						{
							"tenantId.keyword": "` + tenantID + `"
						}
					},
					{
						"match": {
							"serviceId": ` + serviceID + `
						}
					},
					{
						"term": {
							"isDeleted": false
						}
					}
				],
				"should":
				[
					{
						"match":
						{
							"entityId.keyword": "` + resourceIDFullPath + `"
						}
					},
					{
						"match":
						{
							"resourceName": "` + resourceName + `"
						}
					}
				],
				"minimum_should_match": 1
			}
		},
		"size": 1
	}
	`

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource store", []string{tenantID}, err)
		return
	}

	for _, doc := range docs {

		b, err := json.Marshal(doc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		if err = json.Unmarshal(b, &crsDoc); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			continue
		}
	}

	return
}

func OrgOrAccountOnboarded(tenantID, serviceID string) (orgID string, isOrgOnboarded bool) {

	orgQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"entityType.keyword":"Organization"}},{"match":{"serviceId":"` + serviceID + `"}},{"match":{"isDeleted":"false"}}]}},"size":"1","from":0}`
	if orgDocs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, orgQuery); err == nil && len(orgDocs) > 0 {
		if ogID, ok := orgDocs[0]["entityId"].(string); ok {
			orgID = ogID
			isOrgOnboarded = true
		}
	}

	return
}

func ParseECRArn(arn string) (repo, account, region string, err error) {

	pattern := `^arn:aws:ecr:([^:]+):(\d+):repository/(.+)$`
	re := regexp.MustCompile(pattern)

	matches := re.FindStringSubmatch(arn)
	if len(matches) != 4 {
		return "", "", "", fmt.Errorf("invalid ECR ARN format")
	}

	return matches[3], matches[2], matches[1], nil
}

func FetchECRImageDocFromArnUtils(imageRepoCombination, accountID, region, tenantID, collectedAt string) (imageDoc map[string]any, err error) {

	imageQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + accountID + `"}},{"match":{"region.keyword":"` + region + `"}},{"match":{"entityJson":"` + imageRepoCombination + `"}},{"term":{"collectedAt":` + collectedAt + `}},{"match":{"entityType.keyword":"ContainerImage"}}]}},"size":"1"}`
	imageDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCES_INDEX}, imageQuery)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource", []string{tenantID}, err)
		return nil, err
	}

	for _, doc := range imageDocs {
		return doc, nil
	}

	return nil, nil
}

func GetAccountIDAndRegionFromARN(arn string) (accountID, region string, err error) {

	if !strings.HasPrefix(arn, "arn:") {
		return "", "", errors.New("invalid ARN format: must start with 'arn:'")
	}

	parts := strings.Split(arn, ":")
	if len(parts) < 6 {
		return "", "", errors.New("invalid ARN format: insufficient parts")
	}

	return parts[4], parts[3], nil
}

func GetCloudResourceDocumentFromListofEntIds(entityIDs []string, entityType, tenantID, collectedAt string) ([]map[string]any, error) {

	query := `
	{
		"query": {
		  "bool": {
			"must": [
			  {
				"term": {
				  "tenantId.keyword": "` + tenantID + `"
				}
			  },
			  {
				"terms": {
				  "entityId.keyword": ["` + strings.Join(entityIDs, `","`) + `"]
				}
			  },
			  {
				"term": {
				  "entityType.keyword": "` + entityType + `"
				}
			  },
			  {
				"term": {
				  "collectedAt": ` + collectedAt + `
				}
			  }
			],
			"must_not": [],
			"should": []
		  }
		}
	  }
	  `

	docs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource", []string{tenantID}, err)
		return nil, err
	}

	return docs, nil
}

func ConstructECRArn(imageURI string) (string, error) {

	parts := strings.Split(imageURI, "/")
	if len(parts) < 2 {
		return "", fmt.Errorf("invalid ECR image URI: %s", imageURI)
	}

	hostParts := strings.Split(parts[0], ".")
	if len(hostParts) < 6 {
		return "", fmt.Errorf("invalid ECR host: %s", parts[0])
	}
	accountID := hostParts[0]
	region := hostParts[3]

	repoWithTag := parts[1]
	repo := strings.Split(repoWithTag, ":")[0]

	arn := fmt.Sprintf("arn:aws:ecr:%s:%s:repository/%s", region, accountID, repo)
	return arn, nil
}

func GetCloudResourceDocumentFromEntityJSONMatchPhrase(destEntityType, entityJSONMatch, tenantID, collectedAt string) ([]map[string]any, error) {

	query := `
	{
		"query": {
		  "bool": {
			"must": [
			  {
				"term": {
				  "tenantId.keyword": "` + tenantID + `"
				}
			  },
			  {
				"term": {
				  "entityType.keyword": "` + destEntityType + `"
				}
			  },
			  {
				"match_phrase": {
				  "entityJson": "` + entityJSONMatch + `"
				}
			  },
			  {
				"term": {
				  "collectedAt": ` + collectedAt + `
				}
			  }
			],
			"must_not": [],
			"should": []
		  }
		},
		"size":1
	  }
	  `

	docs, err := elastic.ExecuteSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, query)
	if err != nil {
		logger.Print(logger.ERROR, "Error while fetching from cloud resource", []string{tenantID}, err)
		return nil, err
	}

	return docs, nil
}
