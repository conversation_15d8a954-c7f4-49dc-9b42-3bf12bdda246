package common

import (
	"strconv"

	"github.com/precize/elastic"
)

func GetServiceID(tenantID, lastCollectedAt string) (serviceID string) {

	serviceIDQuery := `{"_source":["serviceId"],"query":{"bool":{"filter":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"term":{"collectedAt":` + lastCollectedAt + `}}]}},"size":1}`

	serviceIDDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.CLOUD_RESOURCES_INDEX}, serviceIDQuery)
	if err != nil {
		return
	}

	for _, serviceIDDoc := range serviceIDDocs {

		if serviceIDFloat, ok := serviceIDDoc["serviceId"].(float64); ok {
			serviceID = strconv.FormatFloat(serviceIDFloat, 'f', 0, 64)
			return
		}
	}

	return
}
