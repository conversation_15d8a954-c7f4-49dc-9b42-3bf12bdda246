package components

import (
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func DeployService(service types.Service, processSearchKeyword, artifactsDir string) error {

	logger.Print(logger.INFO, "Downloading artifact", []string{service.Name})

	newArtifactPath := artifactsDir + "/" + service.ProcessName

	_, err := exec.Command("sh", "-c", "scp "+service.ArtifactDownloadPath+" "+newArtifactPath).CombinedOutput()
	if err != nil {
		logger.Print(logger.ERROR, "Failed to download service artifact", []string{service.Name}, err)
		return err
	}

	existingPidBytes, err := exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
	if err != nil {
		logger.Print(logger.ERROR, "Service pid command failed", []string{service.Name}, err)
		return err
	}

	if len(existingPidBytes) > 0 {

		logger.Print(logger.INFO, "Stopping existing process", []string{service.Name})

		if _, err = exec.Command("sh", "-c", "kill -9 $(ps -ef | grep '"+processSearchKeyword+
			"' | grep -v 'grep' | awk '{print $2}')").CombinedOutput(); err != nil {
			logger.Print(logger.ERROR, "Failed to stop service", []string{service.Name}, err)
			return err
		}
	}

	if _, err = os.Stat(service.ProcessPath); err == nil {

		logger.Print(logger.INFO, "Backing up old artifact", []string{service.Name})

		backupPath := strings.TrimSuffix(service.ProcessPath, filepath.Ext(service.ProcessPath)) +
			"_bkup" + filepath.Ext(service.ProcessPath)

		if err = os.Rename(service.ProcessPath, backupPath); err != nil {
			logger.Print(logger.ERROR, "Failed to backup old artifact", []string{service.Name}, err)
		}

		logger.Print(logger.INFO, "Replacing old artifact", []string{service.Name})

		if err = os.Rename(newArtifactPath, service.ProcessPath); err != nil {
			logger.Print(logger.ERROR, "Failed to replace artifact", []string{service.Name}, err)
			return err
		}
	} else {
		logger.Print(logger.INFO, "Old artifact not found. Initializing new artifact", []string{service.Name})

		if err = os.Rename(newArtifactPath, service.ProcessPath); err != nil {
			logger.Print(logger.ERROR, "Failed to initialize artifact", []string{service.Name}, err)
			return err
		}
	}

	return nil
}
