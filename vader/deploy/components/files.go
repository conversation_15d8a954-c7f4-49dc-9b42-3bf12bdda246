package components

import (
	"os/exec"

	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func DownloadConfigFiles(conf types.VaderConfig, serviceFiles []string, serviceName string, downloadedFiles map[string]struct{}) error {

	for _, serviceConfigFile := range serviceFiles {
		// Check if already downloaded
		if _, ok := downloadedFiles[serviceConfigFile]; !ok {
			if file, ok := conf.Files[serviceConfigFile]; ok {
				if _, err := exec.Command("sh", "-c", "scp "+file.DownloadPath+" "+file.FilePath).CombinedOutput(); err != nil {
					logger.Print(logger.ERROR, "Failed to download config file", []string{serviceName}, serviceConfigFile, err)
					return err
				}
				downloadedFiles[serviceConfigFile] = struct{}{}
			}
		}
	}

	return nil
}
