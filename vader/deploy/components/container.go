package components

import (
	"os/exec"
	"strings"

	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func DeployContainerService(containerService types.ContainerService) (string, error) {

	var oldImageID string

	if len(containerService.Account) > 0 {

		out, err := exec.Command("sh", "-c", "sudo aws ecr get-login-password --region "+containerService.Region+
			" | sudo docker login --username AWS --password-stdin "+containerService.Account+".dkr.ecr."+containerService.Region+".amazonaws.com").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "ECR login failed", err, string(out))
			return "", err
		}
	}

	oldImageIDBytes, err := exec.Command("sh", "-c",
		"sudo docker images -q "+containerService.Image).Output()
	if err != nil {
		logger.Print(logger.ERROR, "Docker pull failed", err, string(oldImageIDBytes))
		return "", err
	}

	if len(oldImageIDBytes) > 0 {
		oldImageID = strings.TrimSpace(string(oldImageIDBytes))
		logger.Print(logger.INFO, "Got old image id", []string{containerService.Name}, oldImageID)
	}

	logger.Print(logger.INFO, "Pulling container image", []string{containerService.Name}, containerService.Image)

	if out, err := exec.Command("sh", "-c", "sudo docker pull "+containerService.Image).CombinedOutput(); err != nil {
		logger.Print(logger.ERROR, "Docker pull failed", err, string(out))
		return "", err
	}

	logger.Print(logger.INFO, "Pulled container image", []string{containerService.Name})

	return oldImageID, nil
}
