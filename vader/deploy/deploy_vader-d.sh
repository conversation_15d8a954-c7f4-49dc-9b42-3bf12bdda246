#!/bin/bash

CONFIG=false
ENVS=("qa")

VALID_ENVS=("qa" "preprod" "prod")

for arg in "$@"; do
  case $arg in
    --config=*)
      CONFIG="${arg#*=}"
      shift
      ;;
    --env=*)
      IFS=',' read -r -a ENVS <<< "${arg#*=}"
      # Validate environments
      for e in "${ENVS[@]}"; do
        if [[ ! " ${VALID_ENVS[*]} " =~ " ${e} " ]]; then
          echo "Invalid environment: ${e}"
          echo "Allowed values: qa, preprod, prod"
          exit 1
        fi
      done
      shift
      ;;
    *)
      echo "Unknown argument: $arg"
      echo "Usage: $0 [--config=true|false] [--env=qa,preprod,prod]"
      exit 1
      ;;
  esac
done

MACHINES=(scan activity provider dashboard vector)

LOCAL_BINARY=./vader-d

REMOTE_BINARY_PATH=/app/precize-vader/vader-d
REMOTE_CONFIG_PATH=/app/precize-vader/vader.json

# Function to check if vader-m is running remotely
check_vader_m_running() {
  local host=$1
  ssh -o BatchMode=yes -o ConnectTimeout=5 "${host}" "pgrep -x vader-m >/dev/null 2>&1"
}

for env in "${ENVS[@]}"; do
  for machine in "${MACHINES[@]}"; do
    host="${env}-${machine}"
    config_path="${env}/vader_${machine}.json"

    echo "Processing ${host} ..."

    # Copy binary
    if scp "${LOCAL_BINARY}" "${host}:${REMOTE_BINARY_PATH}"; then
      echo "  Binary copied to ${host}"
    else
      echo "  Failed to copy binary to ${host}"
      continue
    fi

    # Copy config if enabled
    if [[ "${CONFIG}" == "true" ]]; then
      if [[ ! -f "${config_path}" ]]; then
        echo "  Missing config file: ${config_path}, skipping."
        continue
      fi

      attempt=1
      while check_vader_m_running "${host}"; do
        if [[ $attempt -gt 5 ]]; then
          echo "  vader-m still running on ${host} after 5 retries, skipping config copy."
          continue 2  # skip to next host
        fi
        echo "  vader-m running on ${host}, waiting 5s (attempt $attempt)..."
        sleep 5
        ((attempt++))
      done

      if scp "${config_path}" "${host}:${REMOTE_CONFIG_PATH}"; then
        echo "  Config copied to ${host}"
      else
        echo "  Failed to copy config to ${host}"
      fi
    fi
  done
done

echo "Deployment of vader-d complete"
