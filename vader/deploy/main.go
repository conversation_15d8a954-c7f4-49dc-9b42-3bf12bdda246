package main

import (
	"encoding/json"
	"flag"
	"os"
	"os/exec"
	"path/filepath"
	"slices"
	"strings"

	"github.com/precize/logger"
	"github.com/precize/vader/deploy/components"
	"github.com/precize/vader/run"
	"github.com/precize/vader/types"
)

func main() {

	logger.InitializeLogs("vader-d", false)
	logger.Print(logger.INFO, "-------------------------")
	logger.Print(logger.INFO, "Starting Vader Deployment")
	logger.Print(logger.INFO, "-------------------------")

	var (
		appConfigPath          = flag.String("config", "vader.json", "Path to vader.json")
		user                   = flag.String("user", "", "User triggering the deployment")
		bootup                 = flag.Bool("bootup", false, "If machine boot-up, set true")
		bounce                 = flag.Bool("bounce", false, "If no deployment required, only restart")
		deployAllServices      = flag.Bool("all-services", false, "To deploy all services")
		deployProviderServices = flag.Bool("provider-services", false, "To deploy all provider services")
		deployServices         = flag.String("services", "", "Service(s) to deploy")
	)

	flag.Parse()

	if len(*user) <= 0 {
		logger.Print(logger.INFO, "Flag 'user' has to be specified")
		os.Exit(1)
	} else {
		logger.Print(logger.INFO, "Deployment triggered by", *user)

		servicesLog := "Deployment triggered for "

		if *bounce {
			servicesLog = "Bounce triggered for "
		}

		if *deployAllServices {
			servicesLog += "all services"
		} else {

			var and string

			if *deployProviderServices {
				servicesLog += "all provider services "
				and = "and "
			}

			if len(*deployServices) > 0 {
				servicesLog += and + "services - " + *deployServices
			}
		}

		logger.Print(logger.INFO, servicesLog)
	}

	if *bootup && *bounce {
		logger.Print(logger.INFO, "Bootup and Bounce cannot work together")
		os.Exit(1)
	}

	configFile := *appConfigPath

	dat, err := os.ReadFile(configFile)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read file", err)
		return
	}

	var conf types.VaderConfig

	if err = json.Unmarshal(dat, &conf); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", err)
		return
	}

	services := strings.Split(*deployServices, ",")

	ex, _ := os.Executable()
	exPath := filepath.Dir(ex)
	artifactsDir := exPath + "/artifacts"
	if err = os.MkdirAll(artifactsDir, os.ModePerm); err != nil {
		logger.Print(logger.ERROR, "Failed to make artifacts directory", err)
		return
	}

	var downloadedFiles = make(map[string]struct{})

	for k, service := range conf.Services {

		if slices.Contains(services, k) || *deployAllServices || (*deployProviderServices && service.Type == "provider") {

			processSearchKeyword := service.ProcessPath
			if k == "platform" {
				processSearchKeyword = service.ProcessName
			}

			if *bounce {

				logger.Print(logger.INFO, "Bouncing service", []string{service.Name})

				if err = components.BounceService(service, processSearchKeyword); err != nil {
					continue
				}

				logger.Print(logger.INFO, "Bounced service", []string{service.Name})

			} else {

				if len(service.ArtifactDownloadPath) > 0 {

					logger.Print(logger.INFO, "Deploying service", []string{service.Name})

					if err = components.DeployService(service, processSearchKeyword, artifactsDir); err != nil {
						continue
					}

					if err = components.DownloadConfigFiles(conf, service.ConfigFiles, service.Name, downloadedFiles); err != nil {
						continue
					}

					if len(service.Command) > 0 && !*bootup {
						if err = run.RunService(service, processSearchKeyword); err != nil {
							continue
						}
					}

					logger.Print(logger.INFO, "Service deployment completed", []string{service.Name})
				} else {
					logger.Print(logger.INFO, "Service deployment not supported", []string{service.Name})
				}
			}
		}
	}

	for k, containerService := range conf.ContainerServices {

		if slices.Contains(services, k) || *deployAllServices {

			logger.Print(logger.INFO, "Deploying service", []string{containerService.Name})

			oldImageID, err := components.DeployContainerService(containerService)
			if err != nil {
				continue
			}

			if err = components.DownloadConfigFiles(conf, containerService.ConfigFiles, containerService.Name, downloadedFiles); err != nil {
				continue
			}

			if containerService.Run && !*bootup {
				if err = run.RunContainerService(containerService, oldImageID); err != nil {
					continue
				}
			} else {
				out, err := exec.Command("sh", "-c",
					"sudo docker images -f \"dangling=true\" -q | grep "+oldImageID+" | xargs -r sudo docker rmi -f").CombinedOutput()
				if err != nil {
					logger.Print(logger.ERROR, "Failed to remove old images", err, string(out))
					continue
				}

				logger.Print(logger.INFO, "Removed older images of the repo", []string{containerService.Name})
			}

			logger.Print(logger.INFO, "Service deployment completed", []string{containerService.Name})
		}
	}

	logger.Print(logger.INFO, "Vader deployment completed")
}
