package run

import (
	"os/exec"
	"time"

	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func RunService(service types.Service, processSearchKeyword string) error {

	logger.Print(logger.INFO, "Starting process", []string{service.Name})

	exec.Command("sh", "-c", service.Command).Run()
	time.Sleep(2 * time.Second)

	pidBytes, err := exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+
		"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
	if err != nil {
		logger.Print(logger.ERROR, "Service restart pid command failed", []string{service.Name}, err)
		return err
	}

	if len(pidBytes) <= 0 {
		logger.Print(logger.ERROR, "Process start command failed", []string{service.Name}, err)
		return err
	}

	logger.Print(logger.INFO, "Started process", []string{service.Name})

	return nil
}
