package run

import (
	"fmt"
	"os/exec"

	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func RunContainerService(containerService types.ContainerService, oldImageID string) error {

	if len(oldImageID) > 0 {

		out, err := exec.Command("sh", "-c",
			"sudo docker ps -q --filter 'ancestor="+oldImage<PERSON>+"' | xargs -r sudo docker stop").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Failed to stop existing containers", err, string(out))
			return err
		}

		logger.Print(logger.INFO, "Stopped existing containers", []string{containerService.Name})

		out, err = exec.Command("sh", "-c",
			"sudo docker ps -a -q --filter 'ancestor="+oldImageID+"' | xargs -r sudo docker rm").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Failed to remove old containers", err, string(out))
			return err
		}

		out, err = exec.Command("sh", "-c",
			"sudo docker images -f \"dangling=true\" -q | grep "+oldImageID+" | xargs -r sudo docker rmi -f").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Failed to remove old images", err, string(out))
			return err
		}

		logger.Print(logger.INFO, "Removed older containers and images of the repo", []string{containerService.Name})
	}

	for i := 0; i < containerService.ContainerCount; i++ {

		containerName := containerService.ContainerName

		if containerService.ContainerCount > 1 {
			containerName = fmt.Sprintf("%s-%d", containerService.ContainerName, i+1)
		}

		out, err := exec.Command("sh", "-c", fmt.Sprintf("sudo docker run -d --name %s %s %s %s", containerName, containerService.ContainerArgs, containerService.Image, containerService.CommandArgs)).CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Failed to start container", containerName, err, string(out))
			return err
		}

		logger.Print(logger.INFO, "Started new container", []string{containerService.Name}, containerName)
	}

	return nil
}
