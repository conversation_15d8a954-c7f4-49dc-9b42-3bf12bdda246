{"machine": "provider", "prod": true, "devicePaths": ["/", "/app"], "services": {"provider": {"name": "Provider", "processPath": "/app/precize-provider/precize-provider", "processName": "precize-provider", "type": "provider", "command": "/app/precize-provider/precize-provider --config /app/precize-provider/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-provider", "configFiles": ["application.yml"], "monitor": true, "autoRestart": true, "restartFailed": false}, "pserver": {"name": "Provider Server", "processPath": "/app/precize-pserver/precize-pserver", "processName": "precize-pserver", "type": "provider", "command": "/app/precize-pserver/precize-pserver --config /app/precize-provider/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-pserver", "configFiles": ["application.yml"], "monitor": true, "autoRestart": true, "restartFailed": false}, "analyzer": {"name": "<PERSON><PERSON><PERSON>", "processPath": "/app/precize-analyzer/precize-analyzer", "processName": "precize-analyzer", "type": "provider", "command": "/app/precize-analyzer/precize-analyzer --config /app/precize-provider/application.yml &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-analyzer", "configFiles": ["application.yml"], "monitor": true, "autoRestart": true, "restartFailed": false}, "fetch8k": {"name": "Fetch 8k", "processPath": "/app/fetch8k/precize-fetch8k", "processName": "precize-fetch8k", "type": "other", "command": "/app/fetch8k/precize-fetch8k >> /app/fetch8k/fetch8k.log 2>&1 &", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-fetch8k", "monitor": true, "autoRestart": true, "restartFailed": false}, "vader-m": {"name": "Vader Monitor", "processPath": "/app/precize-vader/vader-m", "processName": "vader-m", "type": "other", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-m", "monitor": false}}, "containerServices": {"ai-agent": {"name": "AI Agent", "image": "************.dkr.ecr.ap-south-1.amazonaws.com/precize-ai-agent:latest-prod", "account": "************", "region": "ap-south-1", "containerName": "precize-ai-agent", "containerArgs": "-p 17070:8000 --env-file $HOME/.chatbot_env --platform linux/amd64", "commandArgs": "", "containerCount": 1, "monitor": true, "run": true, "autoRestart": true}}, "files": {"application.yml": {"downloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-platform/_work/precize-platform/precize-platform/config/prod/application-provider.yml", "filePath": "/app/precize-provider/application.yml"}}}