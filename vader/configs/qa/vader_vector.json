{
    "machine": "vector",
    "prod": false,
    "devicePaths":
    [
        "/",
        "/app"
    ],
    "services":
    {
        "vader-m":
        {
            "name": "Vader Monitor",
            "processPath": "/app/precize-vader/vader-m",
            "processName": "vader-m",
            "type": "other",
            "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-m",
            "monitor": false
        }
    },
    "containerServices":
    {
        "weaviate":
        {
            "name": "Weaviate Vector DB",
            "image": "cr.weaviate.io/semitechnologies/weaviate:1.33.4",
            "containerName": "weaviate",
            // "containerArgs": "-p 18001:18001 -p 50051:50051 -v /app/weaviate_data:/var/lib/weaviate -e QUERY_DEFAULTS_LIMIT=25 -e AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true -e PERSISTENCE_DATA_PATH=/var/lib/weaviate -e ENABLE_API_BASED_MODULES=true -e ENABLE_MODULES=text2vec-ollama,generative-ollama,text2vec-openai -e CLUSTER_HOSTNAME=node1 -e MODULES_CLIENT_TIMEOUT=2m",
            // "commandArgs": "--host 0.0.0.0 --port 18001 --scheme http",
            "containerCount": 1,
            "monitor": true,
            "emailOverride": true
        }
    }
}