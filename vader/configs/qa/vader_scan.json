{"machine": "scan", "prod": false, "devicePaths": ["/", "/app"], "services": {"platform": {"name": "Platform", "processPath": "/app/precize-server/precize-scan-0.0.1-SNAPSHOT.jar", "processName": "precize-scan-0.0.1-SNAPSHOT.jar", "type": "platform", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-platform/_work/precize-platform/precize-platform/servers/scan/target/precize-scan-0.0.1-SNAPSHOT.jar", "configFiles": ["application.yml"], "monitor": true, "autoRestart": false}, "enhancer": {"name": "<PERSON><PERSON><PERSON>", "processPath": "/app/precize-enhancer/precize-enhancer", "processName": "precize-enhancer", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-enhancer", "configFiles": ["application.yml"], "monitor": false}, "prioritiser": {"name": "Prioritiser", "processPath": "/app/precize-prioritiser/precize-prioritiser", "processName": "precize-prioritiser", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-prioritiser", "configFiles": ["application.yml", "issue_risk_details.json"], "monitor": false}, "externaldc": {"name": "External DataCollector", "processPath": "/app/precize-externaldc/precize-externaldc", "processName": "precize-externaldc", "type": "provider", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/precize-externaldc", "configFiles": ["application.yml"], "monitor": false}, "vader-m": {"name": "Vader Monitor", "processPath": "/app/precize-vader/vader-m", "processName": "vader-m", "type": "other", "artifactDownloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/vader-m", "monitor": false}}, "containerServices": {"contextor": {"name": "<PERSON><PERSON><PERSON><PERSON>or", "image": "************.dkr.ecr.ap-south-1.amazonaws.com/precize-contextor:qa", "account": "************", "region": "ap-south-1", "containerName": "precize-contextor"}}, "files": {"application.yml": {"downloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-platform/_work/precize-platform/precize-platform/config/qa/application-scan.yml", "filePath": "/app/precize-server/application.yml"}, "issue_risk_details.json": {"downloadPath": "ubuntu@13.126.0.200:/home/<USER>/actions-runner-provider/_work/precize-provider/precize-provider/prioritiser/incidents/issue_risk_details.json", "filePath": "/app/precize-prioritiser/issue_risk_details.json"}}}