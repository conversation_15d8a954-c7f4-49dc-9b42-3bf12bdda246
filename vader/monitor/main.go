package main

import (
	"encoding/json"
	"flag"
	"os"
	"os/exec"
	"strconv"

	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/precize/vader/monitor/components"
	"github.com/precize/vader/types"
)

func main() {

	logger.InitializeLogs("vader-m", false)
	logger.Print(logger.INFO, "-------------------------")
	logger.Print(logger.INFO, "Starting Vader Monitoring")
	logger.Print(logger.INFO, "-------------------------")

	var appConfigPath = flag.String("config", "vader.json", "Path to vader.json")
	flag.Parse()

	configFile := *appConfigPath

	dat, err := os.ReadFile(configFile)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read file", err)
		return
	}

	var conf types.VaderConfig

	if err = json.Unmarshal(dat, &conf); err != nil {
		logger.Print(logger.ERROR, "Failed to unmarshal", err)
		return
	}

	if appConfigFile, ok := conf.Files["application.yml"]; ok {
		config.InitializeApplicationConfig(appConfigFile.FilePath)
	}

	vaderPIdBytes, err := exec.Command("sh", "-c", "ps -ef | grep 'vader-m' | grep -v 'grep' | wc -l").CombinedOutput()
	if err != nil {
		logger.Print(logger.ERROR, "Vader monitor pid command failed", err)
		return
	}

	vaderInstanceCount, _ := strconv.Atoi(string(vaderPIdBytes))
	if vaderInstanceCount > 1 {
		// Another instance running
		logger.Print(logger.INFO, "A vader check is already in progress. Exiting")
		return
	}

	logger.Print(logger.INFO, "Checking deployment status")

	deploymentPIdBytes, err := exec.Command("sh", "-c", "ps -ef | grep 'vader-d' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
	if err != nil {
		logger.Print(logger.ERROR, "Vader deployment pid command failed", err)
		return
	}

	if len(deploymentPIdBytes) > 0 {
		logger.Print(logger.INFO, "Deployment is in progress. Exiting Vader Monitoring")
		return
	}

	components.CheckServices(&conf)
	components.CheckContainerServices(&conf)
	components.CheckMachines(&conf)
	components.CheckJumpMachines(&conf)

	if dat, err = json.MarshalIndent(conf, "", "\t"); err != nil {
		logger.Print(logger.ERROR, "Failed to marshal", err)
	}

	if err = os.WriteFile(configFile, dat, 0644); err != nil {
		logger.Print(logger.ERROR, "Failed to write to file", err)
	}

	logger.Print(logger.INFO, "Vader checks completed")
}
