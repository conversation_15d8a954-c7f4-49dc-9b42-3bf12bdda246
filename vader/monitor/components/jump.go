package components

import (
	"errors"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func CheckJumpMachines(conf *types.VaderConfig) {

	logger.Print(logger.INFO, "Starting jump machine checks")

	for i, machine := range conf.JumpMachines {

		if machine.Enabled {

			if machine.Type == "elastic_data_node" {
				if err := checkDoubleJumpMachine(conf, machine, i); err != nil {
					continue
				}
			} else {
				if err := checkSingleJumpMachine(conf, machine, i); err != nil {
					continue
				}
			}
		}
	}
}

func checkDoubleJumpMachine(conf *types.VaderConfig, machine types.JumpMachine, machineID string) error {

	var machineIP string

	if len(config.AppConfig.Spring.ElasticSearch.Host) > 0 {
		machineIP = config.AppConfig.Spring.ElasticSearch.Host
	} else {
		err := errors.New("elastic config host empty")
		return err
	}

	for j, service := range machine.Services {

		if service.Monitor {

			logger.Print(logger.INFO, "Checking service liveliness", []string{machine.ID, service.Name})

			pidBytes, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" \"ssh -o StrictHostKeyChecking=no "+machine.ID+" 'ps -ef'\" | grep \""+service.ProcessPath+"\" | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
			if err != nil {
				logger.Print(logger.ERROR, "Service pid command failed", []string{service.Name}, err)
				return err
			}

			tmp := conf.JumpMachines[machineID].Services[j]

			if len(pidBytes) <= 0 {

				logger.Print(logger.INFO, "Service is not running", []string{service.Name})
				tmp.LastDownTime = time.Now()

				var (
					emailTimeDiff = 10 * time.Minute
					body          string
					noRecentEmail bool
				)

				if time.Now().Hour() >= 19 && time.Now().Hour() < 3 {
					emailTimeDiff = 1 * time.Hour
				}

				noRecentEmail = time.Now().Sub(service.LastDownEmail) > emailTimeDiff

				body = "Service '" + service.Name + "' is not running in " + machine.Name + " machine"
				body += "\nAttention required."

				body = EMAIL_INTRO + body + EMAIL_ENDING
				content := emailContent{
					Subject: SERVICE_DOWN_SUBJECT,
					Body:    body,
					Entity:  service.Name,
				}
				SendEmail(content, noRecentEmail, (conf.Prod || service.EmailOverride), &tmp.LastDownEmail, config.AppConfig.Environment)
			} else {
				logger.Print(logger.INFO, "Service is running", []string{machine.ID, service.Name})
			}

			conf.JumpMachines[machineID].Services[j] = tmp
		}
	}

	for _, devicePath := range machine.DevicePaths {

		logger.Print(logger.INFO, "Checking disk space for jump machine", []string{machine.ID, devicePath})

		totalSize, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" \"ssh -o StrictHostKeyChecking=no "+machine.ID+" 'df -H'\" | awk '$6 == \""+devicePath+"\" {print $2}'").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Disk total size command failed: ", []string{devicePath}, err)
			return err
		}

		if len(totalSize) <= 0 {
			return err
		}

		availableSize, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" \"	ssh -o StrictHostKeyChecking=no "+machine.ID+" 'df -H'\" | awk '$6 == \""+devicePath+"\" {print $4}'").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Disk available size command failed: ", []string{devicePath}, err)
			return err
		}

		totalSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(totalSize), "G\n"), 64)
		if err != nil {
			logger.Print(logger.ERROR, "Total size float conversion failed: ", []string{devicePath}, err)
			return err
		}

		availableSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(availableSize), "G\n"), 64)
		if err != nil {
			logger.Print(logger.ERROR, "Available size float conversion failed: ", []string{devicePath}, err)
			return err
		}

		percentage := (availableSizeFloat * 100 / totalSizeFloat)

		logger.Print(logger.INFO, "Available space for alternate machine", []string{machine.ID, devicePath}, fmt.Sprintf("%.2f", percentage))

		if percentage <= 15 {

			logger.Print(logger.INFO, "Exceeded threshold disk space on device path for alternate machine", []string{machine.ID, devicePath})
			conf.LastThresholdExceededTime = time.Now()

			noRecentEmail := time.Now().Sub(conf.LastThresholdExceededEmail) > 12*time.Hour
			body := "Disk space left is " + fmt.Sprintf("%.2f", percentage) + " percent in machine " + machine.Name + " for device path '" + devicePath + "'"
			body = EMAIL_INTRO + body + EMAIL_ENDING
			content := emailContent{
				Subject: LOW_DISK_SPACE_SUBJECT,
				Body:    body,
				Entity:  devicePath,
			}
			SendEmail(content, noRecentEmail, (conf.Prod || machine.EmailOverride), &conf.LastThresholdExceededEmail, config.AppConfig.Environment)
		}
	}

	return nil
}

func checkSingleJumpMachine(conf *types.VaderConfig, machine types.JumpMachine, machineID string) error {

	var machineIP string

	switch machine.Type {

	case "elastic_master_node":
		if len(config.AppConfig.Spring.ElasticSearch.Host) > 0 {
			machineIP = config.AppConfig.Spring.ElasticSearch.Host
		} else {
			err := errors.New("elastic config host empty")
			return err
		}
	case "redis":
		if len(config.AppConfig.Spring.Redis.Host) > 0 {
			machineIP = config.AppConfig.Spring.Redis.Host
		} else {
			err := errors.New("redis config host empty")
			return err
		}
	case "vector":
		if len(config.AppConfig.Spring.Vector.Host) > 0 {
			machineIP = config.AppConfig.Spring.Vector.Host
		} else {
			err := errors.New("vector config host empty")
			return err
		}
	}

	for j, service := range machine.Services {

		if service.Monitor {

			logger.Print(logger.INFO, "Checking service liveliness", []string{machine.ID, service.Name})

			pidBytes, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" 'ps -ef' | grep \""+service.ProcessPath+"\" | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
			if err != nil {
				logger.Print(logger.ERROR, "Service pid command failed", []string{service.Name}, err)
				return err
			}

			tmp := conf.JumpMachines[machineID].Services[j]

			if len(pidBytes) <= 0 {

				logger.Print(logger.INFO, "Service is not running", []string{service.Name})
				tmp.LastDownTime = time.Now()

				var (
					emailTimeDiff = 10 * time.Minute
					body          string
					noRecentEmail bool
				)

				if time.Now().Hour() >= 19 && time.Now().Hour() < 3 {
					emailTimeDiff = 1 * time.Hour
				}

				noRecentEmail = time.Now().Sub(service.LastDownEmail) > emailTimeDiff

				body = "Service '" + service.Name + "' is not running in " + machine.Name + " machine"
				body += "\nAttention required."

				body = EMAIL_INTRO + body + EMAIL_ENDING
				content := emailContent{
					Subject: SERVICE_DOWN_SUBJECT,
					Body:    body,
					Entity:  service.Name,
				}
				SendEmail(content, noRecentEmail, (conf.Prod || service.EmailOverride), &tmp.LastDownEmail, config.AppConfig.Environment)
			} else {
				logger.Print(logger.INFO, "Service is running", []string{machine.ID, service.Name})
			}

			conf.JumpMachines[machineID].Services[j] = tmp
		}
	}

	for _, devicePath := range machine.DevicePaths {

		logger.Print(logger.INFO, "Checking disk space for jump machine", []string{machine.ID, devicePath})

		totalSize, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" 'df -H' | awk '$6 == \""+devicePath+"\" {print $2}'").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Disk total size command failed: ", []string{devicePath}, err)
			return err
		}

		if len(totalSize) <= 0 {
			return err
		}

		availableSize, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" 'df -H' | awk '$6 == \""+devicePath+"\" {print $4}'").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Disk available size command failed: ", []string{devicePath}, err)
			return err
		}

		totalSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(totalSize), "G\n"), 64)
		if err != nil {
			logger.Print(logger.ERROR, "Total size float conversion failed: ", []string{devicePath}, err)
			return err
		}

		availableSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(availableSize), "G\n"), 64)
		if err != nil {
			logger.Print(logger.ERROR, "Available size float conversion failed: ", []string{devicePath}, err)
			return err
		}

		percentage := (availableSizeFloat * 100 / totalSizeFloat)

		logger.Print(logger.INFO, "Available space for alternate machine", []string{machine.ID, devicePath}, fmt.Sprintf("%.2f", percentage))

		if percentage <= 15 {

			logger.Print(logger.INFO, "Exceeded threshold disk space on device path for alternate machine", []string{machine.ID, devicePath})
			conf.LastThresholdExceededTime = time.Now()

			noRecentEmail := time.Now().Sub(conf.LastThresholdExceededEmail) > 12*time.Hour
			body := "Disk space left is " + fmt.Sprintf("%.2f", percentage) + " percent in machine " + machine.Name + " for device path '" + devicePath + "'"
			body = EMAIL_INTRO + body + EMAIL_ENDING
			content := emailContent{
				Subject: LOW_DISK_SPACE_SUBJECT,
				Body:    body,
				Entity:  devicePath,
			}
			SendEmail(content, noRecentEmail, (conf.Prod || machine.EmailOverride), &conf.LastThresholdExceededEmail, config.AppConfig.Environment)
		}
	}

	for k, containerService := range machine.ContainerServices {

		if containerService.Monitor {

			logger.Print(logger.INFO, "Checking service liveliness", []string{machine.ID, containerService.Name})

			out, err := exec.Command("sh", "-c", "ssh -o StrictHostKeyChecking=no ubuntu@"+machineIP+" sudo docker ps -q --filter 'ancestor="+containerService.Image+"' | wc -l").CombinedOutput()
			if err != nil {
				logger.Print(logger.ERROR, "Docker ps command failed", err, string(out))
				continue
			}

			countStr := strings.TrimSpace(string(out))
			count, err := strconv.Atoi(countStr)
			if err != nil {
				logger.Print(logger.ERROR, "Failed to parse container count", err, countStr)
				continue
			}

			tmp := machine.ContainerServices[k]

			if count < containerService.ContainerCount {

				logger.Print(logger.INFO, "Container count below expected", []string{machine.ID, containerService.Name}, "Expected: "+strconv.Itoa(containerService.ContainerCount)+", Running: "+strconv.Itoa(count))

				var (
					emailTimeDiff = 10 * time.Minute
					body          string
					noRecentEmail bool
				)

				if time.Now().Hour() >= 19 && time.Now().Hour() < 3 {
					emailTimeDiff = 1 * time.Hour
				}

				noRecentEmail = time.Now().Sub(containerService.LastDownEmail) > emailTimeDiff

				body = "Service '" + containerService.Name + "' is not running in " + machine.Name + " machine"
				body += "\nAttention Required"

				body = EMAIL_INTRO + body + EMAIL_ENDING
				content := emailContent{
					Subject: SERVICE_DOWN_SUBJECT,
					Body:    body,
					Entity:  containerService.Name,
				}
				SendEmail(content, noRecentEmail, (conf.Prod || containerService.EmailOverride), &tmp.LastDownEmail, config.AppConfig.Environment)
			} else {
				logger.Print(logger.INFO, "Containers are running as expected", []string{machine.ID, containerService.Name})
			}

			machine.ContainerServices[k] = tmp
		}
	}

	return nil
}
