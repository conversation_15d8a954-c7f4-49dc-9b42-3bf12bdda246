package components

import (
	"time"

	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"

	"github.com/precize/common"
	"github.com/precize/logger"
)

const (
	SERVICE_DOWN_SUBJECT   = "Critical service went down"
	LOW_DISK_SPACE_SUBJECT = "Disk space exceeded threshold"

	EMAIL_INTRO  = "Team,\n\n"
	EMAIL_ENDING = "\n\n- Vader"
)

type emailContent struct {
	Subject string
	Body    string
	Entity  string
}

func SendEmail(mailContent emailContent, noRecentEmail, emailEnabled bool, lastEmailTime *time.Time, env string) error {

	if emailEnabled && noRecentEmail {
		logger.Print(logger.INFO, "Sending email with subject", []string{mailContent.Entity}, env+": "+mailContent.Subject)

		m := mail.NewV3Mail()

		from := mail.NewEmail("Vader Monitor", "<EMAIL>")
		content := mail.NewContent("text/plain", mailContent.Body)

		m.SetFrom(from)
		m.AddContent(content)

		personalization := mail.NewPersonalization()

		to1 := mail.NewEmail("SRE", "<EMAIL>")

		personalization.AddTos(to1)
		personalization.Subject = mailContent.Subject + " in " + common.ConvertToTitleCase(env) + " environment"

		m.AddPersonalizations(personalization)

		request := sendgrid.GetRequest("*********************************************************************", "/v3/mail/send", "https://api.sendgrid.com")
		request.Method = "POST"
		request.Body = mail.GetRequestBody(m)
		response, err := sendgrid.API(request)
		if err != nil || (response.StatusCode < 200 && response.StatusCode >= 300) {
			logger.Print(logger.ERROR, "Email sending failed", err, response.StatusCode)
			return err
		} else {
			logger.Print(logger.INFO, "Email sent", []string{mailContent.Entity})
			*lastEmailTime = time.Now()
		}
	} else if !emailEnabled {
		logger.Print(logger.INFO, "Emails are disabled", []string{mailContent.Entity})
	} else {
		logger.Print(logger.INFO, "Email already sent at "+lastEmailTime.String(), []string{mailContent.Entity})
	}

	return nil
}
