package components

import (
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func CheckMachines(conf *types.VaderConfig) {

	logger.Print(logger.INFO, "Starting machine checks")

	for _, devicePath := range conf.DevicePaths {

		logger.Print(logger.INFO, "Checking disk space", []string{devicePath})

		totalSize, err := exec.Command("sh", "-c", "df -H | awk '$6 == \""+devicePath+"\" {print $2}'").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Disk total size command failed: ", []string{devicePath}, err)
			continue
		}

		if len(totalSize) <= 0 {
			continue
		}

		availableSize, err := exec.Command("sh", "-c", "df -H | awk '$6 == \""+devicePath+"\" {print $4}'").CombinedOutput()
		if err != nil {
			logger.Print(logger.ERROR, "Disk available size command failed: ", []string{devicePath}, err)
			continue
		}

		totalSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(totalSize), "G\n"), 64)
		if err != nil {
			logger.Print(logger.ERROR, "Total size float conversion failed: ", []string{devicePath}, err)
			continue
		}

		availableSizeFloat, err := strconv.ParseFloat(strings.TrimSuffix(string(availableSize), "G\n"), 64)
		if err != nil {
			logger.Print(logger.ERROR, "Available size float conversion failed: ", []string{devicePath}, err)
			continue
		}

		percentage := (availableSizeFloat * 100 / totalSizeFloat)

		logger.Print(logger.INFO, "Available space", []string{devicePath}, fmt.Sprintf("%.2f", percentage))

		if percentage <= 15 {

			logger.Print(logger.INFO, "Exceeded threshold disk space on device path", []string{devicePath})
			conf.LastThresholdExceededTime = time.Now()

			noRecentEmail := time.Now().Sub(conf.LastThresholdExceededEmail) > 12*time.Hour
			body := "Disk space left is " + fmt.Sprintf("%.2f", percentage) + " percent in machine " + conf.Machine + " for device path '" + devicePath + "'"
			body = EMAIL_INTRO + body + EMAIL_ENDING
			content := emailContent{
				Subject: LOW_DISK_SPACE_SUBJECT,
				Body:    body,
				Entity:  devicePath,
			}
			SendEmail(content, noRecentEmail, (conf.Prod || conf.EmailOverride), &conf.LastThresholdExceededEmail, config.AppConfig.Environment)
		}
	}
}
