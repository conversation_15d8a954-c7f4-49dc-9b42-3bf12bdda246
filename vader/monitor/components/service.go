package components

import (
	"os/exec"
	"strings"
	"time"

	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/precize/vader/types"
)

func CheckServices(conf *types.VaderConfig) {

	logger.Print(logger.INFO, "Starting service checks")

	for k, service := range conf.Services {

		if service.Monitor {

			logger.Print(logger.INFO, "Checking service liveliness", []string{service.Name})

			processSearchKeyword := service.ProcessPath
			if k == "platform" {
				processSearchKeyword = service.ProcessName
			}

			pidBytes, err := exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput()
			if err != nil {
				logger.Print(logger.ERROR, "Service pid command failed", []string{service.Name}, err)
				continue
			}

			tmp := conf.Services[k]

			if len(pidBytes) <= 0 {

				logger.Print(logger.INFO, "Service is not running", []string{service.Name})
				tmp.LastDownTime = time.Now()

				var (
					emailTimeDiff = 10 * time.Minute
					body          string
					noRecentEmail bool
				)

				if time.Now().Hour() >= 19 && time.Now().Hour() < 3 {
					emailTimeDiff = 1 * time.Hour
				}

				noRecentEmail = time.Now().Sub(service.LastDownEmail) > emailTimeDiff

				if service.AutoRestart {

					logger.Print(logger.INFO, "Restarting service", []string{service.Name})

					exec.Command("sh", "-c", service.Command).Run()
					time.Sleep(2 * time.Second)

					if pidBytes, err = exec.Command("sh", "-c", "ps -ef | grep '"+processSearchKeyword+
						"' | grep -v 'grep' | awk '{print $2}'").CombinedOutput(); err != nil {
						logger.Print(logger.ERROR, "Service restart pid command failed", []string{service.Name}, err)
						continue
					}

					if len(pidBytes) <= 0 {
						logger.Print(logger.ERROR, "Process restart command failed", []string{service.Name}, err)
						tmp.RestartFailed = true

						body = "Service '" + service.Name + "' is not running in " + conf.Machine + " machine"
						body += "\nAuto restart is enabled for service."
						body += "\nService '" + service.Name + "' restart failed. Attention Required"

					} else {
						logger.Print(logger.INFO, "Service restarted", []string{service.Name})

						body = "Service '" + service.Name + "' is not running in " + conf.Machine + " machine"
						body += "\nAuto restart is enabled for service."
						body += "\nService Restarted."
					}

					tmp.LastAutoRestartTime = time.Now()
				} else {

					logger.Print(logger.INFO, "Auto restart is disabled", []string{service.Name})

					body = "Service '" + service.Name + "' is not running in " + conf.Machine + " machine"
					body += "\nAuto restart is disabled for service. Attention required."
				}

				body = EMAIL_INTRO + body + EMAIL_ENDING
				content := emailContent{
					Subject: SERVICE_DOWN_SUBJECT,
					Body:    body,
					Entity:  service.Name,
				}
				SendEmail(content, noRecentEmail, (conf.Prod || service.EmailOverride), &tmp.LastDownEmail, config.AppConfig.Environment)

				if tmp.RestartFailed {
					continue
				}
			}

			tmp.RestartFailed = false
			conf.Services[k] = tmp

			logger.Print(logger.INFO, "Getting service stats", []string{service.Name})

			pid := strings.TrimSuffix(string(pidBytes), "\n")

			stats, err := exec.Command("sh", "-c", "ps -p "+pid+" -o %cpu,%mem").CombinedOutput()
			if err != nil {
				logger.Print(logger.ERROR, "Process statistics command failed", []string{service.Name}, err)
				continue
			}

			logger.Print(logger.INFO, "Stats for process", []string{service.Name}, "\n"+string(stats))
		}
	}
}
