package components

import (
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/precize/vader/run"
	"github.com/precize/vader/types"
)

func CheckContainerServices(conf *types.VaderConfig) {

	logger.Print(logger.INFO, "Starting container service checks")

	for k, containerService := range conf.ContainerServices {

		if containerService.Monitor {

			logger.Print(logger.INFO, "Checking service liveliness", []string{containerService.Name})

			out, err := exec.Command("sh", "-c", "sudo docker ps -q --filter 'ancestor="+containerService.Image+"' | wc -l").CombinedOutput()
			if err != nil {
				logger.Print(logger.ERROR, "Docker ps command failed", err, string(out))
				continue
			}

			countStr := strings.TrimSpace(string(out))
			count, err := strconv.Atoi(countStr)
			if err != nil {
				logger.Print(logger.ERROR, "Failed to parse container count", err, countStr)
				continue
			}

			tmp := conf.ContainerServices[k]

			if count < containerService.ContainerCount {

				logger.Print(logger.INFO, "Container count below expected", []string{containerService.Name}, "Expected: "+strconv.Itoa(containerService.ContainerCount)+", Running: "+strconv.Itoa(count))

				var (
					emailTimeDiff = 10 * time.Minute
					body          string
					noRecentEmail bool
				)

				if time.Now().Hour() >= 19 && time.Now().Hour() < 3 {
					emailTimeDiff = 1 * time.Hour
				}

				noRecentEmail = time.Now().Sub(containerService.LastDownEmail) > emailTimeDiff

				if containerService.AutoRestart {

					logger.Print(logger.INFO, "Restarting service", []string{containerService.Name})

					if err = run.RunContainerService(containerService, ""); err != nil {
						continue
					}

					if out, err = exec.Command("sh", "-c", "sudo docker ps -q --filter 'ancestor="+containerService.Image+"' | wc -l").CombinedOutput(); err != nil {
						logger.Print(logger.ERROR, "Docker ps command failed", err, string(out))
						continue
					}

					countStr = strings.TrimSpace(string(out))
					if count, err = strconv.Atoi(countStr); err != nil {
						logger.Print(logger.ERROR, "Failed to parse container count", err, countStr)
						continue
					}

					if count < containerService.ContainerCount {
						logger.Print(logger.ERROR, "Process restart command failed", []string{containerService.Name}, err)
						tmp.RestartFailed = true

						body = "Service '" + containerService.Name + "' is not running in " + conf.Machine + " machine"
						body += "\nAuto restart is enabled for service."
						body += "\nService '" + containerService.Name + "' restart failed. Attention Required"

					} else {

						body = "Service '" + containerService.Name + "' is not running in " + conf.Machine + " machine"
						body += "\nAuto restart is enabled for service."
						body += "\nService Restarted."

						tmp.LastAutoRestartTime = time.Now()
					}
				} else {

					logger.Print(logger.INFO, "Auto restart is disabled", []string{containerService.Name})

					body = "Service '" + containerService.Name + "' is not running in " + conf.Machine + " machine"
					body += "\nAuto restart is disabled for service. Attention required."
				}

				body = EMAIL_INTRO + body + EMAIL_ENDING
				content := emailContent{
					Subject: SERVICE_DOWN_SUBJECT,
					Body:    body,
					Entity:  containerService.Name,
				}
				SendEmail(content, noRecentEmail, (conf.Prod || containerService.EmailOverride), &tmp.LastDownEmail, config.AppConfig.Environment)

				if tmp.RestartFailed {
					continue
				}

			} else {
				logger.Print(logger.INFO, "Containers are running as expected", []string{containerService.Name})
			}

			tmp.RestartFailed = false
			conf.ContainerServices[k] = tmp
		}
	}
}
