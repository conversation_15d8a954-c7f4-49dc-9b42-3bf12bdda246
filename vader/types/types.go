package types

import (
	"time"
)

type VaderConfig struct {
	Machine                    string                      `json:"machine"`
	Prod                       bool                        `json:"prod"`
	EmailOverride              bool                        `json:"emailOverride"`
	DevicePaths                []string                    `json:"devicePaths"`
	LastThresholdExceededTime  time.Time                   `json:"lastThresholdExceededTime,omitempty"`
	LastThresholdExceededEmail time.Time                   `json:"lastThresholdExceededEmail,omitempty"`
	Services                   map[string]Service          `json:"services"`
	ContainerServices          map[string]ContainerService `json:"containerServices"`
	JumpMachines               map[string]JumpMachine      `json:"jumpMachines"`
	Files                      map[string]File             `json:"files"`
}

type Service struct {
	Name                 string    `json:"name"`
	ProcessPath          string    `json:"processPath"`
	ProcessName          string    `json:"processName"`
	Command              string    `json:"command,omitempty"`
	Type                 string    `json:"type"`
	ArtifactDownloadPath string    `json:"artifactDownloadPath,omitempty"`
	ConfigFiles          []string  `json:"configFiles,omitempty"`
	LastAutoRestartTime  time.Time `json:"lastAutoRestartTime,omitempty"`
	LastDownTime         time.Time `json:"lastDownTime,omitempty"`
	LastDownEmail        time.Time `json:"lastDownEmail,omitempty"`
	Monitor              bool      `json:"monitor"`
	AutoRestart          bool      `json:"autoRestart,omitempty"`
	RestartFailed        bool      `json:"restartFailed,omitempty"`
	EmailOverride        bool      `json:"emailOverride"`
}

type ContainerService struct {
	Name                string    `json:"name"`
	Image               string    `json:"image"`
	Account             string    `json:"account"`
	Region              string    `json:"region"`
	ConfigFiles         []string  `json:"configFiles,omitempty"`
	ContainerName       string    `json:"containerName"`
	ContainerCount      int       `json:"containerCount"`
	ContainerArgs       string    `json:"containerArgs,omitempty"`
	CommandArgs         string    `json:"commandArgs,omitempty"`
	LastAutoRestartTime time.Time `json:"lastAutoRestartTime,omitempty"`
	LastDownTime        time.Time `json:"lastDownTime,omitempty"`
	LastDownEmail       time.Time `json:"lastDownEmail,omitempty"`
	Monitor             bool      `json:"monitor"`
	Run                 bool      `json:"run"`
	AutoRestart         bool      `json:"autoRestart,omitempty"`
	RestartFailed       bool      `json:"restartFailed,omitempty"`
	EmailOverride       bool      `json:"emailOverride"`
}

type JumpMachine struct {
	ID                string                      `json:"id"`
	Name              string                      `json:"name"`
	Services          map[string]Service          `json:"services"`
	ContainerServices map[string]ContainerService `json:"containerServices"`
	EmailOverride     bool                        `json:"emailOverride"`
	DevicePaths       []string                    `json:"devicePaths"`
	Type              string                      `json:"type"`
	Enabled           bool                        `json:"enabled"`
}

type File struct {
	DownloadPath string `json:"downloadPath"`
	FilePath     string `json:"filePath"`
}
