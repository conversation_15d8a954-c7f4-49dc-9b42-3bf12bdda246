package environments

import (
	"github.com/precize/rds"
)

func ServiceHasEnvironments(tenantID string, serviceID int) (bool, error) {

	var exists bool

	query := `
        SELECT EXISTS (
            SELECT 1
            FROM environments
            WHERE tenant_id = $1
              AND service_id = $2
            LIMIT 1
        )
    `

	err := rds.Get(&exists, query, tenantID, serviceID)
	if err != nil {
		return false, err
	}

	return exists, nil
}

func InsertEnvironment(env EnvironmentsRecord) error {
	query := `
        INSERT INTO environments (tenant_id, service_id, name, repositories)
        VALUES (:tenant_id, :service_id, :name, :repositories)
    `
	_, err := rds.NamedExec(query, env)
	return err
}
