package environments

import (
	"encoding/json"
	"time"
)

type EnvironmentsRecord struct {
	ID               string          `db:"id"`
	AzureTenantID    string          `db:"azure_tenant_id"`
	ClientID         string          `db:"client_id"`
	CreatedTime      time.Time       `db:"created_time"`
	EncrClientSecret string          `db:"encr_client_secret"`
	ErrorMsg         string          `db:"error_msg"`
	Name             string          `db:"name"`
	OnboardingStatus int             `db:"onboarding_status"`
	Repositories     json.RawMessage `db:"repositories"`
	UpdatedTime      time.Time       `db:"updated_time"`
	ServiceID        int             `db:"service_id"`
	TenantID         string          `db:"tenant_id	"`
}
