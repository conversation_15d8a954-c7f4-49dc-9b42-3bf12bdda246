package scans

import (
	"encoding/json"
	"time"
)

type ScansRecord struct {
	ID                       string          `db:"id"`
	CollectedAt              int64           `db:"collected_at"`
	EndTime                  time.Time       `db:"end_time"`
	EnvEntityStatuses        json.RawMessage `db:"env_entity_statuses"`
	IsCompleteScan           string          `db:"is_complete_scan"`
	PostScan                 string          `db:"post_scan"`
	Priority                 int             `db:"priority"`
	RecordsFailed            int64           `db:"records_failed"`
	RecordsInserted          int64           `db:"records_inserted"`
	RecordsSkipped           int64           `db:"records_skipped"`
	RecordsUpdated           int64           `db:"records_updated"`
	ResourcesProcessed       int64           `db:"resources_processed"`
	ResultMessage            string          `db:"result_message"`
	ScanType                 int             `db:"scan_type"`
	SearchFilters            json.RawMessage `db:"search_filters"`
	ServerName               string          `db:"server_name"`
	Stage                    int             `db:"stage"`
	StageStatuses            json.RawMessage `db:"stage_statuses"`
	StartTime                time.Time       `db:"start_time"`
	Status                   int             `db:"status"`
	TotalResourcesIdentified int64           `db:"total_resources_identified"`
	TriggerBy                string          `db:"trigger_by"`
	TriggerTime              time.Time       `db:"trigger_time"`
	ServiceID                int             `db:"service_id"`
	TenantID                 string          `db:"tenant_id"`
}
