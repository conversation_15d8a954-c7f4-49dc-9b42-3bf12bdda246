package scans

import (
	"database/sql"
	"fmt"
	"strconv"

	"github.com/precize/logger"
	"github.com/precize/rds"
)

func GetPreviousCollectedAt(tenantID, lastCollectedAtStr, serviceIDStr string) (string, error) {

	var previousCollectedAt sql.NullInt64

	lastCollectedAt, err := strconv.ParseInt(lastCollectedAtStr, 10, 64)
	if err != nil {
		logger.Print(logger.ERROR, "CollectedAt parse failed", err)
		return "", err
	}

	serviceID, err := strconv.Atoi(serviceIDStr)
	if err != nil {
		logger.Print(logger.ERROR, "ServiceID parse failed", err)
		return "", err
	}

	query := `
		SELECT collected_at
		FROM scans
		WHERE tenant_id = $1
		  AND service_id = $2
		  AND scan_type = 0
		  AND status = 2
		  AND collected_at < $3
		ORDER BY collected_at DESC
		LIMIT 1
	`

	if err = rds.Get(&previousCollectedAt, query, tenantID, serviceID, lastCollectedAt); err != nil {
		if err == sql.ErrNoRows {
			return "", nil
		}
		return "", err
	}

	if previousCollectedAt.Valid {
		return fmt.Sprintf("%d", previousCollectedAt.Int64), nil
	}

	return "", nil
}
