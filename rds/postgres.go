package rds

import (
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"

	"github.com/precize/common/precize/secrets"
	"github.com/precize/config"
	"github.com/precize/logger"
)

var pgClient *sqlx.DB

func ConnectToPostgres() error {

	rdsURL := config.AppConfig.Spring.DataSource.URL

	if len(rdsURL) == 0 {
		err := errors.New("Config does not have datasource specifications")
		logger.Print(logger.ERROR, "RDS connection failed", err)
		return err
	}

	secretConfig := config.AppConfig.Spring.DataSource.Secret

	rdsCred, err := secrets.RetrieveRDSSecret(
		secretConfig.Name,
		secretConfig.Region,
		secretConfig.AccessKey,
		secretConfig.SecretKey,
	)
	if err != nil {
		return err
	}

	urlParts := strings.SplitN(rdsURL, "/", 2) // split host:port and dbname

	hostPort := urlParts[0]
	dbname := urlParts[1]

	hostPortParts := strings.SplitN(hostPort, ":", 2)
	host := hostPortParts[0]
	port, err := strconv.Atoi(hostPortParts[1])
	if err != nil {
		logger.Print(logger.ERROR, "Parse to int failed", err)
		return err
	}

	pgInfo := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s", host, port, rdsCred.Username, rdsCred.Password, dbname)

	if pgClient, err = sqlx.Connect("postgres", pgInfo); err != nil {
		logger.Print(logger.ERROR, "Got error calling sql open", err)
		return err
	}

	if pgClient == nil {
		err := errors.New("pgClient is nil")
		logger.Print(logger.ERROR, "Got error calling sql open", err)
		return err
	}

	logger.Print(logger.INFO, "Connected to Postgres RDS", host, port)

	return nil
}

func Select(dest any, query string, args ...any) error {
	err := pgClient.Select(dest, query, args...)
	if err != nil {
		logger.Print(logger.ERROR, "Postgres Select request failed", err)
		return err
	}

	return nil
}

func Get(dest any, query string, args ...any) error {
	err := pgClient.Get(dest, query, args...)
	if err != nil {
		if err != sql.ErrNoRows {
			// Handle no rows error from caller
			logger.Print(logger.ERROR, "Postgres Get request failed", err)
		}
		return err
	}

	return nil
}

func Exec(query string, args ...any) (sql.Result, error) {
	result, err := pgClient.Exec(query, args...)
	if err != nil {
		logger.Print(logger.ERROR, "Postgres Exec request failed", err)
		return nil, err
	}

	return result, nil
}

func MustExec(query string, args ...any) sql.Result {
	// Only use for queries that must not fail, like Create table (else it will panic)
	return pgClient.MustExec(query, args...)
}

func NamedExec(query string, arg any) (sql.Result, error) {
	result, err := pgClient.NamedExec(query, arg)
	if err != nil {
		logger.Print(logger.ERROR, "Postgres NamedExec request failed", err)
		return nil, err
	}

	return result, nil
}
