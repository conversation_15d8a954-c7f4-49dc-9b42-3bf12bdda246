package tenants

import (
	"database/sql"

	"github.com/precize/rds"
)

func GetTenant(tenantID string) (*TenantsRecord, error) {

	var tenant TenantsRecord

	query := `
        SELECT id, company_name, created_time, email, 
        	   is_deleted, name, type
        FROM tenants
        WHERE id = $1
        LIMIT 1
    `

	err := rds.Get(&tenant, query, tenantID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &tenant, nil
}

func GetAllTenantIDs() ([]string, error) {

	tenantIDs := []string{}

	query := `
        SELECT id
        FROM tenants
        ORDER BY created_time DESC
    `

	err := rds.Select(&tenantIDs, query)
	if err != nil {
		return nil, err
	}

	return tenantIDs, nil
}
