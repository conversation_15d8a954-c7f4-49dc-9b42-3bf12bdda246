package tenants

import (
	"time"
)

type TenantsRecord struct {
	ID                string    `db:"id"`
	AwsExternalID     string    `db:"aws_external_id"`
	CompanyName       string    `db:"company_name"`
	CreatedTime       time.Time `db:"created_time"`
	Email             string    `db:"email"`
	EnableActivity    bool      `db:"enable_activity"`
	IncreaseDelayTime bool      `db:"increase_delay_time"`
	IsDeleted         bool      `db:"is_deleted"`
	Name              string    `db:"name"`
	Type              string    `db:"type"`
	UpdatedTime       time.Time `db:"updated_time"`
}
