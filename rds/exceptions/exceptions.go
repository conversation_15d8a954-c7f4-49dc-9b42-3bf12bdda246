package exceptions

import (
	"github.com/precize/rds"
)

func GetExceptionsForTenant(exceptionType, op, tenantID string) ([]ExceptionsRecord, error) {

	var records []ExceptionsRecord

	query := `
        SELECT key, values
        FROM exceptions
        WHERE tenant_id = $1
          AND type = $2
          AND op = $3
    `

	err := rds.Select(&records, query, tenantID, exceptionType, op)
	if err != nil {
		return nil, err
	}

	return records, nil
}
