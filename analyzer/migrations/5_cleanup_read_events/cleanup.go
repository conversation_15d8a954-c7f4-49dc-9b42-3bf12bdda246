/// Date: 2025-09-24
/// Author: O<PERSON><PERSON>
/// Target: Cloud Activity (readOnly events)
/// Purpose: Manual cleanup of old readOnly events when automated cron cleanup fails
///
/// This migration allows manual cleanup of readOnly events older than a specified number of days.
/// Use this when the automated 7-day cleanup cron job fails or doesn't run properly.

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"strings"
	"time"

	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/analyzer/internal/utils"
	precizeConfig "github.com/precize/config"
	"github.com/precize/logger"
	"github.com/precize/rds"
	"github.com/precize/rds/tenants"
)

func main() {
	configFilePath := flag.String("config", "application.yml", "Path to application.yml")
	daysOld := flag.Int("days", 7, "Delete readOnly events older than this many days")
	tenantID := flag.String("tenant", "", "Specific tenant ID to clean (optional, cleans all tenants if empty)")
	dryRun := flag.Bool("dry-run", false, "Show what would be deleted without actually deleting")
	flag.Parse()

	logger.InitializeLogs("read-events-cleanup", false)
	config.LoadConfig(configFilePath)

	defaultConf, err := precizeConfig.InitializeApplicationConfig(*configFilePath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *configFilePath)
	}
	es := elasticsearch.NewClient()
	if err = rds.ConnectToPostgres(); err != nil {
		return
	}

	if _, err := es.Ping(); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error pinging elasticsearch: %s", err))
		return
	}

	cutoffTime := time.Now().Add(-time.Duration(*daysOld) * 24 * time.Hour).UTC()
	logger.Print(logger.INFO, fmt.Sprintf("Cleaning readOnly events older than %s (%d days)", cutoffTime.Format(utils.ESTimeLayout), *daysOld))

	if *tenantID != "" {
		logger.Print(logger.INFO, fmt.Sprintf("Cleaning for specific tenant: %s", *tenantID))
		cleanupReadEventsForTenant(es, *tenantID, cutoffTime, *dryRun)
	} else {
		logger.Print(logger.INFO, "Cleaning for all tenants")
		cleanupReadEventsForAllTenants(es, cutoffTime, *dryRun)
	}

	fmt.Println("Cleanup completed")
}

func cleanupReadEventsForAllTenants(es *elasticsearch.Client, cutoffTime time.Time, dryRun bool) {
	tenants, err := tenants.GetAllTenantIDs()
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error fetching tenant ids: %s", err))
		return
	}

	logger.Print(logger.INFO, fmt.Sprintf("Found %d tenants to process", len(tenants)))

	for _, tenantID := range tenants {
		cleanupReadEventsForTenant(es, tenantID, cutoffTime, dryRun)
	}
}

func cleanupReadEventsForTenant(es *elasticsearch.Client, tenantID string, cutoffTime time.Time, dryRun bool) {
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"term": map[string]any{
							"readOnly": true,
						},
					},
					{
						"term": map[string]any{
							"tenantId.keyword": tenantID,
						},
					},
					{
						"range": map[string]any{
							"createdTime": map[string]any{
								"lt": cutoffTime.Format(utils.ESTimeLayout),
							},
						},
					},
				},
			},
		},
	}

	prettyQuery, err := json.MarshalIndent(query, "", "  ")
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Failed to encode query for tenant %s: %s", tenantID, err))
		return
	}

	if dryRun {
		// Count documents that would be deleted
		countRes, err := es.Count(
			es.Count.WithBody(strings.NewReader(string(prettyQuery))),
			es.Count.WithIndex(elasticsearch.CLOUD_ACTIVITY_INDEX),
		)
		if err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Error counting documents for tenant %s: %s", tenantID, err))
			return
		}
		defer countRes.Body.Close()

		var countResponse map[string]any
		if err := json.NewDecoder(countRes.Body).Decode(&countResponse); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Failed to parse count response for tenant %s: %s", tenantID, err))
			return
		}

		count, ok := countResponse["count"]
		if !ok {
			logger.Print(logger.ERROR, fmt.Sprintf("Count response missing 'count' field for tenant %s", tenantID))
			return
		}

		logger.Print(logger.INFO, fmt.Sprintf("[DRY RUN] Would delete %v readOnly events for tenant %s", count, tenantID))
		return
	}

	// Actual deletion
	queryReader := strings.NewReader(string(prettyQuery))
	res, err := es.Client.DeleteByQuery(
		[]string{elasticsearch.CLOUD_ACTIVITY_INDEX},
		queryReader,
		es.Client.DeleteByQuery.WithContext(context.Background()),
		es.Client.DeleteByQuery.WithConflicts("proceed"),
		es.Client.DeleteByQuery.WithRefresh(true),
		es.Client.DeleteByQuery.WithPretty(),
	)
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Delete-by-query failed for tenant %s: %s", tenantID, err))
		return
	}
	defer res.Body.Close()

	if res.IsError() {
		var raw map[string]any
		if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Delete failed and could not parse response for tenant %s: %s", tenantID, err))
			return
		}
		logger.Print(logger.ERROR, fmt.Sprintf("Delete-by-query error for tenant %s: %v", tenantID, raw))
		return
	}

	// Parse and log the successful response
	var response map[string]any
	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Failed to parse delete response for tenant %s: %s", tenantID, err))
		return
	}

	deleted, ok := response["deleted"]
	if !ok {
		logger.Print(logger.ERROR, fmt.Sprintf("Delete response missing 'deleted' field for tenant %s", tenantID))
	} else {
		logger.Print(logger.INFO, fmt.Sprintf("Successfully deleted %v readOnly events for tenant %s", deleted, tenantID))
	}

	// Log any failures
	if failures, ok := response["failures"]; ok && failures != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Delete had failures for tenant %s: %v", tenantID, failures))
	}
}
