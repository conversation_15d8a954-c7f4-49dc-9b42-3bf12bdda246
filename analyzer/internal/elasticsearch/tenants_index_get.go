package elasticsearch

// import (
// 	esjson "github.com/precize/analyzer/internal/esjson"
// )

// FetchTenantIds fetches all tenant IDs from the tenants index
//
// Returns a slice of tenant IDs
//
//	func main() {
//		es := elasticsearch.NewClient([]string{"http://localhost:9200"})
//		tenantIds, err := es.FetchTenantIds()
//		if err != nil {
//			log.Fatalf("Error fetching tenant IDs: %s", err)
//		}
//	}

// Migrated to rds

// func (es *Client) FetchTenantIds() ([]string, error) {
// 	queryString, err := esjson.LoadQuery(esjson.FETCH_ONLY_DOC_ID, nil)
// 	if err != nil {
// 		return nil, err
// 	}

// 	type Response struct {
// 		Hits struct {
// 			Hits []struct {
// 				ID string `json:"_id"`
// 			} `json:"hits"`
// 		} `json:"hits"`
// 	}

// 	response, err := ExecuteQuery[Response](es, TENANTS_INDEX, queryString)
// 	if err != nil {
// 		return nil, err
// 	}

// 	tenantIds := make([]string, 0, len(response.Hits.Hits))
// 	for _, hit := range response.Hits.Hits {
// 		tenantIds = append(tenantIds, hit.ID)
// 	}

// 	return tenantIds, nil
// }
