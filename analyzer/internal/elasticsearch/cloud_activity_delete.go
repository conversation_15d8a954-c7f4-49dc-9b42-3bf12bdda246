package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/logger"
)

func (es *Client) DeleteReadOnlyEventsInRange(tid string, startTimeString string, endTime time.Time, isProcessed bool) error {
	ctx := context.Background()

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"term": map[string]any{
							"readOnly": true,
						},
					},
					{
						"term": map[string]any{
							"tenantId.keyword": tid,
						},
					},
					{
						"range": map[string]any{
							"createdTime": map[string]any{
								"lt": endTime.Format(utils.ESTimeLayout),
							},
						},
					},
				},
			},
		},
	}

	if isProcessed {
		query["query"].(map[string]any)["bool"].(map[string]any)["must"] = append(query["query"].(map[string]any)["bool"].(map[string]any)["must"].([]map[string]any), map[string]any{
			"term": map[string]any{
				"isProcessed": true,
			},
		})
	}

	logger.Print(logger.INFO, fmt.Sprintf("Extract transform query string: %v\n", query))
	prettyQuery, err := json.MarshalIndent(query, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to encode delete query: %w", err)
	}
	queryReader := strings.NewReader(string(prettyQuery))

	logger.Print(logger.INFO, "Delete Query String", queryReader)

	res, err := es.Client.DeleteByQuery(
		[]string{CLOUD_ACTIVITY_INDEX},
		queryReader,
		es.Client.DeleteByQuery.WithContext(ctx),
		es.Client.DeleteByQuery.WithConflicts("proceed"),
		es.Client.DeleteByQuery.WithRefresh(true),
		es.Client.DeleteByQuery.WithPretty(),
	)
	if err != nil {
		return fmt.Errorf("delete-by-query failed: %w", err)
	}
	defer res.Body.Close()

	if res.IsError() {
		var raw map[string]any
		if err := json.NewDecoder(res.Body).Decode(&raw); err != nil {
			return fmt.Errorf("delete failed and could not parse response: %w", err)
		}
		return fmt.Errorf("delete-by-query error: %v", raw)
	}

	// Parse and log the successful response
	var response map[string]any
	if err := json.NewDecoder(res.Body).Decode(&response); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Failed to parse delete response: %s", err))
		return fmt.Errorf("failed to parse delete response: %w", err)
	}

	deleted, ok := response["deleted"]
	if !ok {
		logger.Print(logger.ERROR, "Delete response missing 'deleted' field")
	} else {
		logger.Print(logger.INFO, fmt.Sprintf("Successfully deleted %v readOnly events for tenant %s", deleted, tid))
	}

	// Log any failures
	if failures, ok := response["failures"]; ok && failures != nil {
		if failureList, ok := failures.([]interface{}); ok {
			if len(failureList) > 0 {
				logger.Print(logger.ERROR, fmt.Sprintf("Delete had failures: %v", failureList))
			}
		} else {
			logger.Print(logger.ERROR, fmt.Sprintf("Unexpected type for failures: %T - %v", failures, failures))
		}
	}

	return nil
}
