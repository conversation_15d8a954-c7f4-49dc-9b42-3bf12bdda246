package main

import (
	"flag"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/provide"
	"github.com/precize/rds"
	"github.com/precize/transport"
)

// TODO: Error to be sent to Server/UI

func main() {

	var (
		appConfigPath = flag.String("config", "application.yml", "Path to application.yml")
		debug         = flag.Bool("debug", false, "Debug mode")
	)

	flag.Parse()

	logger.InitializeLogs("provider", *debug)

	defaultConf, err := config.InitializeApplicationConfig(*appConfigPath)
	if err != nil {
		return
	} else if defaultConf {
		logger.Print(logger.INFO, "Application config could not be read. Starting with defaults", *appConfigPath)
	}

	if err = elastic.ConnectToElasticSearch(); err != nil {
		return
	}

	transport.SetHttpClient()
	common.InitializeOpenAI()

	if err = rds.ConnectToPostgres(); err != nil {
		return
	}

	provide.StartProvider()
}
