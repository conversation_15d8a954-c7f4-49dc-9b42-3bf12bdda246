package okta

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/okta/okta-sdk-golang/v2/okta"
	"github.com/okta/okta-sdk-golang/v2/okta/query"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
)

const (
	MAX_RECORDS = 1000
)

func ValidateAuth(oktaEnv tenant.OktaEnvironment, tenantID string) (authValidation map[string]bool, err error) {

	authValidation = make(map[string]bool)

	domainUrl, accessToken, _, err := getOktaCreds(tenantID, oktaEnv.Token)
	if err != nil {
		return
	}

	_, client, err := okta.NewClient(
		context.TODO(),
		okta.WithOrgUrl(domainUrl),
		okta.WithAuthorizationMode("Bearer"),
		okta.WithToken(accessToken),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating okta client", []string{tenantID}, err)
		return
	}

	listAppsParams := query.NewQueryParams(
		query.WithLimit(1),
	)

	if _, _, err = client.Application.ListApplications(context.TODO(), listAppsParams); err != nil {
		logger.Print(logger.ERROR, "Got error getting apps", []string{tenantID}, err)
		authValidation[accessToken] = false
		return
	}

	authValidation[accessToken] = true
	return
}

func ProcessOktaData(tenantID string, oktaEnv tenant.OktaEnvironment, oktaAppStartTime, oktaGroupStartTime,
	oktaUserStartTime, oktaDataEndTime time.Time) {

	var firstIteration bool

	if oktaAppStartTime.IsZero() {
		firstIteration = true
	}

	domainUrl, accessToken, _, err := getOktaCreds(tenantID, oktaEnv.Token)
	if err != nil {
		return
	}

	_, client, err := okta.NewClient(
		context.TODO(),
		okta.WithOrgUrl(domainUrl),
		okta.WithAuthorizationMode("Bearer"),
		okta.WithToken(accessToken),
	)
	if err != nil {
		logger.Print(logger.ERROR, "Got error creating okta client", []string{tenantID}, err)
		return
	}

	domain := strings.TrimPrefix(domainUrl, "https://")
	domain = strings.TrimPrefix(domain, "http://")

	defaultDeleteTrueQuery := `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"idpType.keyword":"` + common.OKTA_IDP_TYPE + `"}},{"match":{"deleted":"false"}},{"match":{"domain.keyword":"` + domain + `"}}]}},"script":"ctx._source.deleted = true;"}`
	defaultDeleteTrueQueryIdentities := `{"query":{"bool":{"must":[{"match":{"type.keyword":"OKTA_USER"}},{"match":{"deleted":"false"}},{"match":{"tenantId.keyword":"` + tenantID + `"}},{"match":{"accountId.keyword":"` + domain + `"}}]}},"script":"ctx._source.deleted = true;"}`
	logger.Print(logger.INFO, "Fetching okta apps from "+elastic.DateTime(oktaAppStartTime)+" to "+elastic.DateTime(oktaDataEndTime), []string{tenantID, domain})

	listAppsParams := query.NewQueryParams(
		query.WithLimit(200),
	)

	apps, appsResp, err := client.Application.ListApplications(context.TODO(), listAppsParams)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting apps", []string{tenantID, domain}, err)
		return
	}

	if firstIteration {
		// Every first iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
		logger.Print(logger.INFO, "Identifying deleted okta apps", []string{tenantID})
		elastic.UpdateByQuery(elastic.IDP_APPS_INDEX, defaultDeleteTrueQuery)
	}

	var userApps = make(map[string][]common.IDPUserApp)

	for _, app := range apps {

		if app.IsApplicationInstance() {
			if application, ok := app.(*okta.Application); ok {
				if application.LastUpdated.After(oktaAppStartTime) &&
					(application.LastUpdated.Before(oktaDataEndTime) || application.LastUpdated.Equal(oktaDataEndTime)) {

					if err = insertAppDoc(application, tenantID, domain, oktaDataEndTime); err != nil {
						continue
					}

					if firstIteration {
						fetchUsersOfApp(client, application, tenantID, oktaEnv.ID, userApps, oktaDataEndTime)
					}
				}
			}
		}
	}

	for {

		if appsResp.HasNextPage() {

			if rateLimitReached(appsResp, client, tenantID, oktaEnv.ID) {
				logger.Print(logger.INFO, "Okta apps api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID, domain})
				time.Sleep(1 * time.Minute)
			}

			var paginatedApplications []*okta.Application
			appsResp, err = appsResp.Next(context.TODO(), &paginatedApplications)
			if err != nil {
				logger.Print(logger.ERROR, "Got error getting applications", []string{tenantID, domain}, err)
				break
			}

			for _, application := range paginatedApplications {

				if application.LastUpdated.After(oktaAppStartTime) &&
					(application.LastUpdated.Before(oktaDataEndTime) || application.LastUpdated.Equal(oktaDataEndTime)) {

					if err = insertAppDoc(application, tenantID, domain, oktaDataEndTime); err != nil {
						continue
					}

					if firstIteration {
						fetchUsersOfApp(client, application, tenantID, oktaEnv.ID, userApps, oktaDataEndTime)
					}
				}

			}
		} else {
			break
		}
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.OKTA_APP, oktaDataEndTime)

	logger.Print(logger.INFO, "Fetching okta groups from "+elastic.DateTime(oktaGroupStartTime)+" to "+elastic.DateTime(oktaDataEndTime), []string{tenantID, domain})

	listGroupsParams := query.NewQueryParams(
		query.WithFilter("(lastUpdated gt \""+elastic.DateTime(oktaGroupStartTime)+"\" and lastUpdated le \""+elastic.DateTime(oktaDataEndTime)+"\") or (lastMembershipUpdated gt \""+elastic.DateTime(oktaGroupStartTime)+"\" and lastMembershipUpdated le \""+elastic.DateTime(oktaDataEndTime)+"\")"),
		query.WithLimit(200),
	)

	groups, groupsResp, err := client.Group.ListGroups(context.TODO(), listGroupsParams)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting groups", []string{tenantID, domain}, err)
		return
	}

	if firstIteration {
		// Every first iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
		logger.Print(logger.INFO, "Identifying deleted okta groups", []string{tenantID})
		elastic.UpdateByQuery(elastic.IDP_GROUPS_INDEX, defaultDeleteTrueQuery)
	}

	var userGroups = make(map[string][]common.IDPUserGroup)

	for _, group := range groups {

		if err = insertGroupDoc(group, tenantID, domain, oktaDataEndTime); err != nil {
			continue
		}

		if firstIteration {
			fetchUsersOfGroup(client, group, tenantID, oktaEnv.ID, userGroups, oktaDataEndTime)
		}
	}

	for {

		if groupsResp.HasNextPage() {

			if rateLimitReached(groupsResp, client, tenantID, oktaEnv.ID) {
				logger.Print(logger.INFO, "Okta groups api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID, domain})
				time.Sleep(1 * time.Minute)
			}

			var paginatedGroups []*okta.Group
			groupsResp, err = groupsResp.Next(context.TODO(), &paginatedGroups)
			if err != nil {
				logger.Print(logger.ERROR, "Got error getting groups", []string{tenantID, domain}, err)
				break
			}
			for _, group := range paginatedGroups {

				if err = insertGroupDoc(group, tenantID, domain, oktaDataEndTime); err != nil {
					continue
				}

				if firstIteration {
					fetchUsersOfGroup(client, group, tenantID, oktaEnv.ID, userGroups, oktaDataEndTime)
				}
			}
		} else {
			break
		}
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.OKTA_GROUP, oktaDataEndTime)

	logger.Print(logger.INFO, "Fetching okta users from "+elastic.DateTime(oktaUserStartTime)+" to "+elastic.DateTime(oktaDataEndTime), []string{tenantID, domain})

	listUsersParams := query.NewQueryParams(
		query.WithFilter("(lastUpdated gt \""+elastic.DateTime(oktaUserStartTime)+"\" and lastUpdated le \""+elastic.DateTime(oktaDataEndTime)+"\")"),
		query.WithLimit(200),
	)

	users, usersResp, err := client.User.ListUsers(context.TODO(), listUsersParams)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting users", []string{tenantID, domain}, err)
		return
	}

	if firstIteration {
		// Every first iteration, we set deleted as true so that when all are collected, we get to know which actually got deleted
		logger.Print(logger.INFO, "Identifying deleted okta users and identities", []string{tenantID})
		elastic.UpdateByQuery(elastic.IDENTITIES_INDEX, defaultDeleteTrueQueryIdentities)
		time.Sleep(5 * time.Second)
		elastic.UpdateByQuery(elastic.IDP_USERS_INDEX, defaultDeleteTrueQuery)
	}

	var (
		bulkIdentitiesQuery, bulkExtRscQuery       string
		currentIdentitiesCount, currentExtRscCount int
	)

	for _, user := range users {
		if !firstIteration {
			if userApps[user.Id], err = fetchAppsOfUser(client, user.Id, tenantID, oktaEnv.ID, oktaDataEndTime); err != nil {
				continue
			}
		}

		if err = insertOktaUserDocs(user, userApps[user.Id], userGroups[user.Id], tenantID, domain, oktaDataEndTime, &bulkIdentitiesQuery, &bulkExtRscQuery, &currentIdentitiesCount, &currentExtRscCount); err != nil {
			continue
		}
	}

	for {

		if usersResp.HasNextPage() {

			if rateLimitReached(usersResp, client, tenantID, oktaEnv.ID) {
				logger.Print(logger.INFO, "Okta users api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID, domain})
				time.Sleep(1 * time.Minute)
			}

			var paginatedUsers []*okta.User
			usersResp, err = usersResp.Next(context.TODO(), &paginatedUsers)
			if err != nil {
				logger.Print(logger.ERROR, "Got error getting users", []string{tenantID, domain}, err)
				break
			}

			for _, user := range paginatedUsers {

				if !firstIteration {
					if userApps[user.Id], err = fetchAppsOfUser(client, user.Id, tenantID, oktaEnv.ID, oktaDataEndTime); err != nil {
						continue
					}
				}

				if err = insertOktaUserDocs(user, userApps[user.Id], userGroups[user.Id], tenantID, domain, oktaDataEndTime, &bulkIdentitiesQuery, &bulkExtRscQuery, &currentIdentitiesCount, &currentExtRscCount); err != nil {
					continue
				}
			}
		} else {
			break
		}
	}

	if currentIdentitiesCount > 0 {
		if err = elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, bulkIdentitiesQuery); err != nil {
			return
		}

		logger.Print(logger.INFO, "Identities bulk API Successful for okta users for "+strconv.Itoa(currentIdentitiesCount)+" records", []string{tenantID})
	}

	if currentExtRscCount > 0 {
		if err = elastic.BulkDocumentsAPI(tenantID, elastic.EXTERNAL_CLOUD_RESOURCES_INDEX, bulkExtRscQuery); err != nil {
			return
		}

		logger.Print(logger.INFO, "External cloud resources bulk API Successful for okta users for "+strconv.Itoa(currentExtRscCount)+" records", []string{tenantID})
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.OKTA_USER, oktaDataEndTime)
}
