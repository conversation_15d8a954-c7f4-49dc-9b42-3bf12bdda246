package okta

import (
	"context"
	"encoding/json"
	"time"

	"github.com/okta/okta-sdk-golang/v2/okta"
	"github.com/okta/okta-sdk-golang/v2/okta/query"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

type AppUserEmbedded struct {
	User struct {
		Credentials struct {
			Username string `json:"userName"`
		} `json:"credentials"`
		Profile AppUserProfile `json:"profile"`
	} `json:"user"`
}

type AppUserProfile struct {
	SamlRoles []string `json:"samlRoles"`
}

func fetchUsersOfApp(client *okta.Client, application *okta.Application, tenantID, envID string, userApps map[string][]common.IDPUserApp, insertTime time.Time) {

	listAppUsersParams := query.NewQueryParams(
		query.WithLimit(200),
	)

	appUsers, appUsersResp, err := client.Application.ListApplicationUsers(context.TODO(), application.Id, listAppUsersParams)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting app users", []string{tenantID}, err)
		return
	}

	if rateLimitReached(appUsersResp, client, tenantID, envID) {
		logger.Print(logger.INFO, "Okta app users api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID})
		time.Sleep(1 * time.Minute)
	}

	for _, appUser := range appUsers {

		var appUserProfile AppUserProfile

		marshaledProfile, err := json.Marshal(appUser.Profile)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		if err = json.Unmarshal(marshaledProfile, &appUserProfile); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			continue
		}

		appUserAdditionalData := map[string]any{
			"samlRoles": appUserProfile.SamlRoles,
		}

		appUserAdditionalDataJson, err := json.Marshal(appUserAdditionalData)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		var (
			appUserName string
			appActive   bool
		)

		if appUser.Credentials != nil {
			appUserName = appUser.Credentials.UserName
		}

		if application.Status == "ACTIVE" {
			appActive = true
		}

		userApps[appUser.Id] = append(userApps[appUser.Id], common.IDPUserApp{
			AppID:             application.Id,
			AppName:           application.Name,
			AppLabel:          application.Label,
			AppUsername:       appUserName,
			AppUserAdditional: string(appUserAdditionalDataJson),
			LastUpdated:       elastic.DateTime(insertTime),
			Active:            appActive,
		})
	}

	for {

		if appUsersResp.HasNextPage() {

			if rateLimitReached(appUsersResp, client, tenantID, envID) {
				logger.Print(logger.INFO, "Okta app users api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID})
				time.Sleep(1 * time.Minute)
			}

			var paginatedAppUsers []*okta.AppUser
			appUsersResp, err = appUsersResp.Next(context.TODO(), &paginatedAppUsers)
			if err != nil {
				logger.Print(logger.ERROR, "Got error getting app users", []string{tenantID}, err)
				break
			}

			for _, appUser := range paginatedAppUsers {

				var appUserProfile AppUserProfile

				marshaledProfile, err := json.Marshal(appUser.Profile)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
					continue
				}

				if err = json.Unmarshal(marshaledProfile, &appUserProfile); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
					continue
				}

				appUserAdditionalData := map[string]any{
					"samlRoles": appUserProfile.SamlRoles,
				}

				appUserAdditionalDataJson, err := json.Marshal(appUserAdditionalData)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
					continue
				}

				var (
					appUserName string
					appActive   bool
				)

				if appUser.Credentials != nil {
					appUserName = appUser.Credentials.UserName
				}

				if application.Status == "ACTIVE" {
					appActive = true
				}

				userApps[appUser.Id] = append(userApps[appUser.Id], common.IDPUserApp{
					AppID:             application.Id,
					AppName:           application.Name,
					AppLabel:          application.Label,
					AppUsername:       appUserName,
					AppUserAdditional: string(appUserAdditionalDataJson),
					Active:            appActive,
					LastUpdated:       elastic.DateTime(insertTime),
				})
			}
		} else {
			break
		}
	}
}

func insertAppDoc(app *okta.Application, tenantID, domain string, insertTime time.Time) (err error) {

	appSettings, err := json.Marshal(app.Settings.App)
	if err != nil {
		logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
		// return
	}

	if _, err = elastic.InsertDocument(tenantID, elastic.IDP_APPS_INDEX, common.IDPAppsDoc{
		AppID:           app.Id,
		Label:           app.Label,
		Name:            app.Name,
		CreatedTime:     elastic.DateTime(*app.Created),
		LastUpdatedTime: elastic.DateTime(*app.LastUpdated),
		IDPType:         common.OKTA_IDP_TYPE,
		Deleted:         false,
		SignOnMode:      app.SignOnMode,
		Status:          app.Status,
		Settings:        string(appSettings),
		TenantID:        tenantID,
		Domain:          domain,
		InsertTime:      elastic.DateTime(insertTime),
	}, common.GenerateCombinedHashID(app.Id, domain, tenantID)); err != nil {
		return
	}

	return
}
