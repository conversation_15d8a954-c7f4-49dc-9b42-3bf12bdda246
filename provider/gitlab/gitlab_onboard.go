package gitlab

import (
	"strconv"
	"strings"

	"github.com/precize/common"
	"github.com/precize/logger"
	"github.com/precize/rds/environments"
	"github.com/precize/rds/tenants"
	"github.com/xanzy/go-gitlab"
)

func onboardGitlab(tenantID, accessToken string, gitlabClient *gitlab.Client) error {

	hasEnv, err := environments.ServiceHasEnvironments(tenantID, common.GITLAB_ID_INT)
	if err != nil {
		return err
	}

	if !hasEnv {

		var (
			projectsPage = 1
			projectList  = make([]string, 0)
		)

		for {

			projects, projectsResp, err := gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
				Membership: gitlab.Bool(true),
				ListOptions: gitlab.ListOptions{
					Page:    projectsPage,
					PerPage: 90,
				},
			})
			if err != nil {
				if strings.Contains(err.<PERSON>r(), "401") {
					gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
					projects, projectsResp, err = gitlabClient.Projects.ListProjects(&gitlab.ListProjectsOptions{
						Membership: gitlab.Bool(true),
						ListOptions: gitlab.ListOptions{
							Page:    projectsPage,
							PerPage: 90,
						},
					})
					if err != nil {
						logger.Print(logger.ERROR, "Got error getting projects", []string{tenantID}, err)
						return err
					}
				}
			}

			for _, project := range projects {
				supportedTfExtensions := []string{"*terragrunt.hcl", "*tf", "*tfvars"}
				fetchedTfRelatedFiles := make([]*gitlab.Blob, 0)
				for _, ext := range supportedTfExtensions {
					srchContents, _, err := gitlabClient.Search.BlobsByProject(project.ID, "filename:"+ext, &gitlab.SearchOptions{})
					if err != nil {
						if strings.Contains(err.Error(), "401") {
							gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
							srchContents, _, err = gitlabClient.Search.BlobsByProject(project.ID, "filename:"+ext, &gitlab.SearchOptions{})
						}
						if err != nil {
							logger.Print(logger.INFO, "Error searching terragrunt file: ", []string{strconv.Itoa(project.ID), project.Name})
							continue
						}
					}
					fetchedTfRelatedFiles = append(fetchedTfRelatedFiles, srchContents...)
				}

				if len(fetchedTfRelatedFiles) > 0 {
					projectList = append(projectList, project.Name)
					for _, fetchedTfRelatedFile := range fetchedTfRelatedFiles {
						if strings.HasSuffix(fetchedTfRelatedFile.Filename, "terragrunt.hcl") {
							contentString, _, err := gitlabClient.RepositoryFiles.GetRawFile(project.ID, fetchedTfRelatedFile.Filename, &gitlab.GetRawFileOptions{})
							if err != nil {
								if strings.Contains(err.Error(), "401") {
									gitlabClient = refreshGitlabToken(tenantID, &accessToken, gitlabClient)
									contentString, _, err = gitlabClient.RepositoryFiles.GetRawFile(project.ID, fetchedTfRelatedFile.Filename, &gitlab.GetRawFileOptions{})
									if err != nil {
										logger.Print(logger.ERROR, "Got error getting commit raw content", []string{tenantID, strconv.Itoa(project.ID)}, err)
										continue
									}
								} else {
									logger.Print(logger.ERROR, "Got error fetching raw file for gitlab", []string{tenantID, strconv.Itoa(project.ID), fetchedTfRelatedFile.Filename}, err)
									continue
								}
							}

							logger.Print(logger.INFO, "Processing Terragrunt File", fetchedTfRelatedFile.Filename)
							err = common.ProcessTerragruntFile(string(contentString), tenantID, common.GITLAB, &common.GitlabApiClient{
								GitlabClient: gitlabClient,
								ProjectID:    project.ID,
								FilePath:     fetchedTfRelatedFile.Filename,
								TenantID:     tenantID,
								RepoName:     project.Name,
								AccessToken:  accessToken,
							})
							if err != nil {
								return err
							}
						}
					}
				}

			}

			if projectsResp == nil || projectsResp.NextPage == 0 {
				break
			}

			projectsPage = projectsResp.NextPage
		}

		tenant, err := tenants.GetTenant(tenantID)
		if err != nil {
			return err
		}

		if len(projectList) > 0 {
			if err = environments.InsertEnvironment(environments.EnvironmentsRecord{
				TenantID:     tenantID,
				ServiceID:    common.GITLAB_ID_INT,
				Name:         tenant.CompanyName,
				Repositories: []byte{},
			}); err != nil {
				return err
			}
		}
	}

	return nil
}
