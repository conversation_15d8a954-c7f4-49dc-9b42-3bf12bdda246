package types

type WizCloudResourcesResponse struct {
	Data struct {
		CloudResources struct {
			Nodes    []WizCloudResource `json:"nodes"`
			PageInfo struct {
				HasNextPage bool   `json:"hasNextPage"`
				EndCursor   string `json:"endCursor"`
			} `json:"pageInfo"`
		} `json:"cloudResources"`
	} `json:"data"`
}

type WizCloudResource struct {
	ID                     string  `json:"id"`
	Name                   string  `json:"name"`
	Type                   string  `json:"type"`
	SubscriptionID         *string `json:"subscriptionId"`
	SubscriptionExternalID *string `json:"subscriptionExternalId"`
	GraphEntity            struct {
		ID               string `json:"id"`
		ProviderUniqueID string `json:"providerUniqueId"`
		Name             string `json:"name"`
		Type             string `json:"type"`
		Projects         []struct {
			ID string `json:"id"`
		} `json:"projects"`
		Properties map[string]any `json:"properties"`
		FirstSeen  string         `json:"firstSeen"`
		LastSeen   string         `json:"lastSeen"`
	} `json:"graphEntity"`
}
