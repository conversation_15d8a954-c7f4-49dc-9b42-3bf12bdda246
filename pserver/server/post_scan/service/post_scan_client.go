package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func getTenantSupportUser(tenantID string) (*TenantUser, error) {
	query := fmt.Sprintf(`{
        "query": {
            "bool": {
                "must": [
                    {
                        "wildcard": {
                            "tenantId.keyword": "%s"
                        }
                    }
                ],
                "should": [
                    {
                        "wildcard": {
                            "username.keyword": "support_*"
                        }
                    },
                    {
                        "term": {
                            "username.keyword": "<EMAIL>"
                        }
                    }
                ],
                "minimum_should_match": 1
            }
        },
        "size": 1,
        "from": 0
    }`, tenantID)

	docs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{"tenant_user"}, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query tenant_user index: %v", err)
	}

	for _, doc := range docs {
		var user TenantUser
		docBytes, err := json.Marshal(doc)
		if err != nil {
			continue
		}

		if err := json.Unmarshal(docBytes, &user); err != nil {
			continue
		}

		return &user, nil
	}

	return nil, fmt.Errorf("no support user found for tenant %s", tenantID)
}

func getAuthToken(baseURL, tenantID string, apiReq PostScanRequest) (string, error) {

	user, err := getTenantSupportUser(tenantID)
	if err != nil {
		return "", fmt.Errorf("failed to get tenant support user: %v", err)
	}

	loginReq := LoginRequest{
		Username:   user.Username,
		Password:   "Gov3rnanc3@Pr3c!z3",
		RememberMe: false,
	}

	if apiReq.Username != "" && apiReq.Password != "" {
		loginReq.Username = apiReq.Username
		loginReq.Password = apiReq.Password
	}

	jsonData, err := json.Marshal(loginReq)
	if err != nil {
		return "", fmt.Errorf("failed to marshal login request: %v", err)
	}

	loginURL := baseURL + "/precize/login"

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Post(loginURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to call login API: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read login response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("login API failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		return "", fmt.Errorf("failed to parse login response: %v", err)
	}

	if loginResp.Status != 200 || loginResp.Data.AccessToken == "" {
		return "", fmt.Errorf("login failed: %s", loginResp.Message)
	}

	return loginResp.Data.AccessToken, nil
}

func callPostScanAPI(serviceID string, tenantID string, collectedAt int64, env string, params PostScanParams, apiReq PostScanRequest) error {
	isParamsEmpty := params.KeywordSearch == nil && params.Campaigns == nil && params.MarkDeleted == nil &&
		params.MarkAi == nil && params.Enhancer == nil && params.IncidentWorkflow == nil &&
		params.SaveStatsStore == nil && params.EvaluatePostScanHeroStats == nil &&
		params.IdentityExtraction == nil && params.ExtractAIResources == nil &&
		params.EvaluateVirtualResources == nil && params.Prioritiser == nil &&
		params.EnrichCloudResources == nil && params.IdentityHeroStats == nil

	var keywordSearch, campaigns, markDeleted, markAi, enhancer, incidentWorkflow, saveStatsStore bool
	var evaluatePostScanHeroStats, identityExtraction, extractAIResources, evaluateVirtualResources bool
	var prioritiser, enrichCloudResources, identityHeroStats bool

	if isParamsEmpty {
		keywordSearch = true
		campaigns = true
		markDeleted = true
		markAi = true
		enhancer = true
		incidentWorkflow = true
		saveStatsStore = true
		evaluatePostScanHeroStats = true
		identityExtraction = true
		extractAIResources = true
		evaluateVirtualResources = true
		prioritiser = true
		enrichCloudResources = true
		identityHeroStats = true
	} else {
		keywordSearch = getBoolValue(params.KeywordSearch, false)
		campaigns = getBoolValue(params.Campaigns, false)
		markDeleted = getBoolValue(params.MarkDeleted, false)
		markAi = getBoolValue(params.MarkAi, false)
		enhancer = getBoolValue(params.Enhancer, false)
		incidentWorkflow = getBoolValue(params.IncidentWorkflow, false)
		saveStatsStore = getBoolValue(params.SaveStatsStore, false)
		evaluatePostScanHeroStats = getBoolValue(params.EvaluatePostScanHeroStats, false)
		identityExtraction = getBoolValue(params.IdentityExtraction, false)
		extractAIResources = getBoolValue(params.ExtractAIResources, false)
		evaluateVirtualResources = getBoolValue(params.EvaluateVirtualResources, false)
		prioritiser = getBoolValue(params.Prioritiser, false)
		enrichCloudResources = getBoolValue(params.EnrichCloudResources, false)
		identityHeroStats = getBoolValue(params.IdentityHeroStats, false)
	}

	var baseURL string
	switch env {
	case "qa":
		baseURL = "https://qa.precize.ai"
	case "preprod":
		baseURL = "https://preprod.precize.ai"
	case "prod":
		baseURL = "https://web.precize.ai"
	default:
		return fmt.Errorf("invalid environment: %s", env)
	}

	authToken, err := getAuthToken(baseURL, tenantID, apiReq)
	if err != nil {
		return fmt.Errorf("failed to get auth token: %v", err)
	}

	apiURL := baseURL + "/precize/resources/run/evaluatePostScanHeroStats"
	url := fmt.Sprintf(
		"%s/%s/%s/%d?keywordSearch=%t&campaigns=%t&markDeleted=%t&markAi=%t&enhancer=%t&incidentWorkflow=%t&saveStatsStore=%t&evaluatePostScanHeroStats=%t&identityExtraction=%t&extractAIResources=%t&evaluateVirtualResources=%t&prioritiser=%t&enrichCloudResources=%t&identityHeroStats=%t",
		apiURL, serviceID, tenantID, collectedAt,
		keywordSearch, campaigns, markDeleted, markAi, enhancer, incidentWorkflow, saveStatsStore,
		evaluatePostScanHeroStats, identityExtraction, extractAIResources, evaluateVirtualResources,
		prioritiser, enrichCloudResources, identityHeroStats,
	)

	headers := map[string]string{
		"Authorization": "Bearer " + authToken,
		"Content-Type":  "application/json",
	}

	logger.Print(logger.INFO, "Calling post scan API", []string{tenantID}, serviceID, collectedAt, url)

	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	io.Copy(io.Discard, resp.Body)

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		return fmt.Errorf("API call failed with status: %s", resp.Status)
	}

	logger.Print(logger.INFO, "API call succeeded", []string{tenantID}, serviceID, collectedAt)
	return nil
}

func getBoolValue(ptr *bool, defaultValue bool) bool {
	if ptr == nil {
		return defaultValue
	}
	return *ptr
}
