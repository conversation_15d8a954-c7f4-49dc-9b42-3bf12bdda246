package service

var ServiceIDToName = map[int]string{
	1000: "aws",
	2000: "azure",
	3000: "gcp",
}

type PostScanRequest struct {
	TenantID    string         `json:"tenantId"`
	ServiceID   string         `json:"serviceId,omitempty"`
	Environment string         `json:"environment"`
	Params      PostScanParams `json:"params,omitempty"`
	Username    string         `json:"username,omitempty"`
	Password    string         `json:"password,omitempty"`
}

type PostScanParams struct {
	KeywordSearch             *bool `json:"keywordSearch,omitempty"`
	Campaigns                 *bool `json:"campaigns,omitempty"`
	MarkDeleted               *bool `json:"markDeleted,omitempty"`
	MarkAi                    *bool `json:"markAi,omitempty"`
	Enhancer                  *bool `json:"enhancer,omitempty"`
	IncidentWorkflow          *bool `json:"incidentWorkflow,omitempty"`
	SaveStatsStore            *bool `json:"saveStatsStore,omitempty"`
	EvaluatePostScanHeroStats *bool `json:"evaluatePostScanHeroStats,omitempty"`
	IdentityExtraction        *bool `json:"identityExtraction,omitempty"`
	ExtractAIResources        *bool `json:"extractAIResources,omitempty"`
	EvaluateVirtualResources  *bool `json:"evaluateVirtualResources,omitempty"`
	Prioritiser               *bool `json:"prioritiser,omitempty"`
	EnrichCloudResources      *bool `json:"enrichCloudResources,omitempty"`
	IdentityHeroStats         *bool `json:"identityHeroStats,omitempty"`
}

type TenantUser struct {
	ID       string `json:"id"`
	TenantID string `json:"tenantId"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	Username string `json:"username"`
	Password string `json:"password"`
	Type     int    `json:"type"`
}

type LoginRequest struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	RememberMe bool   `json:"rememberMe"`
}

type LoginResponse struct {
	Message string `json:"message"`
	Status  int    `json:"status"`
	Error   string `json:"error"`
	Data    struct {
		TenantID          string   `json:"tenantId"`
		CompanyName       string   `json:"companyName"`
		Role              string   `json:"role"`
		UserID            string   `json:"userId"`
		Username          string   `json:"username"`
		Email             string   `json:"email"`
		Type              int      `json:"type"`
		Name              string   `json:"name"`
		AccessToken       string   `json:"access_token"`
		OnboardedServices []string `json:"onboardedServices"`
	} `json:"data"`
}
