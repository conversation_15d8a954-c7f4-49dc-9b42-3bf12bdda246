package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/precize/common/weaviate"
	"github.com/precize/logger"
	"github.com/precize/pserver/server/generic/model"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/filters"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/graphql"
)

func ParseJSON(r *http.Request, v interface{}) error {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to read request body: %v", err)
		return fmt.Errorf("failed to read request body: %w", err)
	}

	if err := json.Unmarshal(body, v); err != nil {
		logger.Print(logger.ERROR, "Failed to parse request JSON: %v", err)
		return fmt.Errorf("invalid JSON format: %w", err)
	}
	return nil
}

func WriteResponse(w http.ResponseWriter, status int, response model.Response) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.Write<PERSON>eader(status)
	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Print(logger.ERROR, "Failed to write response: %v", err)
	}
}

func WriteError(w http.ResponseWriter, status int, message string) {
	WriteResponse(w, status, model.Response{
		Success: false,
		Message: message,
	})
}

func IsNotFoundError(err error) bool {
	return strings.Contains(err.Error(), "404") || strings.Contains(strings.ToLower(err.Error()), "not found")
}

func Float32Ptr(v float32) *float32 {
	return &v
}

func BuildWhereFilter(filter weaviate.SearchFilter) *filters.WhereBuilder {
	whereFilter := filters.Where().WithPath([]string{filter.Path})

	switch filter.Operator {
	case "Equal":
		return whereFilter.WithOperator(filters.Equal).WithValueString(filter.Value)
	case "NotEqual":
		return whereFilter.WithOperator(filters.NotEqual).WithValueString(filter.Value)
	case "GreaterThan":
		return whereFilter.WithOperator(filters.GreaterThan).WithValueString(filter.Value)
	case "GreaterThanEqual":
		return whereFilter.WithOperator(filters.GreaterThanEqual).WithValueString(filter.Value)
	case "LessThan":
		return whereFilter.WithOperator(filters.LessThan).WithValueString(filter.Value)
	case "LessThanEqual":
		return whereFilter.WithOperator(filters.LessThanEqual).WithValueString(filter.Value)
	case "Like":
		return whereFilter.WithOperator(filters.Like).WithValueString(filter.Value)
	case "WithinGeoRange":
		return whereFilter.WithOperator(filters.WithinGeoRange).WithValueString(filter.Value)
	default:
		return whereFilter.WithOperator(filters.Equal).WithValueString(filter.Value)
	}
}

func BuildFieldString(f graphql.Field) string {

	if len(f.Fields) == 0 {
		return f.Name
	}

	childStrs := []string{}
	for _, child := range f.Fields {
		childStrs = append(childStrs, BuildFieldString(child))
	}

	return fmt.Sprintf("%s { %s }", f.Name, strings.Join(childStrs, " "))
}
