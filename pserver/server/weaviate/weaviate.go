package weaviate

import (
	"context"
	"encoding/base64"
	"fmt"

	"github.com/precize/common"
	"github.com/precize/config"
	"github.com/precize/logger"
	"github.com/weaviate/weaviate-go-client/v5/weaviate"
)

var weaviateClient *weaviate.Client

// Before initializing weaviate client, the weaviate container is running with correct config variable
// sudo docker run -d -p 18001:18001 -p 50051:50051 -v /app/weaviate_data:/var/lib/weaviate -e QUERY_DEFAULTS_LIMIT=25 -e AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true -e PERSISTENCE_DATA_PATH=/var/lib/weaviate -e ENABLE_API_BASED_MODULES=true -e ENABLE_MODULES=text2vec-ollama,generative-ollama,text2vec-openai -e CLUSTER_HOSTNAME=node1 -e MODULES_CLIENT_TIMEOUT=2m cr.weaviate.io/semitechnologies/weaviate:1.33.4 --host 0.0.0.0 --port 18001 --scheme http

func InitializeWeaviateClient() error {
	logger.Print(logger.INFO, "Initializing Weaviate client...")

	host := fmt.Sprintf("%s:%d", config.AppConfig.Spring.Vector.Host, config.AppConfig.Spring.Vector.Port)
	if host == "" {
		host = "localhost:8080"
	}
	scheme := config.AppConfig.Weaviate.Scheme
	if scheme == "" {
		scheme = "http"
	}

	azureOpenAIAPIKey := ""

	decodedAPIKey, err := base64.StdEncoding.DecodeString(config.AppConfig.AzureOpenAI.TextEmbedding3Small.APIKey)
	if err == nil {
		decryptedKey, err := common.DecryptTextAES(decodedAPIKey)
		if err == nil {
			azureOpenAIAPIKey = string(decryptedKey)
		} else {
			azureOpenAIAPIKey = config.AppConfig.AzureOpenAI.TextEmbedding3Small.APIKey
		}
	} else {
		logger.Print(logger.ERROR, "Failed to decode base64", err)
		azureOpenAIAPIKey = config.AppConfig.AzureOpenAI.TextEmbedding3Small.APIKey
	}

	cfg := weaviate.Config{
		Host:   host,
		Scheme: scheme,
		Headers: map[string]string{
			"X-Azure-Api-Key":       azureOpenAIAPIKey,
			"X-Azure-Deployment-Id": config.AppConfig.AzureOpenAI.TextEmbedding3Small.DeploymentId,
			"X-Azure-Resource-Name": config.AppConfig.AzureOpenAI.TextEmbedding3Small.ResourceName,
		},
	}

	client, err := weaviate.NewClient(cfg)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to init Weaviate client %v", err)
		return err
	}

	if client == nil {
		return nil
	}

	ctx := context.Background()
	live, err := client.Misc().LiveChecker().Do(ctx)
	if err != nil {
		logger.Print(logger.ERROR, "Weaviate not reachable %v", err)
		return err
	}

	logger.Print(logger.INFO, "Weaviate connection status ", live)
	weaviateClient = client
	return nil
}

func GetClient() *weaviate.Client {
	return weaviateClient
}
